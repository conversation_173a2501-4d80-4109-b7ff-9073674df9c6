[INFO] Scanning for projects...
[INFO] 
[INFO] ----------------------< com.br.sasw:esocial-novo >----------------------
[INFO] Building esocial-novo 0.0.1-SNAPSHOT
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-clean-plugin/3.4.1/maven-clean-plugin-3.4.1.pom
Progress (1): 1.4/5.6 kB
Progress (1): 2.8/5.6 kB
Progress (1): 4.1/5.6 kB
Progress (1): 5.5/5.6 kB
Progress (1): 5.6 kB    
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-clean-plugin/3.4.1/maven-clean-plugin-3.4.1.pom (5.6 kB at 18 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-plugins/43/maven-plugins-43.pom
Progress (1): 1.4/7.5 kB
Progress (1): 2.8/7.5 kB
Progress (1): 4.1/7.5 kB
Progress (1): 5.5/7.5 kB
Progress (1): 6.9/7.5 kB
Progress (1): 7.5 kB    
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-plugins/43/maven-plugins-43.pom (7.5 kB at 233 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/43/maven-parent-43.pom
Progress (1): 1.4/50 kB
Progress (1): 2.8/50 kB
Progress (1): 4.1/50 kB
Progress (1): 5.5/50 kB
Progress (1): 6.9/50 kB
Progress (1): 8.3/50 kB
Progress (1): 9.7/50 kB
Progress (1): 11/50 kB 
Progress (1): 12/50 kB
Progress (1): 14/50 kB
Progress (1): 15/50 kB
Progress (1): 17/50 kB
Progress (1): 18/50 kB
Progress (1): 19/50 kB
Progress (1): 21/50 kB
Progress (1): 22/50 kB
Progress (1): 23/50 kB
Progress (1): 25/50 kB
Progress (1): 26/50 kB
Progress (1): 28/50 kB
Progress (1): 29/50 kB
Progress (1): 30/50 kB
Progress (1): 32/50 kB
Progress (1): 33/50 kB
Progress (1): 34/50 kB
Progress (1): 36/50 kB
Progress (1): 37/50 kB
Progress (1): 39/50 kB
Progress (1): 40/50 kB
Progress (1): 41/50 kB
Progress (1): 43/50 kB
Progress (1): 44/50 kB
Progress (1): 46/50 kB
Progress (1): 47/50 kB
Progress (1): 48/50 kB
Progress (1): 50/50 kB
Progress (1): 50 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/43/maven-parent-43.pom (50 kB at 1.1 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/junit/junit-bom/5.10.3/junit-bom-5.10.3.pom
Progress (1): 5.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/junit/junit-bom/5.10.3/junit-bom-5.10.3.pom (5.6 kB at 377 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-clean-plugin/3.4.1/maven-clean-plugin-3.4.1.jar
Progress (1): 16/36 kB
Progress (1): 32/36 kB
Progress (1): 36 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-clean-plugin/3.4.1/maven-clean-plugin-3.4.1.jar (36 kB at 1.2 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/mojo/jaxb2-maven-plugin/3.1.0/jaxb2-maven-plugin-3.1.0.pom
Progress (1): 16/20 kB
Progress (1): 20 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/mojo/jaxb2-maven-plugin/3.1.0/jaxb2-maven-plugin-3.1.0.pom (20 kB at 635 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/mojo/mojo-parent/67/mojo-parent-67.pom
Progress (1): 16/35 kB
Progress (1): 33/35 kB
Progress (1): 35 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/mojo/mojo-parent/67/mojo-parent-67.pom (35 kB at 245 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/junit/junit-bom/5.8.2/junit-bom-5.8.2.pom
Progress (1): 5.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/junit/junit-bom/5.8.2/junit-bom-5.8.2.pom (5.6 kB at 313 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/mojo/jaxb2-maven-plugin/3.1.0/jaxb2-maven-plugin-3.1.0.jar
Progress (1): 16/177 kB
Progress (1): 33/177 kB
Progress (1): 49/177 kB
Progress (1): 66/177 kB
Progress (1): 82/177 kB
Progress (1): 98/177 kB
Progress (1): 115/177 kB
Progress (1): 131/177 kB
Progress (1): 147/177 kB
Progress (1): 164/177 kB
Progress (1): 177 kB    
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/mojo/jaxb2-maven-plugin/3.1.0/jaxb2-maven-plugin-3.1.0.jar (177 kB at 4.0 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/mojo/build-helper-maven-plugin/3.3.0/build-helper-maven-plugin-3.3.0.pom
Progress (1): 7.4 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/mojo/build-helper-maven-plugin/3.3.0/build-helper-maven-plugin-3.3.0.pom (7.4 kB at 239 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/mojo/mojo-parent/65/mojo-parent-65.pom
Progress (1): 16/35 kB
Progress (1): 33/35 kB
Progress (1): 35 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/mojo/mojo-parent/65/mojo-parent-65.pom (35 kB at 1.1 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/junit/junit-bom/5.8.1/junit-bom-5.8.1.pom
Progress (1): 5.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/junit/junit-bom/5.8.1/junit-bom-5.8.1.pom (5.6 kB at 176 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/mojo/build-helper-maven-plugin/3.3.0/build-helper-maven-plugin-3.3.0.jar
Progress (1): 16/68 kB
Progress (1): 33/68 kB
Progress (1): 48/68 kB
Progress (1): 64/68 kB
Progress (1): 68 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/mojo/build-helper-maven-plugin/3.3.0/build-helper-maven-plugin-3.3.0.jar (68 kB at 1.8 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-resources-plugin/3.3.1/maven-resources-plugin-3.3.1.pom
Progress (1): 8.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-resources-plugin/3.3.1/maven-resources-plugin-3.3.1.pom (8.2 kB at 177 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-plugins/39/maven-plugins-39.pom
Progress (1): 8.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-plugins/39/maven-plugins-39.pom (8.1 kB at 253 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/39/maven-parent-39.pom
Progress (1): 16/48 kB
Progress (1): 33/48 kB
Progress (1): 48 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/39/maven-parent-39.pom (48 kB at 1.5 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-resources-plugin/3.3.1/maven-resources-plugin-3.3.1.jar
Progress (1): 16/31 kB
Progress (1): 31 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-resources-plugin/3.3.1/maven-resources-plugin-3.3.1.jar (31 kB at 1.0 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-compiler-plugin/3.13.0/maven-compiler-plugin-3.13.0.pom
Progress (1): 10 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-compiler-plugin/3.13.0/maven-compiler-plugin-3.13.0.pom (10 kB at 337 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-plugins/41/maven-plugins-41.pom
Progress (1): 7.4 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-plugins/41/maven-plugins-41.pom (7.4 kB at 237 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/41/maven-parent-41.pom
Progress (1): 16/50 kB
Progress (1): 32/50 kB
Progress (1): 49/50 kB
Progress (1): 50 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/41/maven-parent-41.pom (50 kB at 1.6 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/apache/31/apache-31.pom
Progress (1): 16/24 kB
Progress (1): 24 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/apache/31/apache-31.pom (24 kB at 759 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-compiler-plugin/3.13.0/maven-compiler-plugin-3.13.0.jar
Progress (1): 16/83 kB
Progress (1): 33/83 kB
Progress (1): 49/83 kB
Progress (1): 66/83 kB
Progress (1): 82/83 kB
Progress (1): 83 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-compiler-plugin/3.13.0/maven-compiler-plugin-3.13.0.jar (83 kB at 1.8 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-surefire-plugin/3.5.3/maven-surefire-plugin-3.5.3.pom
Progress (1): 5.5 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-surefire-plugin/3.5.3/maven-surefire-plugin-3.5.3.pom (5.5 kB at 177 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire/3.5.3/surefire-3.5.3.pom
Progress (1): 16/20 kB
Progress (1): 20 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire/3.5.3/surefire-3.5.3.pom (20 kB at 641 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/44/maven-parent-44.pom
Progress (1): 16/52 kB
Progress (1): 33/52 kB
Progress (1): 49/52 kB
Progress (1): 52 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/44/maven-parent-44.pom (52 kB at 1.6 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/apache/34/apache-34.pom
Progress (1): 16/24 kB
Progress (1): 24 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/apache/34/apache-34.pom (24 kB at 782 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/junit/junit-bom/5.12.1/junit-bom-5.12.1.pom
Progress (1): 5.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/junit/junit-bom/5.12.1/junit-bom-5.12.1.pom (5.6 kB at 353 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-surefire-plugin/3.5.3/maven-surefire-plugin-3.5.3.jar
Progress (1): 16/46 kB
Progress (1): 33/46 kB
Progress (1): 46 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-surefire-plugin/3.5.3/maven-surefire-plugin-3.5.3.jar (46 kB at 1.4 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-jar-plugin/3.4.2/maven-jar-plugin-3.4.2.pom
Progress (1): 7.7 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-jar-plugin/3.4.2/maven-jar-plugin-3.4.2.pom (7.7 kB at 248 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-plugins/42/maven-plugins-42.pom
Progress (1): 7.7 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-plugins/42/maven-plugins-42.pom (7.7 kB at 513 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/42/maven-parent-42.pom
Progress (1): 16/50 kB
Progress (1): 33/50 kB
Progress (1): 49/50 kB
Progress (1): 50 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/42/maven-parent-42.pom (50 kB at 1.6 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/apache/32/apache-32.pom
Progress (1): 16/24 kB
Progress (1): 24 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/apache/32/apache-32.pom (24 kB at 780 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/junit/junit-bom/5.10.2/junit-bom-5.10.2.pom
Progress (1): 5.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/junit/junit-bom/5.10.2/junit-bom-5.10.2.pom (5.6 kB at 177 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-jar-plugin/3.4.2/maven-jar-plugin-3.4.2.jar
Progress (1): 16/34 kB
Progress (1): 32/34 kB
Progress (1): 34 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-jar-plugin/3.4.2/maven-jar-plugin-3.4.2.jar (34 kB at 1.1 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter/3.4.5/spring-boot-starter-3.4.5.pom
Progress (1): 3.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter/3.4.5/spring-boot-starter-3.4.5.pom (3.0 kB at 203 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot/3.4.5/spring-boot-3.4.5.pom
Progress (1): 2.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot/3.4.5/spring-boot-3.4.5.pom (2.2 kB at 47 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/spring-core/6.2.6/spring-core-6.2.6.pom
Progress (1): 2.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/spring-core/6.2.6/spring-core-6.2.6.pom (2.0 kB at 92 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/spring-jcl/6.2.6/spring-jcl-6.2.6.pom
Progress (1): 1.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/spring-jcl/6.2.6/spring-jcl-6.2.6.pom (1.8 kB at 60 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/spring-context/6.2.6/spring-context-6.2.6.pom
Progress (1): 2.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/spring-context/6.2.6/spring-context-6.2.6.pom (2.8 kB at 90 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/spring-aop/6.2.6/spring-aop-6.2.6.pom
Progress (1): 2.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/spring-aop/6.2.6/spring-aop-6.2.6.pom (2.2 kB at 71 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/spring-beans/6.2.6/spring-beans-6.2.6.pom
Progress (1): 2.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/spring-beans/6.2.6/spring-beans-6.2.6.pom (2.0 kB at 55 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/spring-expression/6.2.6/spring-expression-6.2.6.pom
Progress (1): 2.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/spring-expression/6.2.6/spring-expression-6.2.6.pom (2.1 kB at 83 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/io/micrometer/micrometer-observation/1.14.6/micrometer-observation-1.14.6.pom
Progress (1): 3.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/io/micrometer/micrometer-observation/1.14.6/micrometer-observation-1.14.6.pom (3.8 kB at 124 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/io/micrometer/micrometer-commons/1.14.6/micrometer-commons-1.14.6.pom
Progress (1): 3.4 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/io/micrometer/micrometer-commons/1.14.6/micrometer-commons-1.14.6.pom (3.4 kB at 106 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-autoconfigure/3.4.5/spring-boot-autoconfigure-3.4.5.pom
Progress (1): 2.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-autoconfigure/3.4.5/spring-boot-autoconfigure-3.4.5.pom (2.1 kB at 129 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter-logging/3.4.5/spring-boot-starter-logging-3.4.5.pom
Progress (1): 2.5 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter-logging/3.4.5/spring-boot-starter-logging-3.4.5.pom (2.5 kB at 79 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.pom
Progress (1): 13 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.pom (13 kB at 399 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/ch/qos/logback/logback-parent/1.5.18/logback-parent-1.5.18.pom
Progress (1): 16/19 kB
Progress (1): 19 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/ch/qos/logback/logback-parent/1.5.18/logback-parent-1.5.18.pom (19 kB at 601 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.pom
Progress (1): 9.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.pom (9.1 kB at 605 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.pom
Progress (1): 2.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.pom (2.8 kB at 91 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/slf4j/slf4j-parent/2.0.17/slf4j-parent-2.0.17.pom
Progress (1): 13 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/slf4j/slf4j-parent/2.0.17/slf4j-parent-2.0.17.pom (13 kB at 431 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/slf4j/slf4j-bom/2.0.17/slf4j-bom-2.0.17.pom
Progress (1): 7.3 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/slf4j/slf4j-bom/2.0.17/slf4j-bom-2.0.17.pom (7.3 kB at 236 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.pom
Progress (1): 5.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.pom (5.0 kB at 315 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/logging/log4j/log4j/2.24.3/log4j-2.24.3.pom
Progress (1): 16/35 kB
Progress (1): 33/35 kB
Progress (1): 35 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/logging/log4j/log4j/2.24.3/log4j-2.24.3.pom (35 kB at 1.1 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/groovy/groovy-bom/4.0.22/groovy-bom-4.0.22.pom
Progress (1): 16/27 kB
Progress (1): 27 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/groovy/groovy-bom/4.0.22/groovy-bom-4.0.22.pom (27 kB at 1.7 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/jackson-bom/2.17.2/jackson-bom-2.17.2.pom
Progress (1): 16/19 kB
Progress (1): 19 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/jackson-bom/2.17.2/jackson-bom-2.17.2.pom (19 kB at 603 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/jackson-parent/2.17/jackson-parent-2.17.pom
Progress (1): 6.5 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/jackson-parent/2.17/jackson-parent-2.17.pom (6.5 kB at 210 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/fasterxml/oss-parent/58/oss-parent-58.pom
Progress (1): 16/24 kB
Progress (1): 24 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/fasterxml/oss-parent/58/oss-parent-58.pom (24 kB at 739 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/jakarta/platform/jakarta.jakartaee-bom/9.1.0/jakarta.jakartaee-bom-9.1.0.pom
Progress (1): 9.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/jakarta/platform/jakarta.jakartaee-bom/9.1.0/jakarta.jakartaee-bom-9.1.0.pom (9.6 kB at 308 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/jakarta/platform/jakartaee-api-parent/9.1.0/jakartaee-api-parent-9.1.0.pom
Progress (1): 15 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/jakarta/platform/jakartaee-api-parent/9.1.0/jakartaee-api-parent-9.1.0.pom (15 kB at 464 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/eclipse/ee4j/project/1.0.7/project-1.0.7.pom
Progress (1): 14 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/eclipse/ee4j/project/1.0.7/project-1.0.7.pom (14 kB at 457 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/mockito/mockito-bom/4.11.0/mockito-bom-4.11.0.pom
Progress (1): 3.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/mockito/mockito-bom/4.11.0/mockito-bom-4.11.0.pom (3.2 kB at 102 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/spring-framework-bom/5.3.39/spring-framework-bom-5.3.39.pom
Progress (1): 5.7 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/spring-framework-bom/5.3.39/spring-framework-bom-5.3.39.pom (5.7 kB at 236 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.pom
Progress (1): 4.4 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.pom (4.4 kB at 138 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.pom
Progress (1): 1.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.pom (1.1 kB at 73 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.pom
Progress (1): 16 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.pom (16 kB at 494 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/yaml/snakeyaml/2.3/snakeyaml-2.3.pom
Progress (1): 16/22 kB
Progress (1): 22 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/yaml/snakeyaml/2.3/snakeyaml-2.3.pom (22 kB at 700 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter-web/3.4.5/spring-boot-starter-web-3.4.5.pom
Progress (1): 2.9 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter-web/3.4.5/spring-boot-starter-web-3.4.5.pom (2.9 kB at 95 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter-json/3.4.5/spring-boot-starter-json-3.4.5.pom
Progress (1): 3.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter-json/3.4.5/spring-boot-starter-json-3.4.5.pom (3.1 kB at 96 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/spring-web/6.2.6/spring-web-6.2.6.pom
Progress (1): 2.4 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/spring-web/6.2.6/spring-web-6.2.6.pom (2.4 kB at 75 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.18.3/jackson-databind-2.18.3.pom
Progress (1): 16/21 kB
Progress (1): 21 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.18.3/jackson-databind-2.18.3.pom (21 kB at 677 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/jackson-base/2.18.3/jackson-base-2.18.3.pom
Progress (1): 12 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/jackson-base/2.18.3/jackson-base-2.18.3.pom (12 kB at 384 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.18.3/jackson-annotations-2.18.3.pom
Progress (1): 7.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.18.3/jackson-annotations-2.18.3.pom (7.1 kB at 229 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/core/jackson-core/2.18.3/jackson-core-2.18.3.pom
Progress (1): 10 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/core/jackson-core/2.18.3/jackson-core-2.18.3.pom (10 kB at 323 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.18.3/jackson-datatype-jdk8-2.18.3.pom
Progress (1): 2.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.18.3/jackson-datatype-jdk8-2.18.3.pom (2.6 kB at 81 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/module/jackson-modules-java8/2.18.3/jackson-modules-java8-2.18.3.pom
Progress (1): 3.3 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/module/jackson-modules-java8/2.18.3/jackson-modules-java8-2.18.3.pom (3.3 kB at 106 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.18.3/jackson-datatype-jsr310-2.18.3.pom
Progress (1): 4.9 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.18.3/jackson-datatype-jsr310-2.18.3.pom (4.9 kB at 307 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/module/jackson-module-parameter-names/2.18.3/jackson-module-parameter-names-2.18.3.pom
Progress (1): 4.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/module/jackson-module-parameter-names/2.18.3/jackson-module-parameter-names-2.18.3.pom (4.2 kB at 137 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter-tomcat/3.4.5/spring-boot-starter-tomcat-3.4.5.pom
Progress (1): 3.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter-tomcat/3.4.5/spring-boot-starter-tomcat-3.4.5.pom (3.1 kB at 87 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/tomcat/embed/tomcat-embed-core/10.1.40/tomcat-embed-core-10.1.40.pom
Progress (1): 1.7 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/tomcat/embed/tomcat-embed-core/10.1.40/tomcat-embed-core-10.1.40.pom (1.7 kB at 109 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/tomcat/embed/tomcat-embed-el/10.1.40/tomcat-embed-el-10.1.40.pom
Progress (1): 1.5 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/tomcat/embed/tomcat-embed-el/10.1.40/tomcat-embed-el-10.1.40.pom (1.5 kB at 49 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.40/tomcat-embed-websocket-10.1.40.pom
Progress (1): 1.7 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.40/tomcat-embed-websocket-10.1.40.pom (1.7 kB at 54 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/spring-webmvc/6.2.6/spring-webmvc-6.2.6.pom
Progress (1): 3.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/spring-webmvc/6.2.6/spring-webmvc-6.2.6.pom (3.0 kB at 197 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter-web-services/3.4.5/spring-boot-starter-web-services-3.4.5.pom
Progress (1): 2.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter-web-services/3.4.5/spring-boot-starter-web-services-3.4.5.pom (2.8 kB at 92 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/xml/messaging/saaj/saaj-impl/3.0.4/saaj-impl-3.0.4.pom
Progress (1): 11 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/xml/messaging/saaj/saaj-impl/3.0.4/saaj-impl-3.0.4.pom (11 kB at 475 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/xml/messaging/saaj/metro-saaj/3.0.4/metro-saaj-3.0.4.pom
Progress (1): 16/20 kB
Progress (1): 20 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/xml/messaging/saaj/metro-saaj/3.0.4/metro-saaj-3.0.4.pom (20 kB at 1.2 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/jakarta/xml/soap/jakarta.xml.soap-api/3.0.2/jakarta.xml.soap-api-3.0.2.pom
Progress (1): 16/22 kB
Progress (1): 22 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/jakarta/xml/soap/jakarta.xml.soap-api/3.0.2/jakarta.xml.soap-api-3.0.2.pom (22 kB at 690 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.pom
Progress (1): 16/19 kB
Progress (1): 19 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.pom (19 kB at 597 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/jvnet/staxex/stax-ex/2.1.0/stax-ex-2.1.0.pom
Progress (1): 16 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/jvnet/staxex/stax-ex/2.1.0/stax-ex-2.1.0.pom (16 kB at 504 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.pom
Progress (1): 4.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.pom (4.0 kB at 129 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/eclipse/angus/angus-activation-project/2.0.2/angus-activation-project-2.0.2.pom
Progress (1): 16/21 kB
Progress (1): 21 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/eclipse/angus/angus-activation-project/2.0.2/angus-activation-project-2.0.2.pom (21 kB at 653 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/jakarta/xml/ws/jakarta.xml.ws-api/4.0.2/jakarta.xml.ws-api-4.0.2.pom
Progress (1): 16/19 kB
Progress (1): 19 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/jakarta/xml/ws/jakarta.xml.ws-api/4.0.2/jakarta.xml.ws-api-4.0.2.pom (19 kB at 620 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.pom
Progress (1): 13 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.pom (13 kB at 410 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/jakarta/xml/bind/jakarta.xml.bind-api-parent/4.0.2/jakarta.xml.bind-api-parent-4.0.2.pom
Progress (1): 9.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/jakarta/xml/bind/jakarta.xml.bind-api-parent/4.0.2/jakarta.xml.bind-api-parent-4.0.2.pom (9.1 kB at 295 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/spring-oxm/6.2.6/spring-oxm-6.2.6.pom
Progress (1): 2.4 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/spring-oxm/6.2.6/spring-oxm-6.2.6.pom (2.4 kB at 76 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/ws/spring-ws-core/4.0.13/spring-ws-core-4.0.13.pom
Progress (1): 3.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/ws/spring-ws-core/4.0.13/spring-ws-core-4.0.13.pom (3.2 kB at 103 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/ws/spring-xml/4.0.13/spring-xml-4.0.13.pom
Progress (1): 2.5 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/ws/spring-xml/4.0.13/spring-xml-4.0.13.pom (2.5 kB at 79 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.pom
Progress (1): 11 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.pom (11 kB at 339 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/mvn/jaxb-runtime-parent/4.0.5/jaxb-runtime-parent-4.0.5.pom
Progress (1): 1.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/mvn/jaxb-runtime-parent/4.0.5/jaxb-runtime-parent-4.0.5.pom (1.2 kB at 74 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/mvn/jaxb-parent/4.0.5/jaxb-parent-4.0.5.pom
Progress (1): 16/35 kB
Progress (1): 33/35 kB
Progress (1): 35 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/mvn/jaxb-parent/4.0.5/jaxb-parent-4.0.5.pom (35 kB at 1.1 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-bom-ext/4.0.5/jaxb-bom-ext-4.0.5.pom
Progress (1): 3.5 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-bom-ext/4.0.5/jaxb-bom-ext-4.0.5.pom (3.5 kB at 112 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/glassfish/jaxb/jaxb-core/4.0.5/jaxb-core-4.0.5.pom
Progress (1): 3.7 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/glassfish/jaxb/jaxb-core/4.0.5/jaxb-core-4.0.5.pom (3.7 kB at 220 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/glassfish/jaxb/txw2/4.0.5/txw2-4.0.5.pom
Progress (1): 1.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/glassfish/jaxb/txw2/4.0.5/txw2-4.0.5.pom (1.8 kB at 56 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/mvn/jaxb-txw-parent/4.0.5/jaxb-txw-parent-4.0.5.pom
Progress (1): 1.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/mvn/jaxb-txw-parent/4.0.5/jaxb-txw-parent-4.0.5.pom (1.2 kB at 37 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.pom
Progress (1): 1.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.pom (1.6 kB at 52 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/istack/istack-commons/4.1.2/istack-commons-4.1.2.pom
Progress (1): 16/26 kB
Progress (1): 26 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/istack/istack-commons/4.1.2/istack-commons-4.1.2.pom (26 kB at 1.6 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.pom
Progress (1): 6.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.pom (6.6 kB at 213 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/httpcomponents-client/4.5.13/httpcomponents-client-4.5.13.pom
Progress (1): 16 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/httpcomponents-client/4.5.13/httpcomponents-client-4.5.13.pom (16 kB at 528 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/httpcomponents-parent/11/httpcomponents-parent-11.pom
Progress (1): 16/35 kB
Progress (1): 32/35 kB
Progress (1): 35 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/httpcomponents-parent/11/httpcomponents-parent-11.pom (35 kB at 1.4 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/apache/21/apache-21.pom
Progress (1): 16/17 kB
Progress (1): 17 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/apache/21/apache-21.pom (17 kB at 535 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.pom
Progress (1): 5.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.pom (5.0 kB at 160 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/httpcomponents-core/4.4.16/httpcomponents-core-4.4.16.pom
Progress (1): 12 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/httpcomponents-core/4.4.16/httpcomponents-core-4.4.16.pom (12 kB at 373 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/commons-logging/commons-logging/1.2/commons-logging-1.2.pom
Progress (1): 16/19 kB
Progress (1): 19 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/commons-logging/commons-logging/1.2/commons-logging-1.2.pom (19 kB at 620 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-parent/34/commons-parent-34.pom
Progress (1): 16/56 kB
Progress (1): 33/56 kB
Progress (1): 49/56 kB
Progress (1): 56 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-parent/34/commons-parent-34.pom (56 kB at 1.8 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/apache/13/apache-13.pom
Progress (1): 14 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/apache/13/apache-13.pom (14 kB at 388 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/commons-codec/commons-codec/1.17.2/commons-codec-1.17.2.pom
Progress (1): 16/18 kB
Progress (1): 18 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/commons-codec/commons-codec/1.17.2/commons-codec-1.17.2.pom (18 kB at 707 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-parent/78/commons-parent-78.pom
Progress (1): 15/78 kB
Progress (1): 31/78 kB
Progress (1): 48/78 kB
Progress (1): 64/78 kB
Progress (1): 78 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-parent/78/commons-parent-78.pom (78 kB at 2.5 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/junit/junit-bom/5.11.2/junit-bom-5.11.2.pom
Progress (1): 5.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/junit/junit-bom/5.11.2/junit-bom-5.11.2.pom (5.6 kB at 353 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-impl/4.0.5/jaxb-impl-4.0.5.pom
Progress (1): 12 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-impl/4.0.5/jaxb-impl-4.0.5.pom (12 kB at 373 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/mvn/jaxb-bundles/4.0.5/jaxb-bundles-4.0.5.pom
Progress (1): 1.3 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/mvn/jaxb-bundles/4.0.5/jaxb-bundles-4.0.5.pom (1.3 kB at 43 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-core/4.0.5/jaxb-core-4.0.5.pom
Progress (1): 11 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-core/4.0.5/jaxb-core-4.0.5.pom (11 kB at 344 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/activation/jakarta.activation/2.0.1/jakarta.activation-2.0.1.pom
Progress (1): 2.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/activation/jakarta.activation/2.0.1/jakarta.activation-2.0.1.pom (2.0 kB at 64 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/activation/all/2.0.1/all-2.0.1.pom
Progress (1): 16/23 kB
Progress (1): 23 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/activation/all/2.0.1/all-2.0.1.pom (23 kB at 713 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/eclipse/ee4j/project/1.0.6/project-1.0.6.pom
Progress (1): 13 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/eclipse/ee4j/project/1.0.6/project-1.0.6.pom (13 kB at 430 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/projectlombok/lombok/1.18.38/lombok-1.18.38.pom
Progress (1): 1.5 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/projectlombok/lombok/1.18.38/lombok-1.18.38.pom (1.5 kB at 48 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter-test/3.4.5/spring-boot-starter-test-3.4.5.pom
Progress (1): 5.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter-test/3.4.5/spring-boot-starter-test-3.4.5.pom (5.1 kB at 337 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-test/3.4.5/spring-boot-test-3.4.5.pom
Progress (1): 2.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-test/3.4.5/spring-boot-test-3.4.5.pom (2.2 kB at 71 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/spring-test/6.2.6/spring-test-6.2.6.pom
Progress (1): 2.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/spring-test/6.2.6/spring-test-6.2.6.pom (2.1 kB at 64 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-test-autoconfigure/3.4.5/spring-boot-test-autoconfigure-3.4.5.pom
Progress (1): 2.5 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-test-autoconfigure/3.4.5/spring-boot-test-autoconfigure-3.4.5.pom (2.5 kB at 77 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.pom
Progress (1): 1.9 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.pom (1.9 kB at 62 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/net/minidev/json-smart/2.5.2/json-smart-2.5.2.pom
Progress (1): 10 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/net/minidev/json-smart/2.5.2/json-smart-2.5.2.pom (10 kB at 318 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.pom
Progress (1): 12 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.pom (12 kB at 252 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/ow2/asm/asm/9.7.1/asm-9.7.1.pom
Progress (1): 2.4 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/ow2/asm/asm/9.7.1/asm-9.7.1.pom (2.4 kB at 148 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/ow2/ow2/1.5.1/ow2-1.5.1.pom
Progress (1): 11 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/ow2/ow2/1.5.1/ow2-1.5.1.pom (11 kB at 753 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/assertj/assertj-core/3.26.3/assertj-core-3.26.3.pom
Progress (1): 3.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/assertj/assertj-core/3.26.3/assertj-core-3.26.3.pom (3.8 kB at 4.2 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/net/bytebuddy/byte-buddy/1.15.11/byte-buddy-1.15.11.pom
Progress (1): 16/17 kB
Progress (1): 17 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/net/bytebuddy/byte-buddy/1.15.11/byte-buddy-1.15.11.pom (17 kB at 559 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/net/bytebuddy/byte-buddy-parent/1.15.11/byte-buddy-parent-1.15.11.pom
Progress (1): 16/63 kB
Progress (1): 33/63 kB
Progress (1): 49/63 kB
Progress (1): 63 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/net/bytebuddy/byte-buddy-parent/1.15.11/byte-buddy-parent-1.15.11.pom (63 kB at 1.9 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.pom
Progress (1): 3.5 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.pom (3.5 kB at 222 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/awaitility/awaitility-parent/4.2.2/awaitility-parent-4.2.2.pom
Progress (1): 11 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/awaitility/awaitility-parent/4.2.2/awaitility-parent-4.2.2.pom (11 kB at 678 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/oss/oss-parent/9/oss-parent-9.pom
Progress (1): 6.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/oss/oss-parent/9/oss-parent-9.pom (6.6 kB at 212 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/hamcrest/hamcrest/2.2/hamcrest-2.2.pom
Progress (1): 1.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/hamcrest/hamcrest/2.2/hamcrest-2.2.pom (1.1 kB at 36 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/junit/jupiter/junit-jupiter/5.11.4/junit-jupiter-5.11.4.pom
Progress (1): 3.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/junit/jupiter/junit-jupiter/5.11.4/junit-jupiter-5.11.4.pom (3.2 kB at 247 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/junit/jupiter/junit-jupiter-api/5.11.4/junit-jupiter-api-5.11.4.pom
Progress (1): 3.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/junit/jupiter/junit-jupiter-api/5.11.4/junit-jupiter-api-5.11.4.pom (3.2 kB at 103 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.pom
Progress (1): 2.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.pom (2.0 kB at 66 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/junit/platform/junit-platform-commons/1.11.4/junit-platform-commons-1.11.4.pom
Progress (1): 2.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/junit/platform/junit-platform-commons/1.11.4/junit-platform-commons-1.11.4.pom (2.8 kB at 89 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.pom
Progress (1): 1.5 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.pom (1.5 kB at 48 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/junit/jupiter/junit-jupiter-params/5.11.4/junit-jupiter-params-5.11.4.pom
Progress (1): 3.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/junit/jupiter/junit-jupiter-params/5.11.4/junit-jupiter-params-5.11.4.pom (3.0 kB at 94 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/junit/jupiter/junit-jupiter-engine/5.11.4/junit-jupiter-engine-5.11.4.pom
Progress (1): 3.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/junit/jupiter/junit-jupiter-engine/5.11.4/junit-jupiter-engine-5.11.4.pom (3.2 kB at 100 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/junit/platform/junit-platform-engine/1.11.4/junit-platform-engine-1.11.4.pom
Progress (1): 3.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/junit/platform/junit-platform-engine/1.11.4/junit-platform-engine-1.11.4.pom (3.2 kB at 214 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/mockito/mockito-core/5.14.2/mockito-core-5.14.2.pom
Progress (1): 2.5 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/mockito/mockito-core/5.14.2/mockito-core-5.14.2.pom (2.5 kB at 77 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.pom
Progress (1): 12 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.pom (12 kB at 377 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/objenesis/objenesis/3.3/objenesis-3.3.pom
Progress (1): 3.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/objenesis/objenesis/3.3/objenesis-3.3.pom (3.0 kB at 73 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/objenesis/objenesis-parent/3.3/objenesis-parent-3.3.pom
Progress (1): 16/19 kB
Progress (1): 19 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/objenesis/objenesis-parent/3.3/objenesis-parent-3.3.pom (19 kB at 766 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/mockito/mockito-junit-jupiter/5.14.2/mockito-junit-jupiter-5.14.2.pom
Progress (1): 2.3 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/mockito/mockito-junit-jupiter/5.14.2/mockito-junit-jupiter-5.14.2.pom (2.3 kB at 74 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.pom
Progress (1): 7.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.pom (7.0 kB at 218 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.pom
Progress (1): 2.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.pom (2.8 kB at 87 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/xmlunit/xmlunit-core/2.10.0/xmlunit-core-2.10.0.pom
Progress (1): 2.4 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/xmlunit/xmlunit-core/2.10.0/xmlunit-core-2.10.0.pom (2.4 kB at 79 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/xmlunit/xmlunit-parent/2.10.0/xmlunit-parent-2.10.0.pom
Progress (1): 16/21 kB
Progress (1): 21 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/xmlunit/xmlunit-parent/2.10.0/xmlunit-parent-2.10.0.pom (21 kB at 650 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter/3.4.5/spring-boot-starter-3.4.5.jar
Progress (1): 4.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter/3.4.5/spring-boot-starter-3.4.5.jar (4.8 kB at 298 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot/3.4.5/spring-boot-3.4.5.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/spring-context/6.2.6/spring-context-6.2.6.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-autoconfigure/3.4.5/spring-boot-autoconfigure-3.4.5.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter-logging/3.4.5/spring-boot-starter-logging-3.4.5.jar
Downloading from central: https://repo.maven.apache.org/maven2/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar
Progress (1): 0/1.4 MB
Progress (1): 0/1.4 MB
Progress (1): 0/1.4 MB
Progress (1): 0.1/1.4 MB
Progress (1): 0.1/1.4 MB
Progress (1): 0.1/1.4 MB
Progress (1): 0.1/1.4 MB
Progress (1): 0.1/1.4 MB
Progress (1): 0.1/1.4 MB
Progress (1): 0.2/1.4 MB
Progress (1): 0.2/1.4 MB
Progress (1): 0.2/1.4 MB
Progress (1): 0.2/1.4 MB
Progress (2): 0.2/1.4 MB | 1.4/4.8 kB
Progress (2): 0.2/1.4 MB | 2.8/4.8 kB
Progress (2): 0.2/1.4 MB | 2.8/4.8 kB
Progress (2): 0.2/1.4 MB | 4.1/4.8 kB
Progress (2): 0.2/1.4 MB | 4.8 kB    
Progress (3): 0.2/1.4 MB | 4.8 kB | 16/307 kB
Progress (3): 0.2/1.4 MB | 4.8 kB | 16/307 kB
Progress (4): 0.2/1.4 MB | 4.8 kB | 16/307 kB | 0/2.0 MB
Progress (4): 0.2/1.4 MB | 4.8 kB | 33/307 kB | 0/2.0 MB
Progress (4): 0.3/1.4 MB | 4.8 kB | 33/307 kB | 0/2.0 MB
Progress (4): 0.3/1.4 MB | 4.8 kB | 33/307 kB | 0/2.0 MB
Progress (4): 0.3/1.4 MB | 4.8 kB | 33/307 kB | 0/2.0 MB
Progress (4): 0.3/1.4 MB | 4.8 kB | 33/307 kB | 0/2.0 MB
                                                        
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter-logging/3.4.5/spring-boot-starter-logging-3.4.5.jar (4.8 kB at 85 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar
Progress (3): 0.3/1.4 MB | 49/307 kB | 0/2.0 MB
Progress (3): 0.3/1.4 MB | 49/307 kB | 0/2.0 MB
Progress (3): 0.3/1.4 MB | 49/307 kB | 0/2.0 MB
Progress (3): 0.3/1.4 MB | 66/307 kB | 0/2.0 MB
Progress (3): 0.3/1.4 MB | 66/307 kB | 0/2.0 MB
Progress (4): 0.3/1.4 MB | 66/307 kB | 0/2.0 MB | 0/1.8 MB
Progress (4): 0.3/1.4 MB | 82/307 kB | 0/2.0 MB | 0/1.8 MB
Progress (4): 0.3/1.4 MB | 82/307 kB | 0/2.0 MB | 0/1.8 MB
Progress (4): 0.3/1.4 MB | 82/307 kB | 0.1/2.0 MB | 0/1.8 MB
Progress (4): 0.3/1.4 MB | 98/307 kB | 0.1/2.0 MB | 0/1.8 MB
Progress (4): 0.4/1.4 MB | 98/307 kB | 0.1/2.0 MB | 0/1.8 MB
Progress (4): 0.4/1.4 MB | 98/307 kB | 0.1/2.0 MB | 0/1.8 MB
Progress (4): 0.4/1.4 MB | 98/307 kB | 0.1/2.0 MB | 0/1.8 MB
Progress (4): 0.4/1.4 MB | 115/307 kB | 0.1/2.0 MB | 0/1.8 MB
Progress (4): 0.4/1.4 MB | 115/307 kB | 0.1/2.0 MB | 0/1.8 MB
Progress (4): 0.4/1.4 MB | 115/307 kB | 0.1/2.0 MB | 0/1.8 MB
Progress (4): 0.4/1.4 MB | 115/307 kB | 0.1/2.0 MB | 0/1.8 MB
Progress (4): 0.4/1.4 MB | 115/307 kB | 0.1/2.0 MB | 0/1.8 MB
Progress (4): 0.4/1.4 MB | 115/307 kB | 0.1/2.0 MB | 0/1.8 MB
Progress (4): 0.4/1.4 MB | 131/307 kB | 0.1/2.0 MB | 0/1.8 MB
Progress (4): 0.4/1.4 MB | 147/307 kB | 0.1/2.0 MB | 0/1.8 MB
Progress (4): 0.4/1.4 MB | 147/307 kB | 0.1/2.0 MB | 0/1.8 MB
Progress (4): 0.4/1.4 MB | 147/307 kB | 0.1/2.0 MB | 0/1.8 MB
Progress (4): 0.4/1.4 MB | 164/307 kB | 0.1/2.0 MB | 0/1.8 MB
Progress (4): 0.4/1.4 MB | 164/307 kB | 0.1/2.0 MB | 0/1.8 MB
Progress (4): 0.4/1.4 MB | 180/307 kB | 0.1/2.0 MB | 0/1.8 MB
Progress (4): 0.4/1.4 MB | 180/307 kB | 0.1/2.0 MB | 0/1.8 MB
Progress (4): 0.4/1.4 MB | 180/307 kB | 0.1/2.0 MB | 0/1.8 MB
Progress (4): 0.4/1.4 MB | 180/307 kB | 0.2/2.0 MB | 0/1.8 MB
Progress (4): 0.4/1.4 MB | 180/307 kB | 0.2/2.0 MB | 0.1/1.8 MB
Progress (4): 0.4/1.4 MB | 197/307 kB | 0.2/2.0 MB | 0.1/1.8 MB
Progress (4): 0.4/1.4 MB | 213/307 kB | 0.2/2.0 MB | 0.1/1.8 MB
Progress (4): 0.4/1.4 MB | 229/307 kB | 0.2/2.0 MB | 0.1/1.8 MB
Progress (5): 0.4/1.4 MB | 229/307 kB | 0.2/2.0 MB | 0.1/1.8 MB | 16/627 kB
Progress (5): 0.4/1.4 MB | 229/307 kB | 0.2/2.0 MB | 0.1/1.8 MB | 16/627 kB
Progress (5): 0.5/1.4 MB | 229/307 kB | 0.2/2.0 MB | 0.1/1.8 MB | 16/627 kB
Progress (5): 0.5/1.4 MB | 229/307 kB | 0.2/2.0 MB | 0.1/1.8 MB | 16/627 kB
Progress (5): 0.5/1.4 MB | 246/307 kB | 0.2/2.0 MB | 0.1/1.8 MB | 16/627 kB
Progress (5): 0.5/1.4 MB | 246/307 kB | 0.2/2.0 MB | 0.1/1.8 MB | 16/627 kB
Progress (5): 0.5/1.4 MB | 246/307 kB | 0.2/2.0 MB | 0.1/1.8 MB | 16/627 kB
Progress (5): 0.5/1.4 MB | 256/307 kB | 0.2/2.0 MB | 0.1/1.8 MB | 16/627 kB
Progress (5): 0.5/1.4 MB | 256/307 kB | 0.2/2.0 MB | 0.1/1.8 MB | 16/627 kB
Progress (5): 0.5/1.4 MB | 272/307 kB | 0.2/2.0 MB | 0.1/1.8 MB | 16/627 kB
Progress (5): 0.5/1.4 MB | 272/307 kB | 0.2/2.0 MB | 0.1/1.8 MB | 16/627 kB
Progress (5): 0.5/1.4 MB | 272/307 kB | 0.2/2.0 MB | 0.1/1.8 MB | 16/627 kB
Progress (5): 0.5/1.4 MB | 272/307 kB | 0.2/2.0 MB | 0.1/1.8 MB | 16/627 kB
Progress (5): 0.5/1.4 MB | 272/307 kB | 0.2/2.0 MB | 0.1/1.8 MB | 33/627 kB
Progress (5): 0.5/1.4 MB | 272/307 kB | 0.3/2.0 MB | 0.1/1.8 MB | 33/627 kB
Progress (5): 0.5/1.4 MB | 272/307 kB | 0.3/2.0 MB | 0.1/1.8 MB | 33/627 kB
Progress (5): 0.5/1.4 MB | 272/307 kB | 0.3/2.0 MB | 0.1/1.8 MB | 33/627 kB
Progress (5): 0.5/1.4 MB | 272/307 kB | 0.3/2.0 MB | 0.1/1.8 MB | 33/627 kB
Progress (5): 0.5/1.4 MB | 289/307 kB | 0.3/2.0 MB | 0.1/1.8 MB | 33/627 kB
Progress (5): 0.5/1.4 MB | 305/307 kB | 0.3/2.0 MB | 0.1/1.8 MB | 33/627 kB
Progress (5): 0.5/1.4 MB | 307 kB | 0.3/2.0 MB | 0.1/1.8 MB | 33/627 kB    
Progress (5): 0.5/1.4 MB | 307 kB | 0.3/2.0 MB | 0.1/1.8 MB | 33/627 kB
Progress (5): 0.5/1.4 MB | 307 kB | 0.3/2.0 MB | 0.1/1.8 MB | 33/627 kB
Progress (5): 0.5/1.4 MB | 307 kB | 0.3/2.0 MB | 0.1/1.8 MB | 33/627 kB
Progress (5): 0.5/1.4 MB | 307 kB | 0.3/2.0 MB | 0.1/1.8 MB | 33/627 kB
Progress (5): 0.5/1.4 MB | 307 kB | 0.3/2.0 MB | 0.1/1.8 MB | 33/627 kB
Progress (5): 0.5/1.4 MB | 307 kB | 0.3/2.0 MB | 0.1/1.8 MB | 33/627 kB
Progress (5): 0.5/1.4 MB | 307 kB | 0.3/2.0 MB | 0.2/1.8 MB | 33/627 kB
Progress (5): 0.5/1.4 MB | 307 kB | 0.3/2.0 MB | 0.2/1.8 MB | 49/627 kB
Progress (5): 0.5/1.4 MB | 307 kB | 0.3/2.0 MB | 0.2/1.8 MB | 49/627 kB
Progress (5): 0.5/1.4 MB | 307 kB | 0.3/2.0 MB | 0.2/1.8 MB | 49/627 kB
Progress (5): 0.5/1.4 MB | 307 kB | 0.3/2.0 MB | 0.2/1.8 MB | 49/627 kB
Progress (5): 0.5/1.4 MB | 307 kB | 0.4/2.0 MB | 0.2/1.8 MB | 49/627 kB
Progress (5): 0.5/1.4 MB | 307 kB | 0.4/2.0 MB | 0.2/1.8 MB | 49/627 kB
Progress (5): 0.5/1.4 MB | 307 kB | 0.4/2.0 MB | 0.2/1.8 MB | 49/627 kB
Progress (5): 0.5/1.4 MB | 307 kB | 0.4/2.0 MB | 0.2/1.8 MB | 49/627 kB
Progress (5): 0.5/1.4 MB | 307 kB | 0.4/2.0 MB | 0.2/1.8 MB | 49/627 kB
Progress (5): 0.5/1.4 MB | 307 kB | 0.4/2.0 MB | 0.2/1.8 MB | 63/627 kB
Progress (5): 0.5/1.4 MB | 307 kB | 0.4/2.0 MB | 0.2/1.8 MB | 63/627 kB
Progress (5): 0.5/1.4 MB | 307 kB | 0.4/2.0 MB | 0.2/1.8 MB | 63/627 kB
Progress (5): 0.6/1.4 MB | 307 kB | 0.4/2.0 MB | 0.2/1.8 MB | 63/627 kB
Progress (5): 0.6/1.4 MB | 307 kB | 0.4/2.0 MB | 0.2/1.8 MB | 63/627 kB
Progress (5): 0.6/1.4 MB | 307 kB | 0.4/2.0 MB | 0.2/1.8 MB | 63/627 kB
Progress (5): 0.6/1.4 MB | 307 kB | 0.5/2.0 MB | 0.2/1.8 MB | 63/627 kB
Progress (5): 0.6/1.4 MB | 307 kB | 0.5/2.0 MB | 0.2/1.8 MB | 79/627 kB
Progress (5): 0.6/1.4 MB | 307 kB | 0.5/2.0 MB | 0.2/1.8 MB | 79/627 kB
Progress (5): 0.6/1.4 MB | 307 kB | 0.5/2.0 MB | 0.2/1.8 MB | 79/627 kB
Progress (5): 0.6/1.4 MB | 307 kB | 0.5/2.0 MB | 0.2/1.8 MB | 79/627 kB
Progress (5): 0.6/1.4 MB | 307 kB | 0.5/2.0 MB | 0.2/1.8 MB | 96/627 kB
Progress (5): 0.6/1.4 MB | 307 kB | 0.5/2.0 MB | 0.2/1.8 MB | 96/627 kB
                                                                       
Downloaded from central: https://repo.maven.apache.org/maven2/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar (307 kB at 3.5 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar
Progress (4): 0.6/1.4 MB | 0.5/2.0 MB | 0.2/1.8 MB | 96/627 kB
Progress (4): 0.6/1.4 MB | 0.5/2.0 MB | 0.2/1.8 MB | 96/627 kB
Progress (4): 0.6/1.4 MB | 0.5/2.0 MB | 0.3/1.8 MB | 96/627 kB
Progress (4): 0.6/1.4 MB | 0.5/2.0 MB | 0.3/1.8 MB | 96/627 kB
Progress (4): 0.6/1.4 MB | 0.5/2.0 MB | 0.3/1.8 MB | 112/627 kB
Progress (4): 0.6/1.4 MB | 0.5/2.0 MB | 0.3/1.8 MB | 112/627 kB
Progress (4): 0.6/1.4 MB | 0.5/2.0 MB | 0.3/1.8 MB | 112/627 kB
Progress (4): 0.6/1.4 MB | 0.5/2.0 MB | 0.3/1.8 MB | 112/627 kB
Progress (4): 0.6/1.4 MB | 0.6/2.0 MB | 0.3/1.8 MB | 112/627 kB
Progress (4): 0.6/1.4 MB | 0.6/2.0 MB | 0.3/1.8 MB | 112/627 kB
Progress (4): 0.6/1.4 MB | 0.6/2.0 MB | 0.3/1.8 MB | 112/627 kB
Progress (4): 0.6/1.4 MB | 0.6/2.0 MB | 0.3/1.8 MB | 112/627 kB
Progress (4): 0.6/1.4 MB | 0.6/2.0 MB | 0.3/1.8 MB | 112/627 kB
Progress (4): 0.6/1.4 MB | 0.6/2.0 MB | 0.3/1.8 MB | 112/627 kB
Progress (4): 0.6/1.4 MB | 0.6/2.0 MB | 0.3/1.8 MB | 112/627 kB
Progress (4): 0.6/1.4 MB | 0.6/2.0 MB | 0.3/1.8 MB | 112/627 kB
Progress (4): 0.6/1.4 MB | 0.6/2.0 MB | 0.3/1.8 MB | 112/627 kB
Progress (4): 0.6/1.4 MB | 0.6/2.0 MB | 0.3/1.8 MB | 112/627 kB
Progress (4): 0.6/1.4 MB | 0.7/2.0 MB | 0.3/1.8 MB | 112/627 kB
Progress (4): 0.6/1.4 MB | 0.7/2.0 MB | 0.3/1.8 MB | 129/627 kB
Progress (4): 0.6/1.4 MB | 0.7/2.0 MB | 0.3/1.8 MB | 129/627 kB
Progress (4): 0.6/1.4 MB | 0.7/2.0 MB | 0.3/1.8 MB | 129/627 kB
Progress (4): 0.6/1.4 MB | 0.7/2.0 MB | 0.3/1.8 MB | 145/627 kB
Progress (4): 0.6/1.4 MB | 0.7/2.0 MB | 0.3/1.8 MB | 145/627 kB
Progress (4): 0.6/1.4 MB | 0.7/2.0 MB | 0.3/1.8 MB | 145/627 kB
Progress (4): 0.6/1.4 MB | 0.7/2.0 MB | 0.3/1.8 MB | 145/627 kB
Progress (4): 0.6/1.4 MB | 0.7/2.0 MB | 0.3/1.8 MB | 145/627 kB
Progress (4): 0.6/1.4 MB | 0.7/2.0 MB | 0.3/1.8 MB | 145/627 kB
Progress (4): 0.6/1.4 MB | 0.7/2.0 MB | 0.3/1.8 MB | 145/627 kB
Progress (4): 0.6/1.4 MB | 0.7/2.0 MB | 0.4/1.8 MB | 145/627 kB
Progress (4): 0.6/1.4 MB | 0.7/2.0 MB | 0.4/1.8 MB | 145/627 kB
Progress (4): 0.6/1.4 MB | 0.7/2.0 MB | 0.4/1.8 MB | 161/627 kB
Progress (4): 0.6/1.4 MB | 0.8/2.0 MB | 0.4/1.8 MB | 161/627 kB
Progress (4): 0.6/1.4 MB | 0.8/2.0 MB | 0.4/1.8 MB | 161/627 kB
Progress (4): 0.6/1.4 MB | 0.8/2.0 MB | 0.4/1.8 MB | 161/627 kB
Progress (4): 0.6/1.4 MB | 0.8/2.0 MB | 0.4/1.8 MB | 161/627 kB
Progress (4): 0.6/1.4 MB | 0.8/2.0 MB | 0.4/1.8 MB | 161/627 kB
Progress (4): 0.6/1.4 MB | 0.8/2.0 MB | 0.4/1.8 MB | 161/627 kB
Progress (4): 0.6/1.4 MB | 0.8/2.0 MB | 0.4/1.8 MB | 161/627 kB
Progress (4): 0.6/1.4 MB | 0.8/2.0 MB | 0.4/1.8 MB | 178/627 kB
Progress (4): 0.6/1.4 MB | 0.8/2.0 MB | 0.4/1.8 MB | 178/627 kB
Progress (4): 0.6/1.4 MB | 0.8/2.0 MB | 0.4/1.8 MB | 178/627 kB
Progress (4): 0.6/1.4 MB | 0.8/2.0 MB | 0.4/1.8 MB | 178/627 kB
Progress (4): 0.7/1.4 MB | 0.8/2.0 MB | 0.4/1.8 MB | 178/627 kB
Progress (4): 0.7/1.4 MB | 0.8/2.0 MB | 0.4/1.8 MB | 178/627 kB
Progress (4): 0.7/1.4 MB | 0.8/2.0 MB | 0.4/1.8 MB | 178/627 kB
Progress (4): 0.7/1.4 MB | 0.9/2.0 MB | 0.4/1.8 MB | 178/627 kB
Progress (4): 0.7/1.4 MB | 0.9/2.0 MB | 0.4/1.8 MB | 178/627 kB
Progress (4): 0.7/1.4 MB | 0.9/2.0 MB | 0.5/1.8 MB | 178/627 kB
Progress (4): 0.7/1.4 MB | 0.9/2.0 MB | 0.5/1.8 MB | 178/627 kB
Progress (4): 0.7/1.4 MB | 0.9/2.0 MB | 0.5/1.8 MB | 194/627 kB
Progress (4): 0.7/1.4 MB | 0.9/2.0 MB | 0.5/1.8 MB | 194/627 kB
Progress (4): 0.7/1.4 MB | 0.9/2.0 MB | 0.5/1.8 MB | 194/627 kB
Progress (4): 0.7/1.4 MB | 0.9/2.0 MB | 0.5/1.8 MB | 194/627 kB
Progress (4): 0.7/1.4 MB | 0.9/2.0 MB | 0.5/1.8 MB | 194/627 kB
Progress (4): 0.7/1.4 MB | 1.0/2.0 MB | 0.5/1.8 MB | 194/627 kB
Progress (4): 0.7/1.4 MB | 1.0/2.0 MB | 0.5/1.8 MB | 210/627 kB
Progress (4): 0.7/1.4 MB | 1.0/2.0 MB | 0.5/1.8 MB | 210/627 kB
Progress (4): 0.7/1.4 MB | 1.0/2.0 MB | 0.5/1.8 MB | 227/627 kB
Progress (4): 0.7/1.4 MB | 1.0/2.0 MB | 0.5/1.8 MB | 227/627 kB
Progress (4): 0.7/1.4 MB | 1.0/2.0 MB | 0.5/1.8 MB | 227/627 kB
Progress (4): 0.7/1.4 MB | 1.0/2.0 MB | 0.5/1.8 MB | 227/627 kB
Progress (4): 0.7/1.4 MB | 1.0/2.0 MB | 0.5/1.8 MB | 227/627 kB
Progress (4): 0.7/1.4 MB | 1.0/2.0 MB | 0.5/1.8 MB | 227/627 kB
Progress (4): 0.7/1.4 MB | 1.0/2.0 MB | 0.5/1.8 MB | 243/627 kB
Progress (4): 0.7/1.4 MB | 1.0/2.0 MB | 0.5/1.8 MB | 243/627 kB
Progress (4): 0.7/1.4 MB | 1.0/2.0 MB | 0.5/1.8 MB | 243/627 kB
Progress (4): 0.7/1.4 MB | 1.0/2.0 MB | 0.5/1.8 MB | 260/627 kB
Progress (4): 0.7/1.4 MB | 1.0/2.0 MB | 0.5/1.8 MB | 260/627 kB
Progress (4): 0.7/1.4 MB | 1.0/2.0 MB | 0.5/1.8 MB | 260/627 kB
Progress (4): 0.7/1.4 MB | 1.1/2.0 MB | 0.5/1.8 MB | 260/627 kB
Progress (4): 0.7/1.4 MB | 1.1/2.0 MB | 0.5/1.8 MB | 260/627 kB
Progress (4): 0.7/1.4 MB | 1.1/2.0 MB | 0.5/1.8 MB | 260/627 kB
Progress (4): 0.7/1.4 MB | 1.1/2.0 MB | 0.5/1.8 MB | 260/627 kB
Progress (4): 0.7/1.4 MB | 1.1/2.0 MB | 0.5/1.8 MB | 260/627 kB
Progress (4): 0.7/1.4 MB | 1.1/2.0 MB | 0.5/1.8 MB | 260/627 kB
Progress (4): 0.7/1.4 MB | 1.1/2.0 MB | 0.5/1.8 MB | 260/627 kB
Progress (4): 0.7/1.4 MB | 1.1/2.0 MB | 0.5/1.8 MB | 260/627 kB
Progress (4): 0.7/1.4 MB | 1.1/2.0 MB | 0.6/1.8 MB | 260/627 kB
Progress (4): 0.7/1.4 MB | 1.1/2.0 MB | 0.6/1.8 MB | 260/627 kB
Progress (4): 0.7/1.4 MB | 1.1/2.0 MB | 0.6/1.8 MB | 260/627 kB
Progress (4): 0.7/1.4 MB | 1.1/2.0 MB | 0.6/1.8 MB | 260/627 kB
Progress (5): 0.7/1.4 MB | 1.1/2.0 MB | 0.6/1.8 MB | 260/627 kB | 16/24 kB
Progress (5): 0.7/1.4 MB | 1.1/2.0 MB | 0.6/1.8 MB | 260/627 kB | 24 kB   
Progress (5): 0.7/1.4 MB | 1.1/2.0 MB | 0.6/1.8 MB | 260/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.1/2.0 MB | 0.6/1.8 MB | 260/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.2/2.0 MB | 0.6/1.8 MB | 260/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.2/2.0 MB | 0.6/1.8 MB | 276/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.2/2.0 MB | 0.6/1.8 MB | 276/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.2/2.0 MB | 0.6/1.8 MB | 276/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.2/2.0 MB | 0.6/1.8 MB | 292/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.2/2.0 MB | 0.6/1.8 MB | 292/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.2/2.0 MB | 0.7/1.8 MB | 292/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.2/2.0 MB | 0.7/1.8 MB | 292/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.2/2.0 MB | 0.7/1.8 MB | 292/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.2/2.0 MB | 0.7/1.8 MB | 292/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.2/2.0 MB | 0.7/1.8 MB | 292/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.2/2.0 MB | 0.7/1.8 MB | 292/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.2/2.0 MB | 0.7/1.8 MB | 292/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.2/2.0 MB | 0.7/1.8 MB | 292/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.2/2.0 MB | 0.7/1.8 MB | 292/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.2/2.0 MB | 0.7/1.8 MB | 309/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.3/2.0 MB | 0.7/1.8 MB | 309/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.3/2.0 MB | 0.7/1.8 MB | 325/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.3/2.0 MB | 0.7/1.8 MB | 325/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.3/2.0 MB | 0.7/1.8 MB | 325/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.3/2.0 MB | 0.7/1.8 MB | 325/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.3/2.0 MB | 0.7/1.8 MB | 325/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.3/2.0 MB | 0.7/1.8 MB | 325/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.3/2.0 MB | 0.7/1.8 MB | 325/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.3/2.0 MB | 0.8/1.8 MB | 325/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.3/2.0 MB | 0.8/1.8 MB | 342/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.3/2.0 MB | 0.8/1.8 MB | 342/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.3/2.0 MB | 0.8/1.8 MB | 342/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.3/2.0 MB | 0.8/1.8 MB | 342/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.3/2.0 MB | 0.8/1.8 MB | 358/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.3/2.0 MB | 0.8/1.8 MB | 358/627 kB | 24 kB
Progress (5): 0.7/1.4 MB | 1.3/2.0 MB | 0.8/1.8 MB | 358/627 kB | 24 kB
Progress (5): 0.8/1.4 MB | 1.3/2.0 MB | 0.8/1.8 MB | 358/627 kB | 24 kB
Progress (5): 0.8/1.4 MB | 1.4/2.0 MB | 0.8/1.8 MB | 358/627 kB | 24 kB
Progress (5): 0.8/1.4 MB | 1.4/2.0 MB | 0.8/1.8 MB | 374/627 kB | 24 kB
Progress (5): 0.8/1.4 MB | 1.4/2.0 MB | 0.8/1.8 MB | 374/627 kB | 24 kB
Progress (5): 0.8/1.4 MB | 1.4/2.0 MB | 0.8/1.8 MB | 374/627 kB | 24 kB
Progress (5): 0.8/1.4 MB | 1.4/2.0 MB | 0.8/1.8 MB | 391/627 kB | 24 kB
Progress (5): 0.8/1.4 MB | 1.4/2.0 MB | 0.8/1.8 MB | 391/627 kB | 24 kB
Progress (5): 0.8/1.4 MB | 1.4/2.0 MB | 0.8/1.8 MB | 391/627 kB | 24 kB
Progress (5): 0.8/1.4 MB | 1.4/2.0 MB | 0.8/1.8 MB | 407/627 kB | 24 kB
Progress (5): 0.8/1.4 MB | 1.4/2.0 MB | 0.8/1.8 MB | 407/627 kB | 24 kB
Progress (5): 0.8/1.4 MB | 1.4/2.0 MB | 0.8/1.8 MB | 423/627 kB | 24 kB
Progress (5): 0.8/1.4 MB | 1.4/2.0 MB | 0.8/1.8 MB | 423/627 kB | 24 kB
Progress (5): 0.8/1.4 MB | 1.4/2.0 MB | 0.8/1.8 MB | 423/627 kB | 24 kB
Progress (5): 0.8/1.4 MB | 1.4/2.0 MB | 0.8/1.8 MB | 440/627 kB | 24 kB
Progress (5): 0.8/1.4 MB | 1.4/2.0 MB | 0.8/1.8 MB | 440/627 kB | 24 kB
Progress (5): 0.8/1.4 MB | 1.4/2.0 MB | 0.8/1.8 MB | 440/627 kB | 24 kB
Progress (5): 0.8/1.4 MB | 1.4/2.0 MB | 0.8/1.8 MB | 456/627 kB | 24 kB
Progress (5): 0.8/1.4 MB | 1.4/2.0 MB | 0.9/1.8 MB | 456/627 kB | 24 kB
Progress (5): 0.8/1.4 MB | 1.4/2.0 MB | 0.9/1.8 MB | 456/627 kB | 24 kB
Progress (5): 0.8/1.4 MB | 1.5/2.0 MB | 0.9/1.8 MB | 456/627 kB | 24 kB
                                                                       
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar (24 kB at 195 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar
Progress (4): 0.8/1.4 MB | 1.5/2.0 MB | 0.9/1.8 MB | 456/627 kB
Progress (4): 0.8/1.4 MB | 1.5/2.0 MB | 0.9/1.8 MB | 456/627 kB
Progress (4): 0.8/1.4 MB | 1.5/2.0 MB | 0.9/1.8 MB | 473/627 kB
Progress (4): 0.8/1.4 MB | 1.5/2.0 MB | 0.9/1.8 MB | 473/627 kB
Progress (4): 0.8/1.4 MB | 1.5/2.0 MB | 0.9/1.8 MB | 489/627 kB
Progress (4): 0.8/1.4 MB | 1.5/2.0 MB | 0.9/1.8 MB | 489/627 kB
Progress (4): 0.8/1.4 MB | 1.5/2.0 MB | 0.9/1.8 MB | 505/627 kB
Progress (4): 0.8/1.4 MB | 1.5/2.0 MB | 0.9/1.8 MB | 505/627 kB
Progress (4): 0.8/1.4 MB | 1.5/2.0 MB | 0.9/1.8 MB | 522/627 kB
Progress (4): 0.8/1.4 MB | 1.5/2.0 MB | 0.9/1.8 MB | 522/627 kB
Progress (4): 0.8/1.4 MB | 1.5/2.0 MB | 0.9/1.8 MB | 522/627 kB
Progress (4): 0.8/1.4 MB | 1.5/2.0 MB | 0.9/1.8 MB | 522/627 kB
Progress (4): 0.8/1.4 MB | 1.5/2.0 MB | 0.9/1.8 MB | 538/627 kB
Progress (4): 0.8/1.4 MB | 1.6/2.0 MB | 0.9/1.8 MB | 538/627 kB
Progress (4): 0.8/1.4 MB | 1.6/2.0 MB | 0.9/1.8 MB | 538/627 kB
Progress (4): 0.8/1.4 MB | 1.6/2.0 MB | 0.9/1.8 MB | 555/627 kB
Progress (4): 0.8/1.4 MB | 1.6/2.0 MB | 0.9/1.8 MB | 555/627 kB
Progress (4): 0.8/1.4 MB | 1.6/2.0 MB | 0.9/1.8 MB | 555/627 kB
Progress (4): 0.8/1.4 MB | 1.6/2.0 MB | 0.9/1.8 MB | 571/627 kB
Progress (4): 0.8/1.4 MB | 1.6/2.0 MB | 0.9/1.8 MB | 571/627 kB
Progress (4): 0.8/1.4 MB | 1.6/2.0 MB | 0.9/1.8 MB | 571/627 kB
Progress (4): 0.8/1.4 MB | 1.6/2.0 MB | 0.9/1.8 MB | 571/627 kB
Progress (4): 0.8/1.4 MB | 1.6/2.0 MB | 0.9/1.8 MB | 571/627 kB
Progress (4): 0.8/1.4 MB | 1.6/2.0 MB | 0.9/1.8 MB | 571/627 kB
Progress (4): 0.8/1.4 MB | 1.6/2.0 MB | 0.9/1.8 MB | 571/627 kB
Progress (4): 0.8/1.4 MB | 1.6/2.0 MB | 1.0/1.8 MB | 571/627 kB
Progress (4): 0.8/1.4 MB | 1.7/2.0 MB | 1.0/1.8 MB | 571/627 kB
Progress (4): 0.8/1.4 MB | 1.7/2.0 MB | 1.0/1.8 MB | 571/627 kB
Progress (4): 0.8/1.4 MB | 1.7/2.0 MB | 1.0/1.8 MB | 571/627 kB
Progress (4): 0.8/1.4 MB | 1.7/2.0 MB | 1.0/1.8 MB | 587/627 kB
Progress (4): 0.8/1.4 MB | 1.7/2.0 MB | 1.0/1.8 MB | 587/627 kB
Progress (4): 0.8/1.4 MB | 1.7/2.0 MB | 1.0/1.8 MB | 587/627 kB
Progress (4): 0.8/1.4 MB | 1.7/2.0 MB | 1.0/1.8 MB | 587/627 kB
Progress (4): 0.8/1.4 MB | 1.7/2.0 MB | 1.0/1.8 MB | 587/627 kB
Progress (4): 0.8/1.4 MB | 1.7/2.0 MB | 1.0/1.8 MB | 587/627 kB
Progress (4): 0.8/1.4 MB | 1.7/2.0 MB | 1.0/1.8 MB | 587/627 kB
Progress (4): 0.8/1.4 MB | 1.7/2.0 MB | 1.0/1.8 MB | 587/627 kB
Progress (4): 0.8/1.4 MB | 1.7/2.0 MB | 1.0/1.8 MB | 587/627 kB
Progress (4): 0.8/1.4 MB | 1.8/2.0 MB | 1.0/1.8 MB | 587/627 kB
Progress (4): 0.8/1.4 MB | 1.8/2.0 MB | 1.0/1.8 MB | 587/627 kB
Progress (4): 0.8/1.4 MB | 1.8/2.0 MB | 1.0/1.8 MB | 587/627 kB
Progress (4): 0.8/1.4 MB | 1.8/2.0 MB | 1.0/1.8 MB | 587/627 kB
Progress (4): 0.8/1.4 MB | 1.8/2.0 MB | 1.1/1.8 MB | 587/627 kB
Progress (4): 0.9/1.4 MB | 1.8/2.0 MB | 1.1/1.8 MB | 587/627 kB
Progress (4): 0.9/1.4 MB | 1.8/2.0 MB | 1.1/1.8 MB | 587/627 kB
Progress (4): 0.9/1.4 MB | 1.8/2.0 MB | 1.1/1.8 MB | 587/627 kB
Progress (4): 0.9/1.4 MB | 1.8/2.0 MB | 1.1/1.8 MB | 604/627 kB
Progress (4): 0.9/1.4 MB | 1.8/2.0 MB | 1.1/1.8 MB | 604/627 kB
Progress (4): 0.9/1.4 MB | 1.8/2.0 MB | 1.1/1.8 MB | 604/627 kB
Progress (4): 0.9/1.4 MB | 1.8/2.0 MB | 1.1/1.8 MB | 620/627 kB
Progress (4): 0.9/1.4 MB | 1.8/2.0 MB | 1.1/1.8 MB | 620/627 kB
Progress (4): 0.9/1.4 MB | 1.8/2.0 MB | 1.1/1.8 MB | 627 kB    
Progress (4): 0.9/1.4 MB | 1.8/2.0 MB | 1.1/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 1.8/2.0 MB | 1.1/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 1.8/2.0 MB | 1.1/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 1.9/2.0 MB | 1.1/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 1.9/2.0 MB | 1.1/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 1.9/2.0 MB | 1.1/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 1.9/2.0 MB | 1.1/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 1.9/2.0 MB | 1.1/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 1.9/2.0 MB | 1.2/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 1.9/2.0 MB | 1.2/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 1.9/2.0 MB | 1.2/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 1.9/2.0 MB | 1.2/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 1.9/2.0 MB | 1.2/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 1.9/2.0 MB | 1.2/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 1.9/2.0 MB | 1.2/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 1.9/2.0 MB | 1.2/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 2.0/2.0 MB | 1.2/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 2.0/2.0 MB | 1.2/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 2.0/2.0 MB | 1.2/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 2.0/2.0 MB | 1.2/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 2.0/2.0 MB | 1.3/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 2.0/2.0 MB | 1.3/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 2.0/2.0 MB | 1.3/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 2.0/2.0 MB | 1.3/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 2.0 MB | 1.3/1.8 MB | 627 kB    
Progress (4): 0.9/1.4 MB | 2.0 MB | 1.3/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 2.0 MB | 1.3/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 2.0 MB | 1.3/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 2.0 MB | 1.3/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 2.0 MB | 1.4/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 2.0 MB | 1.4/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 2.0 MB | 1.4/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 2.0 MB | 1.4/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 2.0 MB | 1.4/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 2.0 MB | 1.4/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 2.0 MB | 1.4/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 2.0 MB | 1.4/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 2.0 MB | 1.4/1.8 MB | 627 kB
Progress (4): 0.9/1.4 MB | 2.0 MB | 1.5/1.8 MB | 627 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.5/1.8 MB | 627 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.5/1.8 MB | 627 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.5/1.8 MB | 627 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.5/1.8 MB | 627 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.5/1.8 MB | 627 kB
Progress (5): 1.0/1.4 MB | 2.0 MB | 1.5/1.8 MB | 627 kB | 16/349 kB
Progress (5): 1.0/1.4 MB | 2.0 MB | 1.5/1.8 MB | 627 kB | 16/349 kB
Progress (5): 1.0/1.4 MB | 2.0 MB | 1.6/1.8 MB | 627 kB | 16/349 kB
Progress (5): 1.0/1.4 MB | 2.0 MB | 1.6/1.8 MB | 627 kB | 33/349 kB
Progress (5): 1.0/1.4 MB | 2.0 MB | 1.6/1.8 MB | 627 kB | 33/349 kB
Progress (5): 1.0/1.4 MB | 2.0 MB | 1.6/1.8 MB | 627 kB | 33/349 kB
Progress (5): 1.0/1.4 MB | 2.0 MB | 1.6/1.8 MB | 627 kB | 49/349 kB
Progress (5): 1.0/1.4 MB | 2.0 MB | 1.6/1.8 MB | 627 kB | 49/349 kB
Progress (5): 1.0/1.4 MB | 2.0 MB | 1.6/1.8 MB | 627 kB | 49/349 kB
Progress (5): 1.0/1.4 MB | 2.0 MB | 1.6/1.8 MB | 627 kB | 62/349 kB
                                                                   
Downloaded from central: https://repo.maven.apache.org/maven2/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar (627 kB at 4.6 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.6/1.8 MB | 62/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.6/1.8 MB | 62/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.6/1.8 MB | 62/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.6/1.8 MB | 79/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.7/1.8 MB | 79/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.7/1.8 MB | 95/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.7/1.8 MB | 95/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.7/1.8 MB | 95/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.7/1.8 MB | 111/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.7/1.8 MB | 111/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.7/1.8 MB | 111/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.7/1.8 MB | 128/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.7/1.8 MB | 128/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.7/1.8 MB | 128/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.7/1.8 MB | 128/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.7/1.8 MB | 144/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.8/1.8 MB | 144/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.8 MB | 144/349 kB    
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.8 MB | 161/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.8 MB | 177/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.8 MB | 177/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.8 MB | 193/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.8 MB | 210/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.8 MB | 226/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.8 MB | 242/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.8 MB | 259/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.8 MB | 275/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.8 MB | 292/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.8 MB | 308/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.8 MB | 324/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.8 MB | 324/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.8 MB | 341/349 kB
Progress (4): 1.0/1.4 MB | 2.0 MB | 1.8 MB | 349 kB    
Progress (4): 1.1/1.4 MB | 2.0 MB | 1.8 MB | 349 kB
Progress (4): 1.1/1.4 MB | 2.0 MB | 1.8 MB | 349 kB
Progress (4): 1.1/1.4 MB | 2.0 MB | 1.8 MB | 349 kB
Progress (4): 1.1/1.4 MB | 2.0 MB | 1.8 MB | 349 kB
                                                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-autoconfigure/3.4.5/spring-boot-autoconfigure-3.4.5.jar (2.0 MB at 13 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar
Progress (3): 1.1/1.4 MB | 1.8 MB | 349 kB
Progress (3): 1.1/1.4 MB | 1.8 MB | 349 kB
Progress (3): 1.2/1.4 MB | 1.8 MB | 349 kB
                                          
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar (349 kB at 2.3 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/spring-core/6.2.6/spring-core-6.2.6.jar
Progress (3): 1.2/1.4 MB | 1.8 MB | 6.3 kB
                                          
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot/3.4.5/spring-boot-3.4.5.jar (1.8 MB at 12 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/spring-jcl/6.2.6/spring-jcl-6.2.6.jar
Progress (2): 1.2/1.4 MB | 6.3 kB
Progress (2): 1.2/1.4 MB | 6.3 kB
Progress (2): 1.2/1.4 MB | 6.3 kB
Progress (2): 1.2/1.4 MB | 6.3 kB
                                 
Downloaded from central: https://repo.maven.apache.org/maven2/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar (6.3 kB at 42 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/yaml/snakeyaml/2.3/snakeyaml-2.3.jar
Progress (1): 1.2/1.4 MB
Progress (1): 1.2/1.4 MB
Progress (1): 1.3/1.4 MB
Progress (1): 1.3/1.4 MB
Progress (2): 1.3/1.4 MB | 16/26 kB
Progress (2): 1.3/1.4 MB | 26 kB   
Progress (2): 1.3/1.4 MB | 26 kB
Progress (2): 1.3/1.4 MB | 26 kB
Progress (2): 1.3/1.4 MB | 26 kB
Progress (2): 1.3/1.4 MB | 26 kB
Progress (2): 1.4 MB | 26 kB    
Progress (3): 1.4 MB | 26 kB | 16/25 kB
Progress (3): 1.4 MB | 26 kB | 25 kB   
                                    
Downloaded from central: https://repo.maven.apache.org/maven2/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar (26 kB at 157 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter-web/3.4.5/spring-boot-starter-web-3.4.5.jar
Progress (3): 1.4 MB | 25 kB | 16/342 kB
Progress (4): 1.4 MB | 25 kB | 16/342 kB | 0/2.0 MB
Progress (4): 1.4 MB | 25 kB | 33/342 kB | 0/2.0 MB
Progress (4): 1.4 MB | 25 kB | 49/342 kB | 0/2.0 MB
Progress (4): 1.4 MB | 25 kB | 49/342 kB | 0/2.0 MB
Progress (4): 1.4 MB | 25 kB | 49/342 kB | 0/2.0 MB
Progress (4): 1.4 MB | 25 kB | 65/342 kB | 0/2.0 MB
Progress (4): 1.4 MB | 25 kB | 81/342 kB | 0/2.0 MB
Progress (4): 1.4 MB | 25 kB | 81/342 kB | 0.1/2.0 MB
Progress (4): 1.4 MB | 25 kB | 98/342 kB | 0.1/2.0 MB
Progress (4): 1.4 MB | 25 kB | 98/342 kB | 0.1/2.0 MB
Progress (4): 1.4 MB | 25 kB | 114/342 kB | 0.1/2.0 MB
Progress (4): 1.4 MB | 25 kB | 114/342 kB | 0.1/2.0 MB
Progress (4): 1.4 MB | 25 kB | 130/342 kB | 0.1/2.0 MB
Progress (4): 1.4 MB | 25 kB | 130/342 kB | 0.1/2.0 MB
Progress (4): 1.4 MB | 25 kB | 147/342 kB | 0.1/2.0 MB
Progress (4): 1.4 MB | 25 kB | 147/342 kB | 0.1/2.0 MB
Progress (4): 1.4 MB | 25 kB | 163/342 kB | 0.1/2.0 MB
Progress (4): 1.4 MB | 25 kB | 163/342 kB | 0.1/2.0 MB
Progress (4): 1.4 MB | 25 kB | 180/342 kB | 0.1/2.0 MB
Progress (4): 1.4 MB | 25 kB | 180/342 kB | 0.2/2.0 MB
Progress (4): 1.4 MB | 25 kB | 196/342 kB | 0.2/2.0 MB
Progress (4): 1.4 MB | 25 kB | 196/342 kB | 0.2/2.0 MB
Progress (4): 1.4 MB | 25 kB | 212/342 kB | 0.2/2.0 MB
Progress (4): 1.4 MB | 25 kB | 229/342 kB | 0.2/2.0 MB
Progress (4): 1.4 MB | 25 kB | 229/342 kB | 0.2/2.0 MB
Progress (4): 1.4 MB | 25 kB | 245/342 kB | 0.2/2.0 MB
Progress (4): 1.4 MB | 25 kB | 245/342 kB | 0.2/2.0 MB
Progress (4): 1.4 MB | 25 kB | 262/342 kB | 0.2/2.0 MB
Progress (4): 1.4 MB | 25 kB | 262/342 kB | 0.2/2.0 MB
Progress (4): 1.4 MB | 25 kB | 262/342 kB | 0.2/2.0 MB
Progress (4): 1.4 MB | 25 kB | 278/342 kB | 0.2/2.0 MB
Progress (4): 1.4 MB | 25 kB | 278/342 kB | 0.3/2.0 MB
Progress (4): 1.4 MB | 25 kB | 294/342 kB | 0.3/2.0 MB
Progress (4): 1.4 MB | 25 kB | 294/342 kB | 0.3/2.0 MB
Progress (4): 1.4 MB | 25 kB | 311/342 kB | 0.3/2.0 MB
Progress (4): 1.4 MB | 25 kB | 327/342 kB | 0.3/2.0 MB
Progress (4): 1.4 MB | 25 kB | 327/342 kB | 0.3/2.0 MB
Progress (4): 1.4 MB | 25 kB | 342 kB | 0.3/2.0 MB    
Progress (4): 1.4 MB | 25 kB | 342 kB | 0.3/2.0 MB
Progress (4): 1.4 MB | 25 kB | 342 kB | 0.3/2.0 MB
Progress (4): 1.4 MB | 25 kB | 342 kB | 0.3/2.0 MB
Progress (4): 1.4 MB | 25 kB | 342 kB | 0.4/2.0 MB
                                                  
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/spring-jcl/6.2.6/spring-jcl-6.2.6.jar (25 kB at 139 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter-json/3.4.5/spring-boot-starter-json-3.4.5.jar
Progress (3): 1.4 MB | 342 kB | 0.4/2.0 MB
Progress (3): 1.4 MB | 342 kB | 0.4/2.0 MB
Progress (3): 1.4 MB | 342 kB | 0.4/2.0 MB
Progress (3): 1.4 MB | 342 kB | 0.4/2.0 MB
Progress (3): 1.4 MB | 342 kB | 0.4/2.0 MB
Progress (3): 1.4 MB | 342 kB | 0.5/2.0 MB
                                          
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/spring-context/6.2.6/spring-context-6.2.6.jar (1.4 MB at 7.4 MB/s)
Progress (2): 342 kB | 0.5/2.0 MB
                                 
Downloading from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.18.3/jackson-databind-2.18.3.jar
Progress (2): 342 kB | 0.5/2.0 MB
Progress (2): 342 kB | 0.5/2.0 MB
Progress (2): 342 kB | 0.5/2.0 MB
Progress (2): 342 kB | 0.5/2.0 MB
Progress (2): 342 kB | 0.6/2.0 MB
Progress (2): 342 kB | 0.6/2.0 MB
Progress (2): 342 kB | 0.6/2.0 MB
Progress (2): 342 kB | 0.6/2.0 MB
Progress (2): 342 kB | 0.6/2.0 MB
Progress (3): 342 kB | 0.6/2.0 MB | 4.8 kB
Progress (3): 342 kB | 0.6/2.0 MB | 4.8 kB
Progress (3): 342 kB | 0.6/2.0 MB | 4.8 kB
Progress (3): 342 kB | 0.7/2.0 MB | 4.8 kB
Progress (3): 342 kB | 0.7/2.0 MB | 4.8 kB
Progress (3): 342 kB | 0.7/2.0 MB | 4.8 kB
Progress (3): 342 kB | 0.7/2.0 MB | 4.8 kB
Progress (3): 342 kB | 0.7/2.0 MB | 4.8 kB
                                          
Downloaded from central: https://repo.maven.apache.org/maven2/org/yaml/snakeyaml/2.3/snakeyaml-2.3.jar (342 kB at 1.9 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.18.3/jackson-annotations-2.18.3.jar
Progress (2): 0.7/2.0 MB | 4.8 kB
Progress (2): 0.8/2.0 MB | 4.8 kB
Progress (2): 0.8/2.0 MB | 4.8 kB
Progress (2): 0.8/2.0 MB | 4.8 kB
Progress (2): 0.8/2.0 MB | 4.8 kB
Progress (2): 0.8/2.0 MB | 4.8 kB
Progress (2): 0.8/2.0 MB | 4.8 kB
Progress (2): 0.9/2.0 MB | 4.8 kB
Progress (2): 0.9/2.0 MB | 4.8 kB
Progress (2): 0.9/2.0 MB | 4.8 kB
Progress (2): 0.9/2.0 MB | 4.8 kB
Progress (2): 0.9/2.0 MB | 4.8 kB
Progress (2): 0.9/2.0 MB | 4.8 kB
Progress (2): 1.0/2.0 MB | 4.8 kB
Progress (2): 1.0/2.0 MB | 4.8 kB
Progress (2): 1.0/2.0 MB | 4.8 kB
Progress (2): 1.0/2.0 MB | 4.8 kB
Progress (2): 1.0/2.0 MB | 4.8 kB
Progress (2): 1.0/2.0 MB | 4.8 kB
Progress (2): 1.1/2.0 MB | 4.8 kB
                                 
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter-web/3.4.5/spring-boot-starter-web-3.4.5.jar (4.8 kB at 26 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/core/jackson-core/2.18.3/jackson-core-2.18.3.jar
Progress (1): 1.1/2.0 MB
Progress (1): 1.1/2.0 MB
Progress (1): 1.1/2.0 MB
Progress (1): 1.1/2.0 MB
Progress (1): 1.1/2.0 MB
Progress (1): 1.2/2.0 MB
Progress (1): 1.2/2.0 MB
Progress (1): 1.2/2.0 MB
Progress (2): 1.2/2.0 MB | 4.7 kB
Progress (2): 1.2/2.0 MB | 4.7 kB
Progress (2): 1.2/2.0 MB | 4.7 kB
Progress (2): 1.2/2.0 MB | 4.7 kB
Progress (2): 1.3/2.0 MB | 4.7 kB
Progress (2): 1.3/2.0 MB | 4.7 kB
Progress (2): 1.3/2.0 MB | 4.7 kB
Progress (2): 1.3/2.0 MB | 4.7 kB
Progress (2): 1.3/2.0 MB | 4.7 kB
Progress (2): 1.3/2.0 MB | 4.7 kB
Progress (2): 1.3/2.0 MB | 4.7 kB
Progress (2): 1.4/2.0 MB | 4.7 kB
Progress (2): 1.4/2.0 MB | 4.7 kB
Progress (2): 1.4/2.0 MB | 4.7 kB
Progress (3): 1.4/2.0 MB | 4.7 kB | 0/1.7 MB
Progress (3): 1.4/2.0 MB | 4.7 kB | 0/1.7 MB
Progress (3): 1.4/2.0 MB | 4.7 kB | 0/1.7 MB
Progress (3): 1.4/2.0 MB | 4.7 kB | 0/1.7 MB
Progress (3): 1.4/2.0 MB | 4.7 kB | 0/1.7 MB
Progress (3): 1.4/2.0 MB | 4.7 kB | 0/1.7 MB
Progress (3): 1.5/2.0 MB | 4.7 kB | 0/1.7 MB
Progress (3): 1.5/2.0 MB | 4.7 kB | 0/1.7 MB
Progress (3): 1.5/2.0 MB | 4.7 kB | 0/1.7 MB
Progress (3): 1.5/2.0 MB | 4.7 kB | 0/1.7 MB
Progress (3): 1.5/2.0 MB | 4.7 kB | 0.1/1.7 MB
Progress (3): 1.5/2.0 MB | 4.7 kB | 0.1/1.7 MB
Progress (3): 1.5/2.0 MB | 4.7 kB | 0.1/1.7 MB
Progress (3): 1.6/2.0 MB | 4.7 kB | 0.1/1.7 MB
Progress (3): 1.6/2.0 MB | 4.7 kB | 0.1/1.7 MB
Progress (3): 1.6/2.0 MB | 4.7 kB | 0.1/1.7 MB
Progress (3): 1.6/2.0 MB | 4.7 kB | 0.1/1.7 MB
Progress (3): 1.6/2.0 MB | 4.7 kB | 0.1/1.7 MB
Progress (3): 1.6/2.0 MB | 4.7 kB | 0.1/1.7 MB
Progress (4): 1.6/2.0 MB | 4.7 kB | 0.1/1.7 MB | 16/79 kB
Progress (4): 1.6/2.0 MB | 4.7 kB | 0.1/1.7 MB | 16/79 kB
Progress (4): 1.6/2.0 MB | 4.7 kB | 0.1/1.7 MB | 33/79 kB
Progress (4): 1.6/2.0 MB | 4.7 kB | 0.1/1.7 MB | 33/79 kB
Progress (4): 1.6/2.0 MB | 4.7 kB | 0.1/1.7 MB | 49/79 kB
Progress (4): 1.7/2.0 MB | 4.7 kB | 0.1/1.7 MB | 49/79 kB
Progress (4): 1.7/2.0 MB | 4.7 kB | 0.1/1.7 MB | 53/79 kB
Progress (4): 1.7/2.0 MB | 4.7 kB | 0.1/1.7 MB | 53/79 kB
                                                         
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter-json/3.4.5/spring-boot-starter-json-3.4.5.jar (4.7 kB at 24 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.18.3/jackson-datatype-jdk8-2.18.3.jar
Progress (3): 1.7/2.0 MB | 0.1/1.7 MB | 53/79 kB
Progress (3): 1.7/2.0 MB | 0.1/1.7 MB | 70/79 kB
Progress (3): 1.7/2.0 MB | 0.1/1.7 MB | 79 kB   
Progress (3): 1.7/2.0 MB | 0.1/1.7 MB | 79 kB
Progress (3): 1.7/2.0 MB | 0.1/1.7 MB | 79 kB
Progress (3): 1.7/2.0 MB | 0.1/1.7 MB | 79 kB
Progress (3): 1.7/2.0 MB | 0.1/1.7 MB | 79 kB
Progress (3): 1.7/2.0 MB | 0.1/1.7 MB | 79 kB
Progress (3): 1.8/2.0 MB | 0.1/1.7 MB | 79 kB
Progress (3): 1.8/2.0 MB | 0.1/1.7 MB | 79 kB
Progress (3): 1.8/2.0 MB | 0.1/1.7 MB | 79 kB
Progress (3): 1.8/2.0 MB | 0.1/1.7 MB | 79 kB
Progress (3): 1.8/2.0 MB | 0.1/1.7 MB | 79 kB
Progress (3): 1.8/2.0 MB | 0.1/1.7 MB | 79 kB
Progress (3): 1.9/2.0 MB | 0.1/1.7 MB | 79 kB
Progress (3): 1.9/2.0 MB | 0.1/1.7 MB | 79 kB
Progress (3): 1.9/2.0 MB | 0.1/1.7 MB | 79 kB
Progress (3): 1.9/2.0 MB | 0.1/1.7 MB | 79 kB
Progress (3): 1.9/2.0 MB | 0.1/1.7 MB | 79 kB
Progress (4): 1.9/2.0 MB | 0.1/1.7 MB | 79 kB | 16/598 kB
Progress (4): 1.9/2.0 MB | 0.1/1.7 MB | 79 kB | 16/598 kB
Progress (4): 1.9/2.0 MB | 0.1/1.7 MB | 79 kB | 33/598 kB
Progress (4): 1.9/2.0 MB | 0.1/1.7 MB | 79 kB | 33/598 kB
Progress (4): 1.9/2.0 MB | 0.1/1.7 MB | 79 kB | 49/598 kB
Progress (4): 1.9/2.0 MB | 0.1/1.7 MB | 79 kB | 49/598 kB
Progress (4): 2.0 MB | 0.1/1.7 MB | 79 kB | 49/598 kB    
Progress (4): 2.0 MB | 0.1/1.7 MB | 79 kB | 65/598 kB
Progress (4): 2.0 MB | 0.2/1.7 MB | 79 kB | 65/598 kB
Progress (4): 2.0 MB | 0.2/1.7 MB | 79 kB | 82/598 kB
Progress (4): 2.0 MB | 0.2/1.7 MB | 79 kB | 98/598 kB
Progress (4): 2.0 MB | 0.2/1.7 MB | 79 kB | 115/598 kB
Progress (4): 2.0 MB | 0.2/1.7 MB | 79 kB | 115/598 kB
Progress (4): 2.0 MB | 0.2/1.7 MB | 79 kB | 131/598 kB
Progress (4): 2.0 MB | 0.2/1.7 MB | 79 kB | 147/598 kB
Progress (4): 2.0 MB | 0.2/1.7 MB | 79 kB | 164/598 kB
Progress (4): 2.0 MB | 0.2/1.7 MB | 79 kB | 180/598 kB
Progress (4): 2.0 MB | 0.2/1.7 MB | 79 kB | 180/598 kB
Progress (4): 2.0 MB | 0.2/1.7 MB | 79 kB | 197/598 kB
Progress (4): 2.0 MB | 0.2/1.7 MB | 79 kB | 213/598 kB
Progress (4): 2.0 MB | 0.2/1.7 MB | 79 kB | 213/598 kB
Progress (4): 2.0 MB | 0.2/1.7 MB | 79 kB | 229/598 kB
                                                      
Downloaded from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.18.3/jackson-annotations-2.18.3.jar (79 kB at 365 kB/s)
Progress (3): 2.0 MB | 0.2/1.7 MB | 246/598 kB
                                              
Downloading from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.18.3/jackson-datatype-jsr310-2.18.3.jar
Progress (3): 2.0 MB | 0.2/1.7 MB | 262/598 kB
Progress (3): 2.0 MB | 0.2/1.7 MB | 278/598 kB
Progress (3): 2.0 MB | 0.2/1.7 MB | 278/598 kB
Progress (3): 2.0 MB | 0.2/1.7 MB | 295/598 kB
Progress (3): 2.0 MB | 0.2/1.7 MB | 311/598 kB
Progress (3): 2.0 MB | 0.2/1.7 MB | 328/598 kB
Progress (3): 2.0 MB | 0.2/1.7 MB | 328/598 kB
Progress (3): 2.0 MB | 0.2/1.7 MB | 344/598 kB
Progress (3): 2.0 MB | 0.2/1.7 MB | 360/598 kB
Progress (3): 2.0 MB | 0.2/1.7 MB | 377/598 kB
Progress (3): 2.0 MB | 0.2/1.7 MB | 381/598 kB
Progress (3): 2.0 MB | 0.3/1.7 MB | 381/598 kB
Progress (3): 2.0 MB | 0.3/1.7 MB | 397/598 kB
Progress (3): 2.0 MB | 0.3/1.7 MB | 414/598 kB
Progress (3): 2.0 MB | 0.3/1.7 MB | 430/598 kB
Progress (3): 2.0 MB | 0.3/1.7 MB | 446/598 kB
Progress (3): 2.0 MB | 0.3/1.7 MB | 446/598 kB
Progress (3): 2.0 MB | 0.3/1.7 MB | 463/598 kB
Progress (3): 2.0 MB | 0.3/1.7 MB | 479/598 kB
Progress (3): 2.0 MB | 0.3/1.7 MB | 496/598 kB
Progress (3): 2.0 MB | 0.3/1.7 MB | 512/598 kB
Progress (3): 2.0 MB | 0.3/1.7 MB | 528/598 kB
Progress (3): 2.0 MB | 0.3/1.7 MB | 545/598 kB
Progress (3): 2.0 MB | 0.3/1.7 MB | 561/598 kB
Progress (3): 2.0 MB | 0.3/1.7 MB | 577/598 kB
Progress (3): 2.0 MB | 0.3/1.7 MB | 594/598 kB
Progress (3): 2.0 MB | 0.3/1.7 MB | 598 kB    
Progress (3): 2.0 MB | 0.3/1.7 MB | 598 kB
Progress (3): 2.0 MB | 0.3/1.7 MB | 598 kB
Progress (3): 2.0 MB | 0.3/1.7 MB | 598 kB
Progress (4): 2.0 MB | 0.3/1.7 MB | 598 kB | 16/36 kB
Progress (4): 2.0 MB | 0.3/1.7 MB | 598 kB | 32/36 kB
Progress (4): 2.0 MB | 0.3/1.7 MB | 598 kB | 36 kB   
Progress (4): 2.0 MB | 0.3/1.7 MB | 598 kB | 36 kB
                                                  
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/spring-core/6.2.6/spring-core-6.2.6.jar (2.0 MB at 9.1 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/module/jackson-module-parameter-names/2.18.3/jackson-module-parameter-names-2.18.3.jar
Progress (3): 0.4/1.7 MB | 598 kB | 36 kB
Progress (3): 0.4/1.7 MB | 598 kB | 36 kB
Progress (3): 0.4/1.7 MB | 598 kB | 36 kB
Progress (3): 0.4/1.7 MB | 598 kB | 36 kB
Progress (3): 0.4/1.7 MB | 598 kB | 36 kB
Progress (3): 0.4/1.7 MB | 598 kB | 36 kB
Progress (4): 0.4/1.7 MB | 598 kB | 36 kB | 16/133 kB
Progress (4): 0.4/1.7 MB | 598 kB | 36 kB | 32/133 kB
Progress (4): 0.4/1.7 MB | 598 kB | 36 kB | 49/133 kB
Progress (4): 0.4/1.7 MB | 598 kB | 36 kB | 65/133 kB
Progress (4): 0.4/1.7 MB | 598 kB | 36 kB | 81/133 kB
                                                     
Downloaded from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/core/jackson-core/2.18.3/jackson-core-2.18.3.jar (598 kB at 2.6 MB/s)
Progress (3): 0.4/1.7 MB | 36 kB | 98/133 kB
                                            
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter-tomcat/3.4.5/spring-boot-starter-tomcat-3.4.5.jar
Progress (3): 0.4/1.7 MB | 36 kB | 114/133 kB
Progress (3): 0.5/1.7 MB | 36 kB | 114/133 kB
Progress (3): 0.5/1.7 MB | 36 kB | 130/133 kB
Progress (3): 0.5/1.7 MB | 36 kB | 133 kB    
                                         
Downloaded from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.18.3/jackson-datatype-jdk8-2.18.3.jar (36 kB at 157 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/tomcat/embed/tomcat-embed-core/10.1.40/tomcat-embed-core-10.1.40.jar
Progress (2): 0.5/1.7 MB | 133 kB
Progress (2): 0.5/1.7 MB | 133 kB
Progress (2): 0.5/1.7 MB | 133 kB
Progress (2): 0.5/1.7 MB | 133 kB
Progress (2): 0.5/1.7 MB | 133 kB
Progress (2): 0.6/1.7 MB | 133 kB
Progress (2): 0.6/1.7 MB | 133 kB
Progress (2): 0.6/1.7 MB | 133 kB
                                 
Downloaded from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.18.3/jackson-datatype-jsr310-2.18.3.jar (133 kB at 557 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/tomcat/embed/tomcat-embed-el/10.1.40/tomcat-embed-el-10.1.40.jar
Progress (1): 0.6/1.7 MB
Progress (1): 0.6/1.7 MB
Progress (1): 0.6/1.7 MB
Progress (1): 0.7/1.7 MB
Progress (1): 0.7/1.7 MB
Progress (1): 0.7/1.7 MB
Progress (2): 0.7/1.7 MB | 4.8 kB
Progress (3): 0.7/1.7 MB | 4.8 kB | 10 kB
Progress (3): 0.7/1.7 MB | 4.8 kB | 10 kB
Progress (3): 0.7/1.7 MB | 4.8 kB | 10 kB
Progress (3): 0.7/1.7 MB | 4.8 kB | 10 kB
Progress (3): 0.8/1.7 MB | 4.8 kB | 10 kB
Progress (3): 0.8/1.7 MB | 4.8 kB | 10 kB
Progress (3): 0.8/1.7 MB | 4.8 kB | 10 kB
Progress (4): 0.8/1.7 MB | 4.8 kB | 10 kB | 16/266 kB
                                                     
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter-tomcat/3.4.5/spring-boot-starter-tomcat-3.4.5.jar (4.8 kB at 19 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.40/tomcat-embed-websocket-10.1.40.jar
Progress (3): 0.8/1.7 MB | 10 kB | 32/266 kB
Progress (3): 0.8/1.7 MB | 10 kB | 32/266 kB
Progress (3): 0.8/1.7 MB | 10 kB | 49/266 kB
Progress (3): 0.8/1.7 MB | 10 kB | 65/266 kB
                                            
Downloaded from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/module/jackson-module-parameter-names/2.18.3/jackson-module-parameter-names-2.18.3.jar (10 kB at 42 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/spring-web/6.2.6/spring-web-6.2.6.jar
Progress (2): 0.8/1.7 MB | 65/266 kB
Progress (2): 0.8/1.7 MB | 81/266 kB
Progress (2): 0.8/1.7 MB | 98/266 kB
Progress (2): 0.8/1.7 MB | 114/266 kB
Progress (2): 0.8/1.7 MB | 114/266 kB
Progress (2): 0.8/1.7 MB | 130/266 kB
Progress (2): 0.8/1.7 MB | 147/266 kB
Progress (2): 0.8/1.7 MB | 163/266 kB
Progress (2): 0.8/1.7 MB | 180/266 kB
Progress (2): 0.8/1.7 MB | 196/266 kB
Progress (2): 0.9/1.7 MB | 196/266 kB
Progress (2): 0.9/1.7 MB | 212/266 kB
Progress (2): 0.9/1.7 MB | 229/266 kB
Progress (2): 0.9/1.7 MB | 229/266 kB
Progress (2): 0.9/1.7 MB | 245/266 kB
Progress (2): 0.9/1.7 MB | 262/266 kB
Progress (3): 0.9/1.7 MB | 262/266 kB | 0/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0/3.6 MB    
Progress (3): 0.9/1.7 MB | 266 kB | 0/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0.1/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0.1/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0.1/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0.1/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0.1/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0.1/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0.1/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0.2/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0.2/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0.2/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0.2/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0.2/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0.2/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0.2/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0.2/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0.3/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0.3/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0.3/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0.3/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0.3/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0.3/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0.4/3.6 MB
Progress (3): 0.9/1.7 MB | 266 kB | 0.4/3.6 MB
Progress (3): 1.0/1.7 MB | 266 kB | 0.4/3.6 MB
Progress (3): 1.0/1.7 MB | 266 kB | 0.4/3.6 MB
Progress (3): 1.0/1.7 MB | 266 kB | 0.4/3.6 MB
Progress (3): 1.0/1.7 MB | 266 kB | 0.4/3.6 MB
Progress (3): 1.0/1.7 MB | 266 kB | 0.4/3.6 MB
Progress (3): 1.0/1.7 MB | 266 kB | 0.4/3.6 MB
Progress (3): 1.0/1.7 MB | 266 kB | 0.5/3.6 MB
Progress (3): 1.0/1.7 MB | 266 kB | 0.5/3.6 MB
Progress (3): 1.0/1.7 MB | 266 kB | 0.5/3.6 MB
Progress (3): 1.0/1.7 MB | 266 kB | 0.5/3.6 MB
Progress (3): 1.0/1.7 MB | 266 kB | 0.5/3.6 MB
                                              
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/tomcat/embed/tomcat-embed-el/10.1.40/tomcat-embed-el-10.1.40.jar (266 kB at 1.0 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/spring-beans/6.2.6/spring-beans-6.2.6.jar
Progress (2): 1.0/1.7 MB | 0.5/3.6 MB
Progress (2): 1.0/1.7 MB | 0.5/3.6 MB
Progress (2): 1.0/1.7 MB | 0.5/3.6 MB
Progress (2): 1.0/1.7 MB | 0.6/3.6 MB
Progress (2): 1.0/1.7 MB | 0.6/3.6 MB
Progress (2): 1.0/1.7 MB | 0.6/3.6 MB
Progress (2): 1.0/1.7 MB | 0.6/3.6 MB
Progress (2): 1.0/1.7 MB | 0.6/3.6 MB
Progress (2): 1.0/1.7 MB | 0.6/3.6 MB
Progress (2): 1.0/1.7 MB | 0.6/3.6 MB
Progress (2): 1.0/1.7 MB | 0.6/3.6 MB
Progress (2): 1.0/1.7 MB | 0.7/3.6 MB
Progress (2): 1.0/1.7 MB | 0.7/3.6 MB
Progress (2): 1.0/1.7 MB | 0.7/3.6 MB
Progress (2): 1.0/1.7 MB | 0.7/3.6 MB
Progress (2): 1.0/1.7 MB | 0.7/3.6 MB
Progress (2): 1.1/1.7 MB | 0.7/3.6 MB
Progress (2): 1.1/1.7 MB | 0.7/3.6 MB
Progress (3): 1.1/1.7 MB | 0.7/3.6 MB | 16/282 kB
Progress (3): 1.1/1.7 MB | 0.7/3.6 MB | 16/282 kB
Progress (3): 1.1/1.7 MB | 0.7/3.6 MB | 32/282 kB
Progress (3): 1.1/1.7 MB | 0.8/3.6 MB | 32/282 kB
Progress (3): 1.1/1.7 MB | 0.8/3.6 MB | 32/282 kB
Progress (3): 1.1/1.7 MB | 0.8/3.6 MB | 49/282 kB
Progress (3): 1.1/1.7 MB | 0.8/3.6 MB | 49/282 kB
Progress (3): 1.1/1.7 MB | 0.8/3.6 MB | 49/282 kB
Progress (3): 1.1/1.7 MB | 0.8/3.6 MB | 49/282 kB
Progress (3): 1.1/1.7 MB | 0.8/3.6 MB | 65/282 kB
Progress (3): 1.1/1.7 MB | 0.8/3.6 MB | 65/282 kB
Progress (3): 1.1/1.7 MB | 0.8/3.6 MB | 81/282 kB
Progress (3): 1.1/1.7 MB | 0.8/3.6 MB | 81/282 kB
Progress (3): 1.1/1.7 MB | 0.8/3.6 MB | 98/282 kB
Progress (3): 1.1/1.7 MB | 0.8/3.6 MB | 98/282 kB
Progress (3): 1.1/1.7 MB | 0.8/3.6 MB | 114/282 kB
Progress (3): 1.1/1.7 MB | 0.9/3.6 MB | 114/282 kB
Progress (3): 1.1/1.7 MB | 0.9/3.6 MB | 114/282 kB
Progress (3): 1.1/1.7 MB | 0.9/3.6 MB | 130/282 kB
Progress (3): 1.1/1.7 MB | 0.9/3.6 MB | 130/282 kB
Progress (3): 1.1/1.7 MB | 0.9/3.6 MB | 147/282 kB
Progress (3): 1.1/1.7 MB | 0.9/3.6 MB | 147/282 kB
Progress (3): 1.1/1.7 MB | 0.9/3.6 MB | 163/282 kB
Progress (3): 1.1/1.7 MB | 0.9/3.6 MB | 163/282 kB
Progress (3): 1.1/1.7 MB | 0.9/3.6 MB | 180/282 kB
Progress (3): 1.1/1.7 MB | 0.9/3.6 MB | 180/282 kB
Progress (3): 1.1/1.7 MB | 0.9/3.6 MB | 196/282 kB
Progress (3): 1.1/1.7 MB | 0.9/3.6 MB | 212/282 kB
Progress (3): 1.1/1.7 MB | 0.9/3.6 MB | 212/282 kB
Progress (3): 1.1/1.7 MB | 0.9/3.6 MB | 212/282 kB
Progress (3): 1.1/1.7 MB | 0.9/3.6 MB | 229/282 kB
Progress (3): 1.1/1.7 MB | 1.0/3.6 MB | 229/282 kB
Progress (3): 1.1/1.7 MB | 1.0/3.6 MB | 245/282 kB
Progress (3): 1.1/1.7 MB | 1.0/3.6 MB | 245/282 kB
Progress (3): 1.1/1.7 MB | 1.0/3.6 MB | 262/282 kB
Progress (4): 1.1/1.7 MB | 1.0/3.6 MB | 262/282 kB | 0/2.1 MB
Progress (4): 1.1/1.7 MB | 1.0/3.6 MB | 262/282 kB | 0/2.1 MB
Progress (4): 1.1/1.7 MB | 1.0/3.6 MB | 278/282 kB | 0/2.1 MB
Progress (4): 1.1/1.7 MB | 1.0/3.6 MB | 278/282 kB | 0/2.1 MB
Progress (4): 1.1/1.7 MB | 1.0/3.6 MB | 282 kB | 0/2.1 MB    
Progress (4): 1.1/1.7 MB | 1.0/3.6 MB | 282 kB | 0/2.1 MB
Progress (4): 1.1/1.7 MB | 1.0/3.6 MB | 282 kB | 0/2.1 MB
Progress (4): 1.1/1.7 MB | 1.0/3.6 MB | 282 kB | 0/2.1 MB
Progress (4): 1.1/1.7 MB | 1.0/3.6 MB | 282 kB | 0/2.1 MB
Progress (4): 1.1/1.7 MB | 1.0/3.6 MB | 282 kB | 0/2.1 MB
Progress (4): 1.1/1.7 MB | 1.1/3.6 MB | 282 kB | 0/2.1 MB
Progress (4): 1.1/1.7 MB | 1.1/3.6 MB | 282 kB | 0/2.1 MB
Progress (4): 1.1/1.7 MB | 1.1/3.6 MB | 282 kB | 0.1/2.1 MB
Progress (4): 1.2/1.7 MB | 1.1/3.6 MB | 282 kB | 0.1/2.1 MB
Progress (4): 1.2/1.7 MB | 1.1/3.6 MB | 282 kB | 0.1/2.1 MB
Progress (4): 1.2/1.7 MB | 1.1/3.6 MB | 282 kB | 0.1/2.1 MB
Progress (4): 1.2/1.7 MB | 1.1/3.6 MB | 282 kB | 0.1/2.1 MB
Progress (4): 1.2/1.7 MB | 1.1/3.6 MB | 282 kB | 0.1/2.1 MB
Progress (4): 1.2/1.7 MB | 1.1/3.6 MB | 282 kB | 0.1/2.1 MB
Progress (4): 1.2/1.7 MB | 1.1/3.6 MB | 282 kB | 0.1/2.1 MB
Progress (4): 1.2/1.7 MB | 1.1/3.6 MB | 282 kB | 0.1/2.1 MB
Progress (4): 1.2/1.7 MB | 1.1/3.6 MB | 282 kB | 0.1/2.1 MB
Progress (4): 1.2/1.7 MB | 1.1/3.6 MB | 282 kB | 0.1/2.1 MB
Progress (4): 1.2/1.7 MB | 1.2/3.6 MB | 282 kB | 0.1/2.1 MB
Progress (4): 1.2/1.7 MB | 1.2/3.6 MB | 282 kB | 0.1/2.1 MB
Progress (4): 1.2/1.7 MB | 1.2/3.6 MB | 282 kB | 0.1/2.1 MB
Progress (4): 1.2/1.7 MB | 1.2/3.6 MB | 282 kB | 0.2/2.1 MB
Progress (4): 1.2/1.7 MB | 1.2/3.6 MB | 282 kB | 0.2/2.1 MB
Progress (4): 1.2/1.7 MB | 1.2/3.6 MB | 282 kB | 0.2/2.1 MB
Progress (4): 1.2/1.7 MB | 1.2/3.6 MB | 282 kB | 0.2/2.1 MB
Progress (4): 1.2/1.7 MB | 1.2/3.6 MB | 282 kB | 0.2/2.1 MB
Progress (4): 1.2/1.7 MB | 1.2/3.6 MB | 282 kB | 0.2/2.1 MB
Progress (4): 1.2/1.7 MB | 1.2/3.6 MB | 282 kB | 0.2/2.1 MB
Progress (4): 1.2/1.7 MB | 1.2/3.6 MB | 282 kB | 0.2/2.1 MB
Progress (4): 1.2/1.7 MB | 1.2/3.6 MB | 282 kB | 0.2/2.1 MB
Progress (4): 1.2/1.7 MB | 1.2/3.6 MB | 282 kB | 0.2/2.1 MB
Progress (4): 1.2/1.7 MB | 1.3/3.6 MB | 282 kB | 0.2/2.1 MB
Progress (4): 1.2/1.7 MB | 1.3/3.6 MB | 282 kB | 0.2/2.1 MB
Progress (4): 1.2/1.7 MB | 1.3/3.6 MB | 282 kB | 0.2/2.1 MB
Progress (4): 1.2/1.7 MB | 1.3/3.6 MB | 282 kB | 0.3/2.1 MB
Progress (4): 1.2/1.7 MB | 1.3/3.6 MB | 282 kB | 0.3/2.1 MB
Progress (4): 1.2/1.7 MB | 1.3/3.6 MB | 282 kB | 0.3/2.1 MB
Progress (4): 1.2/1.7 MB | 1.3/3.6 MB | 282 kB | 0.3/2.1 MB
Progress (4): 1.2/1.7 MB | 1.3/3.6 MB | 282 kB | 0.3/2.1 MB
Progress (4): 1.2/1.7 MB | 1.3/3.6 MB | 282 kB | 0.3/2.1 MB
Progress (4): 1.2/1.7 MB | 1.3/3.6 MB | 282 kB | 0.3/2.1 MB
Progress (4): 1.2/1.7 MB | 1.3/3.6 MB | 282 kB | 0.3/2.1 MB
Progress (4): 1.2/1.7 MB | 1.3/3.6 MB | 282 kB | 0.3/2.1 MB
Progress (4): 1.2/1.7 MB | 1.3/3.6 MB | 282 kB | 0.3/2.1 MB
Progress (4): 1.2/1.7 MB | 1.3/3.6 MB | 282 kB | 0.3/2.1 MB
Progress (4): 1.2/1.7 MB | 1.3/3.6 MB | 282 kB | 0.3/2.1 MB
Progress (4): 1.2/1.7 MB | 1.3/3.6 MB | 282 kB | 0.3/2.1 MB
Progress (4): 1.2/1.7 MB | 1.3/3.6 MB | 282 kB | 0.3/2.1 MB
Progress (4): 1.2/1.7 MB | 1.4/3.6 MB | 282 kB | 0.3/2.1 MB
Progress (4): 1.2/1.7 MB | 1.4/3.6 MB | 282 kB | 0.4/2.1 MB
Progress (4): 1.3/1.7 MB | 1.4/3.6 MB | 282 kB | 0.4/2.1 MB
Progress (4): 1.3/1.7 MB | 1.4/3.6 MB | 282 kB | 0.4/2.1 MB
Progress (4): 1.3/1.7 MB | 1.4/3.6 MB | 282 kB | 0.4/2.1 MB
Progress (4): 1.3/1.7 MB | 1.4/3.6 MB | 282 kB | 0.4/2.1 MB
Progress (4): 1.3/1.7 MB | 1.4/3.6 MB | 282 kB | 0.4/2.1 MB
Progress (5): 1.3/1.7 MB | 1.4/3.6 MB | 282 kB | 0.4/2.1 MB | 16/886 kB
Progress (5): 1.3/1.7 MB | 1.4/3.6 MB | 282 kB | 0.4/2.1 MB | 16/886 kB
Progress (5): 1.3/1.7 MB | 1.4/3.6 MB | 282 kB | 0.4/2.1 MB | 16/886 kB
Progress (5): 1.3/1.7 MB | 1.4/3.6 MB | 282 kB | 0.4/2.1 MB | 16/886 kB
Progress (5): 1.3/1.7 MB | 1.4/3.6 MB | 282 kB | 0.4/2.1 MB | 33/886 kB
Progress (5): 1.3/1.7 MB | 1.4/3.6 MB | 282 kB | 0.4/2.1 MB | 33/886 kB
Progress (5): 1.3/1.7 MB | 1.4/3.6 MB | 282 kB | 0.4/2.1 MB | 33/886 kB
Progress (5): 1.3/1.7 MB | 1.4/3.6 MB | 282 kB | 0.4/2.1 MB | 33/886 kB
Progress (5): 1.3/1.7 MB | 1.4/3.6 MB | 282 kB | 0.4/2.1 MB | 49/886 kB
Progress (5): 1.3/1.7 MB | 1.5/3.6 MB | 282 kB | 0.4/2.1 MB | 49/886 kB
Progress (5): 1.3/1.7 MB | 1.5/3.6 MB | 282 kB | 0.4/2.1 MB | 49/886 kB
Progress (5): 1.3/1.7 MB | 1.5/3.6 MB | 282 kB | 0.5/2.1 MB | 49/886 kB
Progress (5): 1.3/1.7 MB | 1.5/3.6 MB | 282 kB | 0.5/2.1 MB | 66/886 kB
Progress (5): 1.3/1.7 MB | 1.5/3.6 MB | 282 kB | 0.5/2.1 MB | 66/886 kB
Progress (5): 1.3/1.7 MB | 1.5/3.6 MB | 282 kB | 0.5/2.1 MB | 66/886 kB
Progress (5): 1.3/1.7 MB | 1.5/3.6 MB | 282 kB | 0.5/2.1 MB | 66/886 kB
Progress (5): 1.3/1.7 MB | 1.5/3.6 MB | 282 kB | 0.5/2.1 MB | 66/886 kB
Progress (5): 1.3/1.7 MB | 1.5/3.6 MB | 282 kB | 0.5/2.1 MB | 82/886 kB
Progress (5): 1.3/1.7 MB | 1.5/3.6 MB | 282 kB | 0.5/2.1 MB | 82/886 kB
Progress (5): 1.3/1.7 MB | 1.5/3.6 MB | 282 kB | 0.5/2.1 MB | 82/886 kB
Progress (5): 1.3/1.7 MB | 1.5/3.6 MB | 282 kB | 0.5/2.1 MB | 98/886 kB
Progress (5): 1.3/1.7 MB | 1.5/3.6 MB | 282 kB | 0.5/2.1 MB | 98/886 kB
Progress (5): 1.3/1.7 MB | 1.5/3.6 MB | 282 kB | 0.5/2.1 MB | 115/886 kB
Progress (5): 1.3/1.7 MB | 1.5/3.6 MB | 282 kB | 0.5/2.1 MB | 115/886 kB
Progress (5): 1.3/1.7 MB | 1.5/3.6 MB | 282 kB | 0.5/2.1 MB | 115/886 kB
Progress (5): 1.3/1.7 MB | 1.5/3.6 MB | 282 kB | 0.5/2.1 MB | 115/886 kB
Progress (5): 1.3/1.7 MB | 1.5/3.6 MB | 282 kB | 0.5/2.1 MB | 131/886 kB
Progress (5): 1.3/1.7 MB | 1.5/3.6 MB | 282 kB | 0.5/2.1 MB | 131/886 kB
Progress (5): 1.3/1.7 MB | 1.6/3.6 MB | 282 kB | 0.5/2.1 MB | 131/886 kB
Progress (5): 1.3/1.7 MB | 1.6/3.6 MB | 282 kB | 0.5/2.1 MB | 147/886 kB
Progress (5): 1.3/1.7 MB | 1.6/3.6 MB | 282 kB | 0.5/2.1 MB | 147/886 kB
Progress (5): 1.3/1.7 MB | 1.6/3.6 MB | 282 kB | 0.5/2.1 MB | 147/886 kB
Progress (5): 1.3/1.7 MB | 1.6/3.6 MB | 282 kB | 0.5/2.1 MB | 164/886 kB
Progress (5): 1.3/1.7 MB | 1.6/3.6 MB | 282 kB | 0.6/2.1 MB | 164/886 kB
Progress (5): 1.3/1.7 MB | 1.6/3.6 MB | 282 kB | 0.6/2.1 MB | 180/886 kB
Progress (5): 1.3/1.7 MB | 1.6/3.6 MB | 282 kB | 0.6/2.1 MB | 180/886 kB
Progress (5): 1.3/1.7 MB | 1.6/3.6 MB | 282 kB | 0.6/2.1 MB | 180/886 kB
Progress (5): 1.3/1.7 MB | 1.6/3.6 MB | 282 kB | 0.6/2.1 MB | 180/886 kB
Progress (5): 1.3/1.7 MB | 1.6/3.6 MB | 282 kB | 0.6/2.1 MB | 180/886 kB
Progress (5): 1.3/1.7 MB | 1.6/3.6 MB | 282 kB | 0.6/2.1 MB | 197/886 kB
Progress (5): 1.3/1.7 MB | 1.6/3.6 MB | 282 kB | 0.6/2.1 MB | 197/886 kB
Progress (5): 1.3/1.7 MB | 1.6/3.6 MB | 282 kB | 0.6/2.1 MB | 197/886 kB
Progress (5): 1.3/1.7 MB | 1.6/3.6 MB | 282 kB | 0.6/2.1 MB | 213/886 kB
Progress (5): 1.3/1.7 MB | 1.6/3.6 MB | 282 kB | 0.6/2.1 MB | 213/886 kB
Progress (5): 1.3/1.7 MB | 1.6/3.6 MB | 282 kB | 0.6/2.1 MB | 213/886 kB
Progress (5): 1.3/1.7 MB | 1.6/3.6 MB | 282 kB | 0.6/2.1 MB | 229/886 kB
                                                                        
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.40/tomcat-embed-websocket-10.1.40.jar (282 kB at 1.0 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/io/micrometer/micrometer-observation/1.14.6/micrometer-observation-1.14.6.jar
Progress (4): 1.3/1.7 MB | 1.6/3.6 MB | 0.6/2.1 MB | 229/886 kB
Progress (4): 1.3/1.7 MB | 1.7/3.6 MB | 0.6/2.1 MB | 229/886 kB
Progress (4): 1.3/1.7 MB | 1.7/3.6 MB | 0.7/2.1 MB | 229/886 kB
Progress (4): 1.3/1.7 MB | 1.7/3.6 MB | 0.7/2.1 MB | 246/886 kB
Progress (4): 1.3/1.7 MB | 1.7/3.6 MB | 0.7/2.1 MB | 256/886 kB
Progress (4): 1.3/1.7 MB | 1.7/3.6 MB | 0.7/2.1 MB | 256/886 kB
Progress (4): 1.3/1.7 MB | 1.7/3.6 MB | 0.7/2.1 MB | 256/886 kB
Progress (4): 1.3/1.7 MB | 1.7/3.6 MB | 0.7/2.1 MB | 256/886 kB
Progress (4): 1.3/1.7 MB | 1.7/3.6 MB | 0.7/2.1 MB | 272/886 kB
Progress (4): 1.3/1.7 MB | 1.7/3.6 MB | 0.7/2.1 MB | 272/886 kB
Progress (4): 1.3/1.7 MB | 1.7/3.6 MB | 0.7/2.1 MB | 272/886 kB
Progress (4): 1.3/1.7 MB | 1.7/3.6 MB | 0.7/2.1 MB | 289/886 kB
Progress (4): 1.3/1.7 MB | 1.7/3.6 MB | 0.7/2.1 MB | 289/886 kB
Progress (4): 1.3/1.7 MB | 1.7/3.6 MB | 0.7/2.1 MB | 305/886 kB
Progress (4): 1.3/1.7 MB | 1.7/3.6 MB | 0.7/2.1 MB | 305/886 kB
Progress (4): 1.3/1.7 MB | 1.7/3.6 MB | 0.7/2.1 MB | 305/886 kB
Progress (4): 1.3/1.7 MB | 1.7/3.6 MB | 0.7/2.1 MB | 321/886 kB
Progress (4): 1.3/1.7 MB | 1.7/3.6 MB | 0.7/2.1 MB | 321/886 kB
Progress (4): 1.3/1.7 MB | 1.7/3.6 MB | 0.8/2.1 MB | 321/886 kB
Progress (4): 1.3/1.7 MB | 1.7/3.6 MB | 0.8/2.1 MB | 338/886 kB
Progress (4): 1.3/1.7 MB | 1.7/3.6 MB | 0.8/2.1 MB | 338/886 kB
Progress (4): 1.3/1.7 MB | 1.7/3.6 MB | 0.8/2.1 MB | 338/886 kB
Progress (4): 1.3/1.7 MB | 1.7/3.6 MB | 0.8/2.1 MB | 354/886 kB
Progress (4): 1.3/1.7 MB | 1.7/3.6 MB | 0.8/2.1 MB | 354/886 kB
Progress (4): 1.3/1.7 MB | 1.8/3.6 MB | 0.8/2.1 MB | 354/886 kB
Progress (4): 1.3/1.7 MB | 1.8/3.6 MB | 0.8/2.1 MB | 370/886 kB
Progress (4): 1.3/1.7 MB | 1.8/3.6 MB | 0.8/2.1 MB | 370/886 kB
Progress (4): 1.3/1.7 MB | 1.8/3.6 MB | 0.8/2.1 MB | 387/886 kB
Progress (4): 1.3/1.7 MB | 1.8/3.6 MB | 0.8/2.1 MB | 387/886 kB
Progress (4): 1.3/1.7 MB | 1.8/3.6 MB | 0.8/2.1 MB | 403/886 kB
Progress (4): 1.3/1.7 MB | 1.8/3.6 MB | 0.8/2.1 MB | 403/886 kB
Progress (4): 1.3/1.7 MB | 1.8/3.6 MB | 0.8/2.1 MB | 420/886 kB
Progress (4): 1.4/1.7 MB | 1.8/3.6 MB | 0.8/2.1 MB | 420/886 kB
Progress (4): 1.4/1.7 MB | 1.8/3.6 MB | 0.8/2.1 MB | 420/886 kB
Progress (4): 1.4/1.7 MB | 1.8/3.6 MB | 0.8/2.1 MB | 436/886 kB
Progress (4): 1.4/1.7 MB | 1.8/3.6 MB | 0.8/2.1 MB | 436/886 kB
Progress (4): 1.4/1.7 MB | 1.8/3.6 MB | 0.8/2.1 MB | 436/886 kB
Progress (4): 1.4/1.7 MB | 1.8/3.6 MB | 0.8/2.1 MB | 436/886 kB
Progress (4): 1.4/1.7 MB | 1.8/3.6 MB | 0.8/2.1 MB | 452/886 kB
Progress (4): 1.4/1.7 MB | 1.8/3.6 MB | 0.8/2.1 MB | 452/886 kB
Progress (4): 1.4/1.7 MB | 1.8/3.6 MB | 0.8/2.1 MB | 452/886 kB
Progress (4): 1.4/1.7 MB | 1.8/3.6 MB | 0.8/2.1 MB | 469/886 kB
Progress (4): 1.4/1.7 MB | 1.8/3.6 MB | 0.8/2.1 MB | 469/886 kB
Progress (4): 1.4/1.7 MB | 1.8/3.6 MB | 0.8/2.1 MB | 469/886 kB
Progress (4): 1.4/1.7 MB | 1.8/3.6 MB | 0.8/2.1 MB | 485/886 kB
Progress (4): 1.4/1.7 MB | 1.8/3.6 MB | 0.9/2.1 MB | 485/886 kB
Progress (4): 1.4/1.7 MB | 1.8/3.6 MB | 0.9/2.1 MB | 502/886 kB
Progress (4): 1.4/1.7 MB | 1.8/3.6 MB | 0.9/2.1 MB | 502/886 kB
Progress (4): 1.4/1.7 MB | 1.8/3.6 MB | 0.9/2.1 MB | 502/886 kB
Progress (4): 1.4/1.7 MB | 1.8/3.6 MB | 0.9/2.1 MB | 518/886 kB
Progress (4): 1.4/1.7 MB | 1.8/3.6 MB | 0.9/2.1 MB | 518/886 kB
Progress (4): 1.4/1.7 MB | 1.8/3.6 MB | 0.9/2.1 MB | 518/886 kB
Progress (4): 1.4/1.7 MB | 1.8/3.6 MB | 0.9/2.1 MB | 518/886 kB
Progress (4): 1.4/1.7 MB | 1.8/3.6 MB | 0.9/2.1 MB | 518/886 kB
Progress (4): 1.4/1.7 MB | 1.9/3.6 MB | 0.9/2.1 MB | 518/886 kB
Progress (4): 1.4/1.7 MB | 1.9/3.6 MB | 0.9/2.1 MB | 518/886 kB
Progress (4): 1.4/1.7 MB | 1.9/3.6 MB | 0.9/2.1 MB | 518/886 kB
Progress (4): 1.4/1.7 MB | 1.9/3.6 MB | 0.9/2.1 MB | 518/886 kB
Progress (4): 1.4/1.7 MB | 1.9/3.6 MB | 0.9/2.1 MB | 518/886 kB
Progress (4): 1.4/1.7 MB | 1.9/3.6 MB | 0.9/2.1 MB | 518/886 kB
Progress (4): 1.4/1.7 MB | 1.9/3.6 MB | 0.9/2.1 MB | 518/886 kB
Progress (4): 1.4/1.7 MB | 1.9/3.6 MB | 0.9/2.1 MB | 518/886 kB
Progress (4): 1.4/1.7 MB | 1.9/3.6 MB | 1.0/2.1 MB | 518/886 kB
Progress (4): 1.4/1.7 MB | 1.9/3.6 MB | 1.0/2.1 MB | 534/886 kB
Progress (4): 1.4/1.7 MB | 1.9/3.6 MB | 1.0/2.1 MB | 534/886 kB
Progress (4): 1.4/1.7 MB | 1.9/3.6 MB | 1.0/2.1 MB | 534/886 kB
Progress (4): 1.4/1.7 MB | 1.9/3.6 MB | 1.0/2.1 MB | 551/886 kB
Progress (4): 1.4/1.7 MB | 1.9/3.6 MB | 1.0/2.1 MB | 551/886 kB
Progress (4): 1.4/1.7 MB | 1.9/3.6 MB | 1.0/2.1 MB | 551/886 kB
Progress (4): 1.4/1.7 MB | 1.9/3.6 MB | 1.0/2.1 MB | 567/886 kB
Progress (4): 1.4/1.7 MB | 1.9/3.6 MB | 1.0/2.1 MB | 567/886 kB
Progress (4): 1.4/1.7 MB | 1.9/3.6 MB | 1.0/2.1 MB | 567/886 kB
Progress (4): 1.4/1.7 MB | 1.9/3.6 MB | 1.0/2.1 MB | 583/886 kB
Progress (4): 1.4/1.7 MB | 1.9/3.6 MB | 1.0/2.1 MB | 583/886 kB
Progress (4): 1.4/1.7 MB | 2.0/3.6 MB | 1.0/2.1 MB | 583/886 kB
Progress (4): 1.4/1.7 MB | 2.0/3.6 MB | 1.0/2.1 MB | 600/886 kB
Progress (4): 1.4/1.7 MB | 2.0/3.6 MB | 1.0/2.1 MB | 600/886 kB
Progress (4): 1.4/1.7 MB | 2.0/3.6 MB | 1.0/2.1 MB | 600/886 kB
Progress (4): 1.4/1.7 MB | 2.0/3.6 MB | 1.0/2.1 MB | 616/886 kB
Progress (4): 1.5/1.7 MB | 2.0/3.6 MB | 1.0/2.1 MB | 616/886 kB
Progress (4): 1.5/1.7 MB | 2.0/3.6 MB | 1.1/2.1 MB | 616/886 kB
Progress (4): 1.5/1.7 MB | 2.0/3.6 MB | 1.1/2.1 MB | 616/886 kB
Progress (4): 1.5/1.7 MB | 2.0/3.6 MB | 1.1/2.1 MB | 633/886 kB
Progress (4): 1.5/1.7 MB | 2.0/3.6 MB | 1.1/2.1 MB | 633/886 kB
Progress (4): 1.5/1.7 MB | 2.0/3.6 MB | 1.1/2.1 MB | 633/886 kB
Progress (4): 1.5/1.7 MB | 2.0/3.6 MB | 1.1/2.1 MB | 633/886 kB
Progress (4): 1.5/1.7 MB | 2.0/3.6 MB | 1.1/2.1 MB | 633/886 kB
Progress (4): 1.5/1.7 MB | 2.0/3.6 MB | 1.1/2.1 MB | 649/886 kB
Progress (4): 1.5/1.7 MB | 2.0/3.6 MB | 1.1/2.1 MB | 649/886 kB
Progress (4): 1.5/1.7 MB | 2.0/3.6 MB | 1.1/2.1 MB | 649/886 kB
Progress (4): 1.5/1.7 MB | 2.0/3.6 MB | 1.1/2.1 MB | 665/886 kB
Progress (4): 1.5/1.7 MB | 2.0/3.6 MB | 1.1/2.1 MB | 665/886 kB
Progress (4): 1.5/1.7 MB | 2.1/3.6 MB | 1.1/2.1 MB | 665/886 kB
Progress (4): 1.5/1.7 MB | 2.1/3.6 MB | 1.1/2.1 MB | 682/886 kB
Progress (4): 1.5/1.7 MB | 2.1/3.6 MB | 1.1/2.1 MB | 682/886 kB
Progress (4): 1.5/1.7 MB | 2.1/3.6 MB | 1.1/2.1 MB | 698/886 kB
Progress (4): 1.5/1.7 MB | 2.1/3.6 MB | 1.1/2.1 MB | 698/886 kB
Progress (4): 1.5/1.7 MB | 2.1/3.6 MB | 1.1/2.1 MB | 715/886 kB
Progress (4): 1.5/1.7 MB | 2.1/3.6 MB | 1.1/2.1 MB | 715/886 kB
Progress (4): 1.5/1.7 MB | 2.1/3.6 MB | 1.1/2.1 MB | 715/886 kB
Progress (4): 1.5/1.7 MB | 2.1/3.6 MB | 1.1/2.1 MB | 731/886 kB
Progress (4): 1.5/1.7 MB | 2.1/3.6 MB | 1.1/2.1 MB | 731/886 kB
Progress (4): 1.5/1.7 MB | 2.1/3.6 MB | 1.2/2.1 MB | 731/886 kB
Progress (4): 1.5/1.7 MB | 2.1/3.6 MB | 1.2/2.1 MB | 747/886 kB
Progress (4): 1.5/1.7 MB | 2.1/3.6 MB | 1.2/2.1 MB | 747/886 kB
Progress (4): 1.5/1.7 MB | 2.1/3.6 MB | 1.2/2.1 MB | 747/886 kB
Progress (4): 1.5/1.7 MB | 2.1/3.6 MB | 1.2/2.1 MB | 764/886 kB
Progress (4): 1.5/1.7 MB | 2.1/3.6 MB | 1.2/2.1 MB | 764/886 kB
Progress (4): 1.5/1.7 MB | 2.1/3.6 MB | 1.2/2.1 MB | 780/886 kB
Progress (4): 1.5/1.7 MB | 2.1/3.6 MB | 1.2/2.1 MB | 780/886 kB
Progress (4): 1.5/1.7 MB | 2.1/3.6 MB | 1.2/2.1 MB | 780/886 kB
Progress (4): 1.5/1.7 MB | 2.1/3.6 MB | 1.2/2.1 MB | 796/886 kB
Progress (4): 1.5/1.7 MB | 2.1/3.6 MB | 1.2/2.1 MB | 796/886 kB
Progress (4): 1.5/1.7 MB | 2.2/3.6 MB | 1.2/2.1 MB | 796/886 kB
Progress (4): 1.5/1.7 MB | 2.2/3.6 MB | 1.2/2.1 MB | 796/886 kB
Progress (4): 1.5/1.7 MB | 2.2/3.6 MB | 1.2/2.1 MB | 813/886 kB
Progress (4): 1.5/1.7 MB | 2.2/3.6 MB | 1.2/2.1 MB | 813/886 kB
Progress (4): 1.5/1.7 MB | 2.2/3.6 MB | 1.2/2.1 MB | 813/886 kB
Progress (4): 1.5/1.7 MB | 2.2/3.6 MB | 1.2/2.1 MB | 829/886 kB
Progress (4): 1.5/1.7 MB | 2.2/3.6 MB | 1.2/2.1 MB | 829/886 kB
Progress (4): 1.5/1.7 MB | 2.2/3.6 MB | 1.2/2.1 MB | 829/886 kB
Progress (4): 1.5/1.7 MB | 2.2/3.6 MB | 1.2/2.1 MB | 841/886 kB
Progress (4): 1.5/1.7 MB | 2.2/3.6 MB | 1.2/2.1 MB | 841/886 kB
Progress (4): 1.5/1.7 MB | 2.2/3.6 MB | 1.2/2.1 MB | 841/886 kB
Progress (4): 1.5/1.7 MB | 2.2/3.6 MB | 1.3/2.1 MB | 841/886 kB
Progress (4): 1.5/1.7 MB | 2.2/3.6 MB | 1.3/2.1 MB | 857/886 kB
Progress (4): 1.5/1.7 MB | 2.2/3.6 MB | 1.3/2.1 MB | 857/886 kB
Progress (4): 1.5/1.7 MB | 2.2/3.6 MB | 1.3/2.1 MB | 857/886 kB
Progress (4): 1.5/1.7 MB | 2.2/3.6 MB | 1.3/2.1 MB | 857/886 kB
Progress (4): 1.5/1.7 MB | 2.2/3.6 MB | 1.3/2.1 MB | 873/886 kB
Progress (4): 1.5/1.7 MB | 2.2/3.6 MB | 1.3/2.1 MB | 873/886 kB
Progress (4): 1.5/1.7 MB | 2.2/3.6 MB | 1.3/2.1 MB | 873/886 kB
Progress (4): 1.5/1.7 MB | 2.2/3.6 MB | 1.3/2.1 MB | 886 kB    
Progress (4): 1.5/1.7 MB | 2.2/3.6 MB | 1.3/2.1 MB | 886 kB
Progress (4): 1.5/1.7 MB | 2.2/3.6 MB | 1.3/2.1 MB | 886 kB
Progress (4): 1.5/1.7 MB | 2.2/3.6 MB | 1.3/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.2/3.6 MB | 1.3/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.2/3.6 MB | 1.3/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.3/3.6 MB | 1.3/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.3/3.6 MB | 1.4/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.3/3.6 MB | 1.4/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.3/3.6 MB | 1.4/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.3/3.6 MB | 1.4/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.3/3.6 MB | 1.4/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.3/3.6 MB | 1.4/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.3/3.6 MB | 1.4/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.3/3.6 MB | 1.4/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.3/3.6 MB | 1.4/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.3/3.6 MB | 1.4/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.3/3.6 MB | 1.4/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.3/3.6 MB | 1.4/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.3/3.6 MB | 1.4/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.3/3.6 MB | 1.4/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.3/3.6 MB | 1.5/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.4/3.6 MB | 1.5/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.4/3.6 MB | 1.5/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.4/3.6 MB | 1.5/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.4/3.6 MB | 1.5/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.4/3.6 MB | 1.5/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.4/3.6 MB | 1.5/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.4/3.6 MB | 1.5/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.4/3.6 MB | 1.5/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.4/3.6 MB | 1.5/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.4/3.6 MB | 1.5/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.4/3.6 MB | 1.6/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.4/3.6 MB | 1.6/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.4/3.6 MB | 1.6/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.4/3.6 MB | 1.6/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.4/3.6 MB | 1.6/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.4/3.6 MB | 1.6/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.4/3.6 MB | 1.6/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.5/3.6 MB | 1.6/2.1 MB | 886 kB
Progress (4): 1.6/1.7 MB | 2.5/3.6 MB | 1.6/2.1 MB | 886 kB
Progress (4): 1.7/1.7 MB | 2.5/3.6 MB | 1.6/2.1 MB | 886 kB
Progress (4): 1.7/1.7 MB | 2.5/3.6 MB | 1.6/2.1 MB | 886 kB
Progress (4): 1.7/1.7 MB | 2.5/3.6 MB | 1.6/2.1 MB | 886 kB
Progress (4): 1.7 MB | 2.5/3.6 MB | 1.6/2.1 MB | 886 kB    
Progress (4): 1.7 MB | 2.5/3.6 MB | 1.6/2.1 MB | 886 kB
Progress (4): 1.7 MB | 2.5/3.6 MB | 1.6/2.1 MB | 886 kB
Progress (4): 1.7 MB | 2.5/3.6 MB | 1.7/2.1 MB | 886 kB
Progress (4): 1.7 MB | 2.5/3.6 MB | 1.7/2.1 MB | 886 kB
Progress (4): 1.7 MB | 2.5/3.6 MB | 1.7/2.1 MB | 886 kB
Progress (4): 1.7 MB | 2.5/3.6 MB | 1.7/2.1 MB | 886 kB
Progress (4): 1.7 MB | 2.5/3.6 MB | 1.7/2.1 MB | 886 kB
Progress (4): 1.7 MB | 2.5/3.6 MB | 1.7/2.1 MB | 886 kB
Progress (5): 1.7 MB | 2.5/3.6 MB | 1.7/2.1 MB | 886 kB | 16/75 kB
Progress (5): 1.7 MB | 2.5/3.6 MB | 1.7/2.1 MB | 886 kB | 16/75 kB
Progress (5): 1.7 MB | 2.6/3.6 MB | 1.7/2.1 MB | 886 kB | 16/75 kB
Progress (5): 1.7 MB | 2.6/3.6 MB | 1.7/2.1 MB | 886 kB | 16/75 kB
Progress (5): 1.7 MB | 2.6/3.6 MB | 1.7/2.1 MB | 886 kB | 16/75 kB
Progress (5): 1.7 MB | 2.6/3.6 MB | 1.7/2.1 MB | 886 kB | 32/75 kB
Progress (5): 1.7 MB | 2.6/3.6 MB | 1.7/2.1 MB | 886 kB | 32/75 kB
Progress (5): 1.7 MB | 2.6/3.6 MB | 1.7/2.1 MB | 886 kB | 32/75 kB
Progress (5): 1.7 MB | 2.6/3.6 MB | 1.7/2.1 MB | 886 kB | 49/75 kB
Progress (5): 1.7 MB | 2.6/3.6 MB | 1.8/2.1 MB | 886 kB | 49/75 kB
Progress (5): 1.7 MB | 2.6/3.6 MB | 1.8/2.1 MB | 886 kB | 49/75 kB
Progress (5): 1.7 MB | 2.6/3.6 MB | 1.8/2.1 MB | 886 kB | 49/75 kB
Progress (5): 1.7 MB | 2.6/3.6 MB | 1.8/2.1 MB | 886 kB | 63/75 kB
Progress (5): 1.7 MB | 2.6/3.6 MB | 1.8/2.1 MB | 886 kB | 63/75 kB
Progress (5): 1.7 MB | 2.6/3.6 MB | 1.8/2.1 MB | 886 kB | 63/75 kB
Progress (5): 1.7 MB | 2.6/3.6 MB | 1.8/2.1 MB | 886 kB | 75 kB   
Progress (5): 1.7 MB | 2.6/3.6 MB | 1.8/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.6/3.6 MB | 1.8/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.6/3.6 MB | 1.8/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.6/3.6 MB | 1.8/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.7/3.6 MB | 1.8/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.7/3.6 MB | 1.8/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.7/3.6 MB | 1.8/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.7/3.6 MB | 1.8/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.7/3.6 MB | 1.9/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.7/3.6 MB | 1.9/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.7/3.6 MB | 1.9/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.7/3.6 MB | 1.9/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.7/3.6 MB | 1.9/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.7/3.6 MB | 1.9/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.7/3.6 MB | 1.9/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.7/3.6 MB | 1.9/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.7/3.6 MB | 1.9/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.7/3.6 MB | 1.9/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.7/3.6 MB | 2.0/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.7/3.6 MB | 2.0/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.8/3.6 MB | 2.0/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.8/3.6 MB | 2.0/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.8/3.6 MB | 2.0/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.8/3.6 MB | 2.0/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.8/3.6 MB | 2.0/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.8/3.6 MB | 2.0/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.8/3.6 MB | 2.0/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.8/3.6 MB | 2.0/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.8/3.6 MB | 2.0/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.8/3.6 MB | 2.1/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.8/3.6 MB | 2.1/2.1 MB | 886 kB | 75 kB
Progress (5): 1.7 MB | 2.8/3.6 MB | 2.1/2.1 MB | 886 kB | 75 kB
                                                               
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/spring-beans/6.2.6/spring-beans-6.2.6.jar (886 kB at 2.9 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/io/micrometer/micrometer-commons/1.14.6/micrometer-commons-1.14.6.jar
Progress (4): 1.7 MB | 2.9/3.6 MB | 2.1/2.1 MB | 75 kB
Progress (4): 1.7 MB | 2.9/3.6 MB | 2.1 MB | 75 kB    
Progress (4): 1.7 MB | 2.9/3.6 MB | 2.1 MB | 75 kB
Progress (4): 1.7 MB | 2.9/3.6 MB | 2.1 MB | 75 kB
Progress (4): 1.7 MB | 2.9/3.6 MB | 2.1 MB | 75 kB
Progress (4): 1.7 MB | 2.9/3.6 MB | 2.1 MB | 75 kB
Progress (4): 1.7 MB | 2.9/3.6 MB | 2.1 MB | 75 kB
Progress (4): 1.7 MB | 3.0/3.6 MB | 2.1 MB | 75 kB
Progress (4): 1.7 MB | 3.0/3.6 MB | 2.1 MB | 75 kB
Progress (4): 1.7 MB | 3.0/3.6 MB | 2.1 MB | 75 kB
Progress (4): 1.7 MB | 3.0/3.6 MB | 2.1 MB | 75 kB
Progress (4): 1.7 MB | 3.0/3.6 MB | 2.1 MB | 75 kB
Progress (4): 1.7 MB | 3.0/3.6 MB | 2.1 MB | 75 kB
Progress (4): 1.7 MB | 3.1/3.6 MB | 2.1 MB | 75 kB
Progress (4): 1.7 MB | 3.1/3.6 MB | 2.1 MB | 75 kB
                                                  
Downloaded from central: https://repo.maven.apache.org/maven2/io/micrometer/micrometer-observation/1.14.6/micrometer-observation-1.14.6.jar (75 kB at 243 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/spring-webmvc/6.2.6/spring-webmvc-6.2.6.jar
Progress (3): 1.7 MB | 3.1/3.6 MB | 2.1 MB
Progress (3): 1.7 MB | 3.1/3.6 MB | 2.1 MB
Progress (3): 1.7 MB | 3.1/3.6 MB | 2.1 MB
Progress (3): 1.7 MB | 3.1/3.6 MB | 2.1 MB
Progress (3): 1.7 MB | 3.2/3.6 MB | 2.1 MB
Progress (3): 1.7 MB | 3.2/3.6 MB | 2.1 MB
Progress (3): 1.7 MB | 3.2/3.6 MB | 2.1 MB
Progress (3): 1.7 MB | 3.2/3.6 MB | 2.1 MB
Progress (3): 1.7 MB | 3.2/3.6 MB | 2.1 MB
                                          
Downloaded from central: https://repo.maven.apache.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.18.3/jackson-databind-2.18.3.jar (1.7 MB at 5.4 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/spring-aop/6.2.6/spring-aop-6.2.6.jar
Progress (2): 3.2/3.6 MB | 2.1 MB
Progress (2): 3.2/3.6 MB | 2.1 MB
Progress (2): 3.3/3.6 MB | 2.1 MB
Progress (2): 3.3/3.6 MB | 2.1 MB
Progress (2): 3.3/3.6 MB | 2.1 MB
Progress (2): 3.3/3.6 MB | 2.1 MB
Progress (2): 3.3/3.6 MB | 2.1 MB
Progress (2): 3.3/3.6 MB | 2.1 MB
Progress (2): 3.4/3.6 MB | 2.1 MB
Progress (2): 3.4/3.6 MB | 2.1 MB
Progress (2): 3.4/3.6 MB | 2.1 MB
Progress (2): 3.4/3.6 MB | 2.1 MB
Progress (2): 3.4/3.6 MB | 2.1 MB
Progress (2): 3.4/3.6 MB | 2.1 MB
Progress (2): 3.5/3.6 MB | 2.1 MB
Progress (2): 3.5/3.6 MB | 2.1 MB
Progress (2): 3.5/3.6 MB | 2.1 MB
Progress (2): 3.5/3.6 MB | 2.1 MB
Progress (2): 3.5/3.6 MB | 2.1 MB
Progress (2): 3.5/3.6 MB | 2.1 MB
Progress (2): 3.5/3.6 MB | 2.1 MB
Progress (2): 3.6/3.6 MB | 2.1 MB
Progress (2): 3.6/3.6 MB | 2.1 MB
Progress (2): 3.6/3.6 MB | 2.1 MB
Progress (2): 3.6/3.6 MB | 2.1 MB
Progress (2): 3.6/3.6 MB | 2.1 MB
Progress (2): 3.6 MB | 2.1 MB    
                             
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/spring-web/6.2.6/spring-web-6.2.6.jar (2.1 MB at 6.4 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/spring-expression/6.2.6/spring-expression-6.2.6.jar
Progress (2): 3.6 MB | 15/48 kB
Progress (2): 3.6 MB | 31/48 kB
Progress (2): 3.6 MB | 48/48 kB
Progress (2): 3.6 MB | 48 kB   
Progress (3): 3.6 MB | 48 kB | 0/1.1 MB
Progress (3): 3.6 MB | 48 kB | 0/1.1 MB
Progress (3): 3.6 MB | 48 kB | 0/1.1 MB
Progress (3): 3.6 MB | 48 kB | 0.1/1.1 MB
Progress (3): 3.6 MB | 48 kB | 0.1/1.1 MB
Progress (3): 3.6 MB | 48 kB | 0.1/1.1 MB
Progress (3): 3.6 MB | 48 kB | 0.1/1.1 MB
Progress (3): 3.6 MB | 48 kB | 0.1/1.1 MB
Progress (3): 3.6 MB | 48 kB | 0.1/1.1 MB
Progress (3): 3.6 MB | 48 kB | 0.2/1.1 MB
Progress (3): 3.6 MB | 48 kB | 0.2/1.1 MB
Progress (3): 3.6 MB | 48 kB | 0.2/1.1 MB
                                         
Downloaded from central: https://repo.maven.apache.org/maven2/io/micrometer/micrometer-commons/1.14.6/micrometer-commons-1.14.6.jar (48 kB at 147 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter-web-services/3.4.5/spring-boot-starter-web-services-3.4.5.jar
Progress (2): 3.6 MB | 0.2/1.1 MB
Progress (2): 3.6 MB | 0.2/1.1 MB
Progress (2): 3.6 MB | 0.2/1.1 MB
Progress (3): 3.6 MB | 0.2/1.1 MB | 16/420 kB
Progress (3): 3.6 MB | 0.3/1.1 MB | 16/420 kB
Progress (3): 3.6 MB | 0.3/1.1 MB | 16/420 kB
Progress (3): 3.6 MB | 0.3/1.1 MB | 32/420 kB
Progress (3): 3.6 MB | 0.3/1.1 MB | 49/420 kB
Progress (3): 3.6 MB | 0.3/1.1 MB | 49/420 kB
Progress (3): 3.6 MB | 0.3/1.1 MB | 49/420 kB
Progress (3): 3.6 MB | 0.3/1.1 MB | 65/420 kB
Progress (3): 3.6 MB | 0.3/1.1 MB | 65/420 kB
Progress (3): 3.6 MB | 0.3/1.1 MB | 65/420 kB
Progress (3): 3.6 MB | 0.3/1.1 MB | 81/420 kB
Progress (3): 3.6 MB | 0.4/1.1 MB | 81/420 kB
Progress (3): 3.6 MB | 0.4/1.1 MB | 81/420 kB
Progress (3): 3.6 MB | 0.4/1.1 MB | 81/420 kB
Progress (3): 3.6 MB | 0.4/1.1 MB | 81/420 kB
Progress (3): 3.6 MB | 0.4/1.1 MB | 98/420 kB
Progress (3): 3.6 MB | 0.4/1.1 MB | 98/420 kB
Progress (3): 3.6 MB | 0.4/1.1 MB | 114/420 kB
Progress (3): 3.6 MB | 0.4/1.1 MB | 114/420 kB
Progress (3): 3.6 MB | 0.5/1.1 MB | 114/420 kB
Progress (3): 3.6 MB | 0.5/1.1 MB | 130/420 kB
Progress (3): 3.6 MB | 0.5/1.1 MB | 130/420 kB
Progress (3): 3.6 MB | 0.5/1.1 MB | 130/420 kB
Progress (3): 3.6 MB | 0.5/1.1 MB | 130/420 kB
Progress (3): 3.6 MB | 0.5/1.1 MB | 130/420 kB
Progress (3): 3.6 MB | 0.5/1.1 MB | 147/420 kB
Progress (3): 3.6 MB | 0.5/1.1 MB | 147/420 kB
Progress (3): 3.6 MB | 0.5/1.1 MB | 147/420 kB
Progress (3): 3.6 MB | 0.6/1.1 MB | 147/420 kB
Progress (3): 3.6 MB | 0.6/1.1 MB | 163/420 kB
Progress (3): 3.6 MB | 0.6/1.1 MB | 163/420 kB
Progress (3): 3.6 MB | 0.6/1.1 MB | 180/420 kB
Progress (3): 3.6 MB | 0.6/1.1 MB | 180/420 kB
                                              
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/tomcat/embed/tomcat-embed-core/10.1.40/tomcat-embed-core-10.1.40.jar (3.6 MB at 11 MB/s)
Progress (2): 0.6/1.1 MB | 180/420 kB
                                     
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/xml/messaging/saaj/saaj-impl/3.0.4/saaj-impl-3.0.4.jar
Progress (2): 0.6/1.1 MB | 180/420 kB
Progress (2): 0.6/1.1 MB | 196/420 kB
Progress (2): 0.6/1.1 MB | 196/420 kB
Progress (3): 0.6/1.1 MB | 196/420 kB | 16/318 kB
Progress (3): 0.6/1.1 MB | 212/420 kB | 16/318 kB
Progress (3): 0.7/1.1 MB | 212/420 kB | 16/318 kB
Progress (3): 0.7/1.1 MB | 229/420 kB | 16/318 kB
Progress (3): 0.7/1.1 MB | 229/420 kB | 16/318 kB
Progress (3): 0.7/1.1 MB | 229/420 kB | 32/318 kB
Progress (3): 0.7/1.1 MB | 245/420 kB | 32/318 kB
Progress (3): 0.7/1.1 MB | 245/420 kB | 32/318 kB
Progress (3): 0.7/1.1 MB | 245/420 kB | 49/318 kB
Progress (3): 0.7/1.1 MB | 262/420 kB | 49/318 kB
Progress (3): 0.7/1.1 MB | 262/420 kB | 49/318 kB
Progress (3): 0.7/1.1 MB | 278/420 kB | 49/318 kB
Progress (3): 0.7/1.1 MB | 278/420 kB | 65/318 kB
Progress (3): 0.7/1.1 MB | 278/420 kB | 65/318 kB
Progress (3): 0.7/1.1 MB | 278/420 kB | 81/318 kB
Progress (3): 0.7/1.1 MB | 278/420 kB | 81/318 kB
Progress (3): 0.7/1.1 MB | 294/420 kB | 81/318 kB
Progress (3): 0.7/1.1 MB | 294/420 kB | 98/318 kB
Progress (3): 0.8/1.1 MB | 294/420 kB | 98/318 kB
Progress (3): 0.8/1.1 MB | 311/420 kB | 98/318 kB
Progress (3): 0.8/1.1 MB | 311/420 kB | 114/318 kB
Progress (3): 0.8/1.1 MB | 311/420 kB | 114/318 kB
Progress (3): 0.8/1.1 MB | 311/420 kB | 130/318 kB
Progress (3): 0.8/1.1 MB | 311/420 kB | 130/318 kB
Progress (3): 0.8/1.1 MB | 311/420 kB | 147/318 kB
Progress (3): 0.8/1.1 MB | 311/420 kB | 147/318 kB
Progress (3): 0.8/1.1 MB | 327/420 kB | 147/318 kB
Progress (3): 0.8/1.1 MB | 327/420 kB | 163/318 kB
Progress (3): 0.8/1.1 MB | 327/420 kB | 163/318 kB
Progress (3): 0.8/1.1 MB | 327/420 kB | 180/318 kB
Progress (3): 0.8/1.1 MB | 327/420 kB | 180/318 kB
Progress (3): 0.8/1.1 MB | 327/420 kB | 196/318 kB
Progress (3): 0.9/1.1 MB | 327/420 kB | 196/318 kB
Progress (3): 0.9/1.1 MB | 343/420 kB | 196/318 kB
Progress (3): 0.9/1.1 MB | 343/420 kB | 212/318 kB
Progress (3): 0.9/1.1 MB | 343/420 kB | 212/318 kB
Progress (3): 0.9/1.1 MB | 343/420 kB | 229/318 kB
Progress (3): 0.9/1.1 MB | 343/420 kB | 229/318 kB
Progress (3): 0.9/1.1 MB | 343/420 kB | 245/318 kB
Progress (3): 0.9/1.1 MB | 343/420 kB | 245/318 kB
Progress (3): 0.9/1.1 MB | 343/420 kB | 262/318 kB
Progress (3): 0.9/1.1 MB | 343/420 kB | 262/318 kB
Progress (3): 0.9/1.1 MB | 343/420 kB | 278/318 kB
Progress (3): 0.9/1.1 MB | 343/420 kB | 278/318 kB
Progress (3): 0.9/1.1 MB | 343/420 kB | 294/318 kB
Progress (3): 0.9/1.1 MB | 343/420 kB | 294/318 kB
Progress (3): 0.9/1.1 MB | 343/420 kB | 311/318 kB
Progress (3): 0.9/1.1 MB | 360/420 kB | 311/318 kB
Progress (3): 0.9/1.1 MB | 360/420 kB | 318 kB    
Progress (3): 1.0/1.1 MB | 360/420 kB | 318 kB
Progress (4): 1.0/1.1 MB | 360/420 kB | 318 kB | 4.8 kB
Progress (4): 1.0/1.1 MB | 376/420 kB | 318 kB | 4.8 kB
Progress (4): 1.0/1.1 MB | 376/420 kB | 318 kB | 4.8 kB
Progress (4): 1.0/1.1 MB | 393/420 kB | 318 kB | 4.8 kB
Progress (4): 1.0/1.1 MB | 393/420 kB | 318 kB | 4.8 kB
Progress (4): 1.0/1.1 MB | 393/420 kB | 318 kB | 4.8 kB
Progress (4): 1.0/1.1 MB | 402/420 kB | 318 kB | 4.8 kB
Progress (4): 1.0/1.1 MB | 402/420 kB | 318 kB | 4.8 kB
Progress (4): 1.0/1.1 MB | 402/420 kB | 318 kB | 4.8 kB
Progress (4): 1.0/1.1 MB | 418/420 kB | 318 kB | 4.8 kB
Progress (4): 1.0/1.1 MB | 420 kB | 318 kB | 4.8 kB    
Progress (4): 1.1/1.1 MB | 420 kB | 318 kB | 4.8 kB
Progress (4): 1.1/1.1 MB | 420 kB | 318 kB | 4.8 kB
Progress (4): 1.1 MB | 420 kB | 318 kB | 4.8 kB    
                                               
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter-web-services/3.4.5/spring-boot-starter-web-services-3.4.5.jar (4.8 kB at 13 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/jakarta/xml/soap/jakarta.xml.soap-api/3.0.2/jakarta.xml.soap-api-3.0.2.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/spring-expression/6.2.6/spring-expression-6.2.6.jar (318 kB at 893 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/jvnet/staxex/stax-ex/2.1.0/stax-ex-2.1.0.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/spring-aop/6.2.6/spring-aop-6.2.6.jar (420 kB at 1.2 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.jar
Progress (2): 1.1 MB | 16/462 kB
Progress (2): 1.1 MB | 32/462 kB
Progress (2): 1.1 MB | 49/462 kB
Progress (2): 1.1 MB | 65/462 kB
Progress (2): 1.1 MB | 81/462 kB
Progress (2): 1.1 MB | 98/462 kB
Progress (2): 1.1 MB | 114/462 kB
Progress (2): 1.1 MB | 130/462 kB
Progress (2): 1.1 MB | 147/462 kB
Progress (2): 1.1 MB | 163/462 kB
                                 
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/spring-webmvc/6.2.6/spring-webmvc-6.2.6.jar (1.1 MB at 3.1 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/jakarta/xml/ws/jakarta.xml.ws-api/4.0.2/jakarta.xml.ws-api-4.0.2.jar
Progress (1): 180/462 kB
Progress (1): 196/462 kB
Progress (1): 212/462 kB
Progress (1): 229/462 kB
Progress (1): 245/462 kB
Progress (1): 262/462 kB
Progress (1): 278/462 kB
Progress (1): 294/462 kB
Progress (1): 311/462 kB
Progress (1): 327/462 kB
Progress (1): 343/462 kB
Progress (1): 360/462 kB
Progress (1): 376/462 kB
Progress (1): 393/462 kB
Progress (1): 409/462 kB
Progress (1): 425/462 kB
Progress (1): 442/462 kB
Progress (1): 458/462 kB
Progress (1): 462 kB    
Progress (2): 462 kB | 16/27 kB
Progress (3): 462 kB | 16/27 kB | 16/38 kB
Progress (3): 462 kB | 27 kB | 16/38 kB   
Progress (3): 462 kB | 27 kB | 33/38 kB
Progress (3): 462 kB | 27 kB | 38 kB   
Progress (4): 462 kB | 27 kB | 38 kB | 15/39 kB
Progress (4): 462 kB | 27 kB | 38 kB | 31/39 kB
Progress (4): 462 kB | 27 kB | 38 kB | 39 kB   
                                            
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/xml/messaging/saaj/saaj-impl/3.0.4/saaj-impl-3.0.4.jar (462 kB at 1.2 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/spring-oxm/6.2.6/spring-oxm-6.2.6.jar
Progress (4): 27 kB | 38 kB | 39 kB | 16/78 kB
Progress (4): 27 kB | 38 kB | 39 kB | 33/78 kB
Progress (4): 27 kB | 38 kB | 39 kB | 49/78 kB
Progress (4): 27 kB | 38 kB | 39 kB | 66/78 kB
Progress (4): 27 kB | 38 kB | 39 kB | 78 kB   
                                           
Downloaded from central: https://repo.maven.apache.org/maven2/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.jar (27 kB at 70 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/ws/spring-ws-core/4.0.13/spring-ws-core-4.0.13.jar
Downloaded from central: https://repo.maven.apache.org/maven2/jakarta/xml/soap/jakarta.xml.soap-api/3.0.2/jakarta.xml.soap-api-3.0.2.jar (38 kB at 98 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/ws/spring-xml/4.0.13/spring-xml-4.0.13.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/jvnet/staxex/stax-ex/2.1.0/stax-ex-2.1.0.jar (39 kB at 100 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar
Downloaded from central: https://repo.maven.apache.org/maven2/jakarta/xml/ws/jakarta.xml.ws-api/4.0.2/jakarta.xml.ws-api-4.0.2.jar (78 kB at 201 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar
Progress (1): 16/60 kB
Progress (1): 33/60 kB
Progress (1): 49/60 kB
Progress (1): 60 kB   
Progress (2): 60 kB | 16/75 kB
Progress (3): 60 kB | 16/75 kB | 16/485 kB
Progress (3): 60 kB | 33/75 kB | 16/485 kB
Progress (3): 60 kB | 33/75 kB | 33/485 kB
Progress (3): 60 kB | 49/75 kB | 33/485 kB
Progress (3): 60 kB | 66/75 kB | 33/485 kB
Progress (4): 60 kB | 66/75 kB | 33/485 kB | 16/780 kB
Progress (4): 60 kB | 75 kB | 33/485 kB | 16/780 kB   
Progress (4): 60 kB | 75 kB | 33/485 kB | 33/780 kB
Progress (4): 60 kB | 75 kB | 49/485 kB | 33/780 kB
Progress (4): 60 kB | 75 kB | 49/485 kB | 49/780 kB
Progress (4): 60 kB | 75 kB | 64/485 kB | 49/780 kB
Progress (4): 60 kB | 75 kB | 80/485 kB | 49/780 kB
Progress (4): 60 kB | 75 kB | 80/485 kB | 62/780 kB
Progress (4): 60 kB | 75 kB | 96/485 kB | 62/780 kB
Progress (4): 60 kB | 75 kB | 96/485 kB | 79/780 kB
Progress (5): 60 kB | 75 kB | 96/485 kB | 79/780 kB | 16/328 kB
Progress (5): 60 kB | 75 kB | 113/485 kB | 79/780 kB | 16/328 kB
Progress (5): 60 kB | 75 kB | 113/485 kB | 95/780 kB | 16/328 kB
                                                                
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/spring-oxm/6.2.6/spring-oxm-6.2.6.jar (60 kB at 149 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/commons-logging/commons-logging/1.2/commons-logging-1.2.jar
Progress (4): 75 kB | 113/485 kB | 111/780 kB | 16/328 kB
Progress (4): 75 kB | 129/485 kB | 111/780 kB | 16/328 kB
Progress (4): 75 kB | 129/485 kB | 111/780 kB | 33/328 kB
Progress (4): 75 kB | 129/485 kB | 128/780 kB | 33/328 kB
Progress (4): 75 kB | 145/485 kB | 128/780 kB | 33/328 kB
Progress (4): 75 kB | 145/485 kB | 128/780 kB | 49/328 kB
Progress (4): 75 kB | 145/485 kB | 144/780 kB | 49/328 kB
Progress (4): 75 kB | 162/485 kB | 144/780 kB | 49/328 kB
Progress (4): 75 kB | 162/485 kB | 161/780 kB | 49/328 kB
Progress (4): 75 kB | 162/485 kB | 161/780 kB | 65/328 kB
Progress (4): 75 kB | 178/485 kB | 161/780 kB | 65/328 kB
Progress (4): 75 kB | 178/485 kB | 177/780 kB | 65/328 kB
Progress (4): 75 kB | 195/485 kB | 177/780 kB | 65/328 kB
Progress (4): 75 kB | 195/485 kB | 177/780 kB | 82/328 kB
Progress (4): 75 kB | 195/485 kB | 193/780 kB | 82/328 kB
Progress (4): 75 kB | 211/485 kB | 193/780 kB | 82/328 kB
Progress (4): 75 kB | 211/485 kB | 193/780 kB | 98/328 kB
Progress (4): 75 kB | 211/485 kB | 210/780 kB | 98/328 kB
Progress (4): 75 kB | 227/485 kB | 210/780 kB | 98/328 kB
Progress (4): 75 kB | 227/485 kB | 210/780 kB | 114/328 kB
Progress (4): 75 kB | 227/485 kB | 226/780 kB | 114/328 kB
Progress (4): 75 kB | 244/485 kB | 226/780 kB | 114/328 kB
Progress (4): 75 kB | 244/485 kB | 243/780 kB | 114/328 kB
Progress (4): 75 kB | 260/485 kB | 243/780 kB | 114/328 kB
Progress (4): 75 kB | 260/485 kB | 243/780 kB | 131/328 kB
Progress (4): 75 kB | 260/485 kB | 259/780 kB | 131/328 kB
Progress (4): 75 kB | 260/485 kB | 259/780 kB | 147/328 kB
Progress (4): 75 kB | 277/485 kB | 259/780 kB | 147/328 kB
Progress (4): 75 kB | 293/485 kB | 259/780 kB | 147/328 kB
Progress (4): 75 kB | 293/485 kB | 259/780 kB | 164/328 kB
Progress (4): 75 kB | 293/485 kB | 275/780 kB | 164/328 kB
Progress (4): 75 kB | 309/485 kB | 275/780 kB | 164/328 kB
Progress (4): 75 kB | 309/485 kB | 275/780 kB | 180/328 kB
Progress (4): 75 kB | 309/485 kB | 292/780 kB | 180/328 kB
Progress (4): 75 kB | 326/485 kB | 292/780 kB | 180/328 kB
Progress (4): 75 kB | 326/485 kB | 308/780 kB | 180/328 kB
Progress (4): 75 kB | 326/485 kB | 308/780 kB | 196/328 kB
Progress (4): 75 kB | 342/485 kB | 308/780 kB | 196/328 kB
Progress (4): 75 kB | 342/485 kB | 324/780 kB | 196/328 kB
Progress (4): 75 kB | 342/485 kB | 324/780 kB | 213/328 kB
Progress (4): 75 kB | 358/485 kB | 324/780 kB | 213/328 kB
Progress (4): 75 kB | 358/485 kB | 341/780 kB | 213/328 kB
Progress (4): 75 kB | 375/485 kB | 341/780 kB | 213/328 kB
Progress (4): 75 kB | 375/485 kB | 341/780 kB | 229/328 kB
Progress (4): 75 kB | 375/485 kB | 357/780 kB | 229/328 kB
Progress (4): 75 kB | 391/485 kB | 357/780 kB | 229/328 kB
Progress (4): 75 kB | 391/485 kB | 357/780 kB | 246/328 kB
Progress (4): 75 kB | 391/485 kB | 374/780 kB | 246/328 kB
Progress (4): 75 kB | 408/485 kB | 374/780 kB | 246/328 kB
Progress (4): 75 kB | 408/485 kB | 390/780 kB | 246/328 kB
Progress (4): 75 kB | 408/485 kB | 390/780 kB | 262/328 kB
Progress (4): 75 kB | 424/485 kB | 390/780 kB | 262/328 kB
Progress (4): 75 kB | 424/485 kB | 406/780 kB | 262/328 kB
Progress (4): 75 kB | 424/485 kB | 406/780 kB | 278/328 kB
Progress (4): 75 kB | 440/485 kB | 406/780 kB | 278/328 kB
Progress (4): 75 kB | 440/485 kB | 423/780 kB | 278/328 kB
Progress (4): 75 kB | 440/485 kB | 423/780 kB | 295/328 kB
Progress (4): 75 kB | 457/485 kB | 423/780 kB | 295/328 kB
Progress (4): 75 kB | 457/485 kB | 439/780 kB | 295/328 kB
Progress (4): 75 kB | 457/485 kB | 439/780 kB | 311/328 kB
Progress (4): 75 kB | 473/485 kB | 439/780 kB | 311/328 kB
Progress (4): 75 kB | 473/485 kB | 456/780 kB | 311/328 kB
Progress (4): 75 kB | 473/485 kB | 456/780 kB | 327/328 kB
Progress (4): 75 kB | 485 kB | 456/780 kB | 327/328 kB    
Progress (4): 75 kB | 485 kB | 472/780 kB | 327/328 kB
Progress (4): 75 kB | 485 kB | 472/780 kB | 328 kB    
                                                  
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/ws/spring-xml/4.0.13/spring-xml-4.0.13.jar (75 kB at 185 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/commons-codec/commons-codec/1.17.2/commons-codec-1.17.2.jar
Progress (3): 485 kB | 488/780 kB | 328 kB
Progress (3): 485 kB | 505/780 kB | 328 kB
Progress (3): 485 kB | 521/780 kB | 328 kB
Progress (3): 485 kB | 537/780 kB | 328 kB
Progress (3): 485 kB | 554/780 kB | 328 kB
Progress (3): 485 kB | 570/780 kB | 328 kB
Progress (3): 485 kB | 587/780 kB | 328 kB
Progress (3): 485 kB | 603/780 kB | 328 kB
Progress (3): 485 kB | 619/780 kB | 328 kB
Progress (3): 485 kB | 636/780 kB | 328 kB
Progress (3): 485 kB | 652/780 kB | 328 kB
Progress (3): 485 kB | 669/780 kB | 328 kB
Progress (3): 485 kB | 685/780 kB | 328 kB
Progress (3): 485 kB | 701/780 kB | 328 kB
Progress (3): 485 kB | 718/780 kB | 328 kB
Progress (3): 485 kB | 734/780 kB | 328 kB
Progress (3): 485 kB | 750/780 kB | 328 kB
Progress (3): 485 kB | 767/780 kB | 328 kB
Progress (3): 485 kB | 780 kB | 328 kB    
Progress (4): 485 kB | 780 kB | 328 kB | 16/62 kB
Progress (4): 485 kB | 780 kB | 328 kB | 33/62 kB
Progress (4): 485 kB | 780 kB | 328 kB | 49/62 kB
Progress (4): 485 kB | 780 kB | 328 kB | 62 kB   
                                              
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/ws/spring-ws-core/4.0.13/spring-ws-core-4.0.13.jar (485 kB at 1.2 MB/s)
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar (328 kB at 784 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar
Downloading from central: https://repo.maven.apache.org/maven2/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar
Progress (3): 780 kB | 62 kB | 16/373 kB
Progress (3): 780 kB | 62 kB | 33/373 kB
                                        
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar (780 kB at 1.9 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-impl/4.0.5/jaxb-impl-4.0.5.jar
Progress (2): 62 kB | 49/373 kB
                               
Downloaded from central: https://repo.maven.apache.org/maven2/commons-logging/commons-logging/1.2/commons-logging-1.2.jar (62 kB at 148 kB/s)
Progress (1): 62/373 kB
                       
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-core/4.0.5/jaxb-core-4.0.5.jar
Progress (1): 78/373 kB
Progress (1): 95/373 kB
Progress (1): 111/373 kB
Progress (1): 127/373 kB
Progress (1): 144/373 kB
Progress (1): 160/373 kB
Progress (1): 176/373 kB
Progress (1): 193/373 kB
Progress (1): 209/373 kB
Progress (1): 226/373 kB
Progress (1): 242/373 kB
Progress (1): 258/373 kB
Progress (1): 275/373 kB
Progress (1): 291/373 kB
Progress (1): 308/373 kB
Progress (1): 324/373 kB
Progress (1): 340/373 kB
Progress (1): 357/373 kB
Progress (1): 373 kB    
Progress (2): 373 kB | 16/131 kB
Progress (3): 373 kB | 16/131 kB | 16/67 kB
Progress (3): 373 kB | 33/131 kB | 16/67 kB
Progress (3): 373 kB | 33/131 kB | 33/67 kB
Progress (3): 373 kB | 49/131 kB | 33/67 kB
Progress (3): 373 kB | 49/131 kB | 49/67 kB
Progress (3): 373 kB | 62/131 kB | 49/67 kB
Progress (3): 373 kB | 62/131 kB | 66/67 kB
Progress (3): 373 kB | 62/131 kB | 67 kB   
Progress (3): 373 kB | 78/131 kB | 67 kB
Progress (3): 373 kB | 94/131 kB | 67 kB
Progress (3): 373 kB | 111/131 kB | 67 kB
Progress (3): 373 kB | 127/131 kB | 67 kB
Progress (3): 373 kB | 131 kB | 67 kB    
Progress (4): 373 kB | 131 kB | 67 kB | 16/235 kB
Progress (5): 373 kB | 131 kB | 67 kB | 16/235 kB | 16/923 kB
Progress (5): 373 kB | 131 kB | 67 kB | 33/235 kB | 16/923 kB
Progress (5): 373 kB | 131 kB | 67 kB | 49/235 kB | 16/923 kB
Progress (5): 373 kB | 131 kB | 67 kB | 49/235 kB | 32/923 kB
Progress (5): 373 kB | 131 kB | 67 kB | 62/235 kB | 32/923 kB
Progress (5): 373 kB | 131 kB | 67 kB | 62/235 kB | 49/923 kB
Progress (5): 373 kB | 131 kB | 67 kB | 79/235 kB | 49/923 kB
Progress (5): 373 kB | 131 kB | 67 kB | 79/235 kB | 65/923 kB
Progress (5): 373 kB | 131 kB | 67 kB | 79/235 kB | 81/923 kB
Progress (5): 373 kB | 131 kB | 67 kB | 95/235 kB | 81/923 kB
Progress (5): 373 kB | 131 kB | 67 kB | 95/235 kB | 98/923 kB
Progress (5): 373 kB | 131 kB | 67 kB | 111/235 kB | 98/923 kB
Progress (5): 373 kB | 131 kB | 67 kB | 111/235 kB | 114/923 kB
                                                               
Downloaded from central: https://repo.maven.apache.org/maven2/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar (67 kB at 153 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.jar
Progress (4): 373 kB | 131 kB | 111/235 kB | 130/923 kB
                                                       
Downloaded from central: https://repo.maven.apache.org/maven2/commons-codec/commons-codec/1.17.2/commons-codec-1.17.2.jar (373 kB at 859 kB/s)
Progress (3): 131 kB | 128/235 kB | 130/923 kB
                                              
Downloading from central: https://repo.maven.apache.org/maven2/org/glassfish/jaxb/jaxb-core/4.0.5/jaxb-core-4.0.5.jar
Progress (3): 131 kB | 128/235 kB | 147/923 kB
Progress (3): 131 kB | 128/235 kB | 163/923 kB
Progress (3): 131 kB | 128/235 kB | 180/923 kB
Progress (3): 131 kB | 144/235 kB | 180/923 kB
Progress (3): 131 kB | 144/235 kB | 196/923 kB
Progress (3): 131 kB | 161/235 kB | 196/923 kB
Progress (3): 131 kB | 161/235 kB | 209/923 kB
Progress (3): 131 kB | 161/235 kB | 225/923 kB
Progress (3): 131 kB | 161/235 kB | 242/923 kB
Progress (3): 131 kB | 177/235 kB | 242/923 kB
                                              
Downloaded from central: https://repo.maven.apache.org/maven2/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar (131 kB at 302 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/glassfish/jaxb/txw2/4.0.5/txw2-4.0.5.jar
Progress (2): 177/235 kB | 258/923 kB
Progress (2): 193/235 kB | 258/923 kB
Progress (2): 193/235 kB | 274/923 kB
Progress (2): 193/235 kB | 291/923 kB
Progress (2): 210/235 kB | 291/923 kB
Progress (2): 226/235 kB | 291/923 kB
Progress (2): 226/235 kB | 307/923 kB
Progress (2): 226/235 kB | 324/923 kB
Progress (2): 226/235 kB | 340/923 kB
Progress (2): 226/235 kB | 356/923 kB
Progress (2): 226/235 kB | 373/923 kB
Progress (2): 226/235 kB | 389/923 kB
Progress (2): 226/235 kB | 405/923 kB
Progress (2): 226/235 kB | 422/923 kB
Progress (2): 226/235 kB | 438/923 kB
Progress (2): 226/235 kB | 455/923 kB
Progress (2): 226/235 kB | 471/923 kB
Progress (2): 226/235 kB | 487/923 kB
Progress (2): 235 kB | 487/923 kB    
Progress (2): 235 kB | 504/923 kB
Progress (2): 235 kB | 520/923 kB
Progress (2): 235 kB | 537/923 kB
Progress (2): 235 kB | 553/923 kB
Progress (2): 235 kB | 569/923 kB
Progress (2): 235 kB | 586/923 kB
Progress (2): 235 kB | 602/923 kB
Progress (2): 235 kB | 618/923 kB
Progress (2): 235 kB | 635/923 kB
Progress (2): 235 kB | 651/923 kB
Progress (2): 235 kB | 668/923 kB
Progress (2): 235 kB | 684/923 kB
Progress (2): 235 kB | 700/923 kB
Progress (2): 235 kB | 717/923 kB
Progress (2): 235 kB | 733/923 kB
Progress (2): 235 kB | 750/923 kB
Progress (2): 235 kB | 766/923 kB
Progress (2): 235 kB | 782/923 kB
Progress (2): 235 kB | 799/923 kB
Progress (2): 235 kB | 815/923 kB
Progress (2): 235 kB | 831/923 kB
Progress (2): 235 kB | 848/923 kB
Progress (2): 235 kB | 864/923 kB
Progress (2): 235 kB | 881/923 kB
Progress (2): 235 kB | 897/923 kB
Progress (2): 235 kB | 913/923 kB
Progress (2): 235 kB | 923 kB    
Progress (3): 235 kB | 923 kB | 16/920 kB
Progress (3): 235 kB | 923 kB | 33/920 kB
Progress (3): 235 kB | 923 kB | 49/920 kB
Progress (3): 235 kB | 923 kB | 62/920 kB
                                         
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-core/4.0.5/jaxb-core-4.0.5.jar (235 kB at 523 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar
Progress (3): 923 kB | 62/920 kB | 16/73 kB
Progress (3): 923 kB | 79/920 kB | 16/73 kB
Progress (3): 923 kB | 95/920 kB | 16/73 kB
Progress (3): 923 kB | 95/920 kB | 33/73 kB
Progress (3): 923 kB | 111/920 kB | 33/73 kB
Progress (3): 923 kB | 111/920 kB | 49/73 kB
Progress (3): 923 kB | 128/920 kB | 49/73 kB
Progress (4): 923 kB | 128/920 kB | 49/73 kB | 16/139 kB
Progress (4): 923 kB | 128/920 kB | 66/73 kB | 16/139 kB
Progress (4): 923 kB | 128/920 kB | 73 kB | 16/139 kB   
Progress (4): 923 kB | 144/920 kB | 73 kB | 16/139 kB
Progress (4): 923 kB | 144/920 kB | 73 kB | 33/139 kB
Progress (4): 923 kB | 161/920 kB | 73 kB | 33/139 kB
Progress (4): 923 kB | 161/920 kB | 73 kB | 49/139 kB
Progress (4): 923 kB | 177/920 kB | 73 kB | 49/139 kB
Progress (4): 923 kB | 177/920 kB | 73 kB | 62/139 kB
Progress (4): 923 kB | 193/920 kB | 73 kB | 62/139 kB
Progress (4): 923 kB | 193/920 kB | 73 kB | 78/139 kB
Progress (4): 923 kB | 210/920 kB | 73 kB | 78/139 kB
Progress (4): 923 kB | 210/920 kB | 73 kB | 95/139 kB
Progress (4): 923 kB | 226/920 kB | 73 kB | 95/139 kB
Progress (4): 923 kB | 226/920 kB | 73 kB | 111/139 kB
Progress (4): 923 kB | 243/920 kB | 73 kB | 111/139 kB
Progress (4): 923 kB | 243/920 kB | 73 kB | 128/139 kB
Progress (4): 923 kB | 259/920 kB | 73 kB | 128/139 kB
Progress (4): 923 kB | 259/920 kB | 73 kB | 139 kB    
Progress (4): 923 kB | 275/920 kB | 73 kB | 139 kB
Progress (4): 923 kB | 292/920 kB | 73 kB | 139 kB
Progress (4): 923 kB | 308/920 kB | 73 kB | 139 kB
Progress (4): 923 kB | 324/920 kB | 73 kB | 139 kB
Progress (4): 923 kB | 341/920 kB | 73 kB | 139 kB
Progress (4): 923 kB | 357/920 kB | 73 kB | 139 kB
Progress (4): 923 kB | 374/920 kB | 73 kB | 139 kB
Progress (4): 923 kB | 390/920 kB | 73 kB | 139 kB
Progress (4): 923 kB | 406/920 kB | 73 kB | 139 kB
Progress (4): 923 kB | 423/920 kB | 73 kB | 139 kB
Progress (4): 923 kB | 439/920 kB | 73 kB | 139 kB
Progress (4): 923 kB | 456/920 kB | 73 kB | 139 kB
Progress (4): 923 kB | 472/920 kB | 73 kB | 139 kB
Progress (4): 923 kB | 488/920 kB | 73 kB | 139 kB
Progress (4): 923 kB | 505/920 kB | 73 kB | 139 kB
Progress (4): 923 kB | 521/920 kB | 73 kB | 139 kB
Progress (4): 923 kB | 537/920 kB | 73 kB | 139 kB
Progress (4): 923 kB | 554/920 kB | 73 kB | 139 kB
Progress (4): 923 kB | 570/920 kB | 73 kB | 139 kB
Progress (4): 923 kB | 587/920 kB | 73 kB | 139 kB
Progress (4): 923 kB | 603/920 kB | 73 kB | 139 kB
Progress (4): 923 kB | 619/920 kB | 73 kB | 139 kB
Progress (4): 923 kB | 636/920 kB | 73 kB | 139 kB
                                                  
Downloaded from central: https://repo.maven.apache.org/maven2/org/glassfish/jaxb/txw2/4.0.5/txw2-4.0.5.jar (73 kB at 158 kB/s)
Progress (3): 923 kB | 652/920 kB | 139 kB
                                          
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/activation/jakarta.activation/2.0.1/jakarta.activation-2.0.1.jar
Progress (3): 923 kB | 669/920 kB | 139 kB
Progress (3): 923 kB | 685/920 kB | 139 kB
Progress (3): 923 kB | 701/920 kB | 139 kB
Progress (3): 923 kB | 718/920 kB | 139 kB
Progress (3): 923 kB | 734/920 kB | 139 kB
Progress (3): 923 kB | 750/920 kB | 139 kB
                                          
Downloaded from central: https://repo.maven.apache.org/maven2/org/glassfish/jaxb/jaxb-core/4.0.5/jaxb-core-4.0.5.jar (139 kB at 298 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar
Progress (2): 923 kB | 767/920 kB
Progress (2): 923 kB | 783/920 kB
Progress (2): 923 kB | 800/920 kB
Progress (2): 923 kB | 816/920 kB
                                 
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-impl/4.0.5/jaxb-impl-4.0.5.jar (923 kB at 2.0 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter-test/3.4.5/spring-boot-starter-test-3.4.5.jar
Progress (1): 832/920 kB
Progress (1): 849/920 kB
Progress (1): 865/920 kB
Progress (1): 882/920 kB
Progress (1): 898/920 kB
Progress (1): 914/920 kB
Progress (1): 920 kB    
Progress (2): 920 kB | 16/26 kB
Progress (2): 920 kB | 26 kB   
                            
Downloaded from central: https://repo.maven.apache.org/maven2/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.jar (920 kB at 1.9 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-test/3.4.5/spring-boot-test-3.4.5.jar
Progress (2): 26 kB | 16/62 kB
Progress (2): 26 kB | 33/62 kB
                              
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar (26 kB at 54 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-test-autoconfigure/3.4.5/spring-boot-test-autoconfigure-3.4.5.jar
Progress (1): 49/62 kB
Progress (1): 62 kB   
Progress (2): 62 kB | 0/2.1 MB
Progress (2): 62 kB | 0/2.1 MB
Progress (3): 62 kB | 0/2.1 MB | 4.8 kB
Progress (3): 62 kB | 0/2.1 MB | 4.8 kB
Progress (3): 62 kB | 0.1/2.1 MB | 4.8 kB
Progress (3): 62 kB | 0.1/2.1 MB | 4.8 kB
Progress (3): 62 kB | 0.1/2.1 MB | 4.8 kB
Progress (3): 62 kB | 0.1/2.1 MB | 4.8 kB
Progress (3): 62 kB | 0.1/2.1 MB | 4.8 kB
Progress (3): 62 kB | 0.1/2.1 MB | 4.8 kB
Progress (3): 62 kB | 0.2/2.1 MB | 4.8 kB
Progress (3): 62 kB | 0.2/2.1 MB | 4.8 kB
Progress (3): 62 kB | 0.2/2.1 MB | 4.8 kB
Progress (3): 62 kB | 0.2/2.1 MB | 4.8 kB
Progress (3): 62 kB | 0.2/2.1 MB | 4.8 kB
Progress (3): 62 kB | 0.2/2.1 MB | 4.8 kB
Progress (3): 62 kB | 0.3/2.1 MB | 4.8 kB
Progress (3): 62 kB | 0.3/2.1 MB | 4.8 kB
Progress (3): 62 kB | 0.3/2.1 MB | 4.8 kB
Progress (3): 62 kB | 0.3/2.1 MB | 4.8 kB
Progress (3): 62 kB | 0.3/2.1 MB | 4.8 kB
Progress (3): 62 kB | 0.3/2.1 MB | 4.8 kB
Progress (3): 62 kB | 0.4/2.1 MB | 4.8 kB
Progress (3): 62 kB | 0.4/2.1 MB | 4.8 kB
Progress (3): 62 kB | 0.4/2.1 MB | 4.8 kB
                                         
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/activation/jakarta.activation/2.0.1/jakarta.activation-2.0.1.jar (62 kB at 125 kB/s)
Progress (2): 0.4/2.1 MB | 4.8 kB
                                 
Downloading from central: https://repo.maven.apache.org/maven2/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar
Progress (2): 0.4/2.1 MB | 4.8 kB
Progress (2): 0.4/2.1 MB | 4.8 kB
Progress (2): 0.5/2.1 MB | 4.8 kB
Progress (2): 0.5/2.1 MB | 4.8 kB
                                 
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-starter-test/3.4.5/spring-boot-starter-test-3.4.5.jar (4.8 kB at 9.7 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar
Progress (1): 0.5/2.1 MB
Progress (1): 0.5/2.1 MB
Progress (1): 0.5/2.1 MB
Progress (1): 0.5/2.1 MB
Progress (1): 0.5/2.1 MB
Progress (1): 0.6/2.1 MB
Progress (1): 0.6/2.1 MB
Progress (1): 0.6/2.1 MB
Progress (1): 0.6/2.1 MB
Progress (1): 0.6/2.1 MB
Progress (1): 0.6/2.1 MB
Progress (1): 0.7/2.1 MB
Progress (1): 0.7/2.1 MB
Progress (2): 0.7/2.1 MB | 16/254 kB
Progress (2): 0.7/2.1 MB | 16/254 kB
Progress (2): 0.7/2.1 MB | 33/254 kB
Progress (2): 0.7/2.1 MB | 33/254 kB
Progress (2): 0.7/2.1 MB | 49/254 kB
Progress (2): 0.7/2.1 MB | 49/254 kB
Progress (2): 0.7/2.1 MB | 63/254 kB
Progress (2): 0.7/2.1 MB | 63/254 kB
Progress (2): 0.7/2.1 MB | 80/254 kB
Progress (2): 0.8/2.1 MB | 80/254 kB
Progress (2): 0.8/2.1 MB | 80/254 kB
Progress (2): 0.8/2.1 MB | 96/254 kB
Progress (2): 0.8/2.1 MB | 96/254 kB
Progress (2): 0.8/2.1 MB | 112/254 kB
Progress (2): 0.8/2.1 MB | 129/254 kB
Progress (2): 0.8/2.1 MB | 129/254 kB
Progress (2): 0.8/2.1 MB | 145/254 kB
Progress (2): 0.8/2.1 MB | 145/254 kB
Progress (2): 0.8/2.1 MB | 161/254 kB
Progress (2): 0.8/2.1 MB | 161/254 kB
Progress (2): 0.8/2.1 MB | 178/254 kB
Progress (2): 0.8/2.1 MB | 194/254 kB
Progress (3): 0.8/2.1 MB | 194/254 kB | 16/227 kB
Progress (3): 0.9/2.1 MB | 194/254 kB | 16/227 kB
Progress (3): 0.9/2.1 MB | 211/254 kB | 16/227 kB
Progress (3): 0.9/2.1 MB | 211/254 kB | 16/227 kB
Progress (3): 0.9/2.1 MB | 211/254 kB | 33/227 kB
Progress (3): 0.9/2.1 MB | 227/254 kB | 33/227 kB
Progress (3): 0.9/2.1 MB | 227/254 kB | 33/227 kB
Progress (3): 0.9/2.1 MB | 227/254 kB | 49/227 kB
Progress (3): 0.9/2.1 MB | 243/254 kB | 49/227 kB
Progress (3): 0.9/2.1 MB | 243/254 kB | 49/227 kB
Progress (3): 0.9/2.1 MB | 254 kB | 49/227 kB    
Progress (3): 0.9/2.1 MB | 254 kB | 66/227 kB
Progress (3): 0.9/2.1 MB | 254 kB | 66/227 kB
Progress (3): 0.9/2.1 MB | 254 kB | 66/227 kB
Progress (3): 0.9/2.1 MB | 254 kB | 82/227 kB
Progress (3): 1.0/2.1 MB | 254 kB | 82/227 kB
Progress (3): 1.0/2.1 MB | 254 kB | 82/227 kB
Progress (3): 1.0/2.1 MB | 254 kB | 98/227 kB
Progress (3): 1.0/2.1 MB | 254 kB | 98/227 kB
Progress (3): 1.0/2.1 MB | 254 kB | 98/227 kB
Progress (3): 1.0/2.1 MB | 254 kB | 115/227 kB
Progress (3): 1.0/2.1 MB | 254 kB | 115/227 kB
Progress (3): 1.0/2.1 MB | 254 kB | 131/227 kB
Progress (3): 1.0/2.1 MB | 254 kB | 147/227 kB
Progress (3): 1.0/2.1 MB | 254 kB | 164/227 kB
Progress (3): 1.0/2.1 MB | 254 kB | 180/227 kB
Progress (3): 1.0/2.1 MB | 254 kB | 197/227 kB
Progress (3): 1.0/2.1 MB | 254 kB | 213/227 kB
Progress (3): 1.0/2.1 MB | 254 kB | 227 kB    
Progress (3): 1.0/2.1 MB | 254 kB | 227 kB
Progress (4): 1.0/2.1 MB | 254 kB | 227 kB | 16/277 kB
Progress (4): 1.1/2.1 MB | 254 kB | 227 kB | 16/277 kB
Progress (4): 1.1/2.1 MB | 254 kB | 227 kB | 16/277 kB
Progress (4): 1.1/2.1 MB | 254 kB | 227 kB | 33/277 kB
Progress (4): 1.1/2.1 MB | 254 kB | 227 kB | 33/277 kB
Progress (4): 1.1/2.1 MB | 254 kB | 227 kB | 49/277 kB
Progress (4): 1.1/2.1 MB | 254 kB | 227 kB | 49/277 kB
Progress (4): 1.1/2.1 MB | 254 kB | 227 kB | 65/277 kB
Progress (4): 1.1/2.1 MB | 254 kB | 227 kB | 65/277 kB
Progress (4): 1.1/2.1 MB | 254 kB | 227 kB | 82/277 kB
Progress (4): 1.1/2.1 MB | 254 kB | 227 kB | 82/277 kB
Progress (4): 1.2/2.1 MB | 254 kB | 227 kB | 82/277 kB
Progress (4): 1.2/2.1 MB | 254 kB | 227 kB | 82/277 kB
Progress (4): 1.2/2.1 MB | 254 kB | 227 kB | 98/277 kB
Progress (4): 1.2/2.1 MB | 254 kB | 227 kB | 98/277 kB
Progress (5): 1.2/2.1 MB | 254 kB | 227 kB | 98/277 kB | 16/70 kB
Progress (5): 1.2/2.1 MB | 254 kB | 227 kB | 114/277 kB | 16/70 kB
Progress (5): 1.2/2.1 MB | 254 kB | 227 kB | 114/277 kB | 16/70 kB
Progress (5): 1.2/2.1 MB | 254 kB | 227 kB | 114/277 kB | 32/70 kB
Progress (5): 1.2/2.1 MB | 254 kB | 227 kB | 114/277 kB | 32/70 kB
Progress (5): 1.2/2.1 MB | 254 kB | 227 kB | 131/277 kB | 32/70 kB
Progress (5): 1.2/2.1 MB | 254 kB | 227 kB | 131/277 kB | 49/70 kB
Progress (5): 1.2/2.1 MB | 254 kB | 227 kB | 131/277 kB | 49/70 kB
Progress (5): 1.2/2.1 MB | 254 kB | 227 kB | 131/277 kB | 65/70 kB
Progress (5): 1.3/2.1 MB | 254 kB | 227 kB | 131/277 kB | 65/70 kB
Progress (5): 1.3/2.1 MB | 254 kB | 227 kB | 131/277 kB | 70 kB   
Progress (5): 1.3/2.1 MB | 254 kB | 227 kB | 131/277 kB | 70 kB
Progress (5): 1.3/2.1 MB | 254 kB | 227 kB | 131/277 kB | 70 kB
Progress (5): 1.3/2.1 MB | 254 kB | 227 kB | 147/277 kB | 70 kB
Progress (5): 1.3/2.1 MB | 254 kB | 227 kB | 147/277 kB | 70 kB
Progress (5): 1.3/2.1 MB | 254 kB | 227 kB | 164/277 kB | 70 kB
Progress (5): 1.3/2.1 MB | 254 kB | 227 kB | 164/277 kB | 70 kB
Progress (5): 1.3/2.1 MB | 254 kB | 227 kB | 164/277 kB | 70 kB
Progress (5): 1.3/2.1 MB | 254 kB | 227 kB | 180/277 kB | 70 kB
Progress (5): 1.4/2.1 MB | 254 kB | 227 kB | 180/277 kB | 70 kB
                                                               
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-test/3.4.5/spring-boot-test-3.4.5.jar (254 kB at 496 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar
Progress (4): 1.4/2.1 MB | 227 kB | 180/277 kB | 70 kB
Progress (4): 1.4/2.1 MB | 227 kB | 196/277 kB | 70 kB
Progress (4): 1.4/2.1 MB | 227 kB | 196/277 kB | 70 kB
Progress (4): 1.4/2.1 MB | 227 kB | 196/277 kB | 70 kB
Progress (4): 1.4/2.1 MB | 227 kB | 213/277 kB | 70 kB
Progress (4): 1.4/2.1 MB | 227 kB | 213/277 kB | 70 kB
Progress (4): 1.4/2.1 MB | 227 kB | 229/277 kB | 70 kB
Progress (4): 1.4/2.1 MB | 227 kB | 229/277 kB | 70 kB
Progress (4): 1.4/2.1 MB | 227 kB | 229/277 kB | 70 kB
Progress (4): 1.5/2.1 MB | 227 kB | 229/277 kB | 70 kB
Progress (4): 1.5/2.1 MB | 227 kB | 245/277 kB | 70 kB
Progress (4): 1.5/2.1 MB | 227 kB | 245/277 kB | 70 kB
Progress (4): 1.5/2.1 MB | 227 kB | 245/277 kB | 70 kB
Progress (4): 1.5/2.1 MB | 227 kB | 245/277 kB | 70 kB
Progress (4): 1.5/2.1 MB | 227 kB | 245/277 kB | 70 kB
Progress (4): 1.5/2.1 MB | 227 kB | 245/277 kB | 70 kB
                                                      
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-test-autoconfigure/3.4.5/spring-boot-test-autoconfigure-3.4.5.jar (227 kB at 443 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar
Progress (3): 1.6/2.1 MB | 245/277 kB | 70 kB
Progress (3): 1.6/2.1 MB | 245/277 kB | 70 kB
Progress (3): 1.6/2.1 MB | 245/277 kB | 70 kB
Progress (3): 1.6/2.1 MB | 245/277 kB | 70 kB
Progress (3): 1.6/2.1 MB | 245/277 kB | 70 kB
Progress (3): 1.6/2.1 MB | 262/277 kB | 70 kB
Progress (3): 1.6/2.1 MB | 262/277 kB | 70 kB
Progress (3): 1.6/2.1 MB | 277 kB | 70 kB    
Progress (3): 1.7/2.1 MB | 277 kB | 70 kB
Progress (3): 1.7/2.1 MB | 277 kB | 70 kB
                                         
Downloaded from central: https://repo.maven.apache.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar (70 kB at 137 kB/s)
Progress (2): 1.7/2.1 MB | 277 kB
                                 
Downloading from central: https://repo.maven.apache.org/maven2/org/ow2/asm/asm/9.7.1/asm-9.7.1.jar
Progress (2): 1.7/2.1 MB | 277 kB
Progress (2): 1.7/2.1 MB | 277 kB
Progress (2): 1.7/2.1 MB | 277 kB
Progress (2): 1.8/2.1 MB | 277 kB
Progress (2): 1.8/2.1 MB | 277 kB
Progress (2): 1.8/2.1 MB | 277 kB
Progress (2): 1.8/2.1 MB | 277 kB
Progress (2): 1.8/2.1 MB | 277 kB
Progress (2): 1.8/2.1 MB | 277 kB
Progress (2): 1.9/2.1 MB | 277 kB
Progress (2): 1.9/2.1 MB | 277 kB
Progress (2): 1.9/2.1 MB | 277 kB
Progress (2): 1.9/2.1 MB | 277 kB
Progress (2): 1.9/2.1 MB | 277 kB
Progress (2): 1.9/2.1 MB | 277 kB
Progress (2): 1.9/2.1 MB | 277 kB
Progress (2): 2.0/2.1 MB | 277 kB
Progress (2): 2.0/2.1 MB | 277 kB
Progress (2): 2.0/2.1 MB | 277 kB
Progress (2): 2.0/2.1 MB | 277 kB
Progress (2): 2.0/2.1 MB | 277 kB
Progress (2): 2.0/2.1 MB | 277 kB
Progress (2): 2.1 MB | 277 kB    
                             
Downloaded from central: https://repo.maven.apache.org/maven2/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar (277 kB at 524 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/assertj/assertj-core/3.26.3/assertj-core-3.26.3.jar
Progress (2): 2.1 MB | 16/122 kB
Progress (2): 2.1 MB | 33/122 kB
Progress (2): 2.1 MB | 48/122 kB
Progress (2): 2.1 MB | 64/122 kB
Progress (2): 2.1 MB | 81/122 kB
Progress (2): 2.1 MB | 97/122 kB
Progress (2): 2.1 MB | 113/122 kB
Progress (2): 2.1 MB | 122 kB    
Progress (3): 2.1 MB | 122 kB | 16/30 kB
Progress (3): 2.1 MB | 122 kB | 30 kB   
Progress (4): 2.1 MB | 122 kB | 30 kB | 16/126 kB
Progress (4): 2.1 MB | 122 kB | 30 kB | 33/126 kB
Progress (4): 2.1 MB | 122 kB | 30 kB | 49/126 kB
Progress (4): 2.1 MB | 122 kB | 30 kB | 66/126 kB
Progress (4): 2.1 MB | 122 kB | 30 kB | 82/126 kB
Progress (4): 2.1 MB | 122 kB | 30 kB | 98/126 kB
Progress (4): 2.1 MB | 122 kB | 30 kB | 115/126 kB
Progress (4): 2.1 MB | 122 kB | 30 kB | 126 kB    
                                              
Downloaded from central: https://repo.maven.apache.org/maven2/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar (122 kB at 225 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/net/bytebuddy/byte-buddy/1.15.11/byte-buddy-1.15.11.jar
Downloaded from central: https://repo.maven.apache.org/maven2/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar (30 kB at 56 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar (2.1 MB at 3.8 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/ow2/asm/asm/9.7.1/asm-9.7.1.jar (126 kB at 232 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/junit/jupiter/junit-jupiter/5.11.4/junit-jupiter-5.11.4.jar
Progress (1): 0/1.4 MB
Progress (1): 0/1.4 MB
Progress (1): 0/1.4 MB
Progress (1): 0.1/1.4 MB
Progress (1): 0.1/1.4 MB
Progress (1): 0.1/1.4 MB
Progress (1): 0.1/1.4 MB
Progress (1): 0.1/1.4 MB
Progress (1): 0.1/1.4 MB
Progress (1): 0.2/1.4 MB
Progress (1): 0.2/1.4 MB
Progress (1): 0.2/1.4 MB
Progress (1): 0.2/1.4 MB
Progress (1): 0.2/1.4 MB
Progress (1): 0.2/1.4 MB
Progress (1): 0.3/1.4 MB
Progress (1): 0.3/1.4 MB
Progress (1): 0.3/1.4 MB
Progress (1): 0.3/1.4 MB
Progress (1): 0.3/1.4 MB
Progress (1): 0.3/1.4 MB
Progress (1): 0.4/1.4 MB
Progress (1): 0.4/1.4 MB
Progress (1): 0.4/1.4 MB
Progress (1): 0.4/1.4 MB
Progress (1): 0.4/1.4 MB
Progress (1): 0.4/1.4 MB
Progress (1): 0.5/1.4 MB
Progress (1): 0.5/1.4 MB
Progress (1): 0.5/1.4 MB
Progress (2): 0.5/1.4 MB | 16/97 kB
Progress (3): 0.5/1.4 MB | 16/97 kB | 0/8.5 MB
Progress (3): 0.5/1.4 MB | 32/97 kB | 0/8.5 MB
Progress (3): 0.5/1.4 MB | 32/97 kB | 0/8.5 MB
Progress (3): 0.5/1.4 MB | 32/97 kB | 0/8.5 MB
Progress (3): 0.5/1.4 MB | 49/97 kB | 0/8.5 MB
Progress (3): 0.5/1.4 MB | 49/97 kB | 0/8.5 MB
Progress (3): 0.5/1.4 MB | 49/97 kB | 0/8.5 MB
Progress (3): 0.5/1.4 MB | 65/97 kB | 0/8.5 MB
Progress (3): 0.5/1.4 MB | 65/97 kB | 0/8.5 MB
Progress (3): 0.5/1.4 MB | 65/97 kB | 0.1/8.5 MB
Progress (3): 0.5/1.4 MB | 81/97 kB | 0.1/8.5 MB
Progress (4): 0.5/1.4 MB | 81/97 kB | 0.1/8.5 MB | 16/123 kB
Progress (4): 0.5/1.4 MB | 81/97 kB | 0.1/8.5 MB | 16/123 kB
Progress (4): 0.5/1.4 MB | 97 kB | 0.1/8.5 MB | 16/123 kB   
Progress (4): 0.6/1.4 MB | 97 kB | 0.1/8.5 MB | 16/123 kB
Progress (4): 0.6/1.4 MB | 97 kB | 0.1/8.5 MB | 16/123 kB
Progress (4): 0.6/1.4 MB | 97 kB | 0.1/8.5 MB | 32/123 kB
Progress (4): 0.6/1.4 MB | 97 kB | 0.1/8.5 MB | 32/123 kB
Progress (4): 0.6/1.4 MB | 97 kB | 0.1/8.5 MB | 32/123 kB
Progress (4): 0.6/1.4 MB | 97 kB | 0.1/8.5 MB | 49/123 kB
Progress (4): 0.6/1.4 MB | 97 kB | 0.1/8.5 MB | 65/123 kB
Progress (4): 0.6/1.4 MB | 97 kB | 0.1/8.5 MB | 65/123 kB
Progress (5): 0.6/1.4 MB | 97 kB | 0.1/8.5 MB | 65/123 kB | 6.4 kB
Progress (5): 0.6/1.4 MB | 97 kB | 0.1/8.5 MB | 81/123 kB | 6.4 kB
Progress (5): 0.6/1.4 MB | 97 kB | 0.1/8.5 MB | 81/123 kB | 6.4 kB
Progress (5): 0.6/1.4 MB | 97 kB | 0.1/8.5 MB | 81/123 kB | 6.4 kB
Progress (5): 0.6/1.4 MB | 97 kB | 0.1/8.5 MB | 98/123 kB | 6.4 kB
Progress (5): 0.6/1.4 MB | 97 kB | 0.1/8.5 MB | 98/123 kB | 6.4 kB
Progress (5): 0.6/1.4 MB | 97 kB | 0.1/8.5 MB | 98/123 kB | 6.4 kB
Progress (5): 0.6/1.4 MB | 97 kB | 0.1/8.5 MB | 98/123 kB | 6.4 kB
Progress (5): 0.6/1.4 MB | 97 kB | 0.1/8.5 MB | 114/123 kB | 6.4 kB
Progress (5): 0.7/1.4 MB | 97 kB | 0.1/8.5 MB | 114/123 kB | 6.4 kB
Progress (5): 0.7/1.4 MB | 97 kB | 0.1/8.5 MB | 123 kB | 6.4 kB    
Progress (5): 0.7/1.4 MB | 97 kB | 0.1/8.5 MB | 123 kB | 6.4 kB
Progress (5): 0.7/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
Progress (5): 0.7/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
Progress (5): 0.7/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
Progress (5): 0.7/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
Progress (5): 0.7/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
Progress (5): 0.7/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
Progress (5): 0.7/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
Progress (5): 0.7/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
Progress (5): 0.7/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
Progress (5): 0.8/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
Progress (5): 0.8/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
Progress (5): 0.8/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
Progress (5): 0.8/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
Progress (5): 0.8/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
Progress (5): 0.8/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
Progress (5): 0.8/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
Progress (5): 0.8/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
Progress (5): 0.9/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
Progress (5): 0.9/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
Progress (5): 0.9/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
Progress (5): 0.9/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
Progress (5): 0.9/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
Progress (5): 0.9/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
Progress (5): 1.0/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
Progress (5): 1.0/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
Progress (5): 1.0/1.4 MB | 97 kB | 0.2/8.5 MB | 123 kB | 6.4 kB
                                                               
Downloaded from central: https://repo.maven.apache.org/maven2/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar (97 kB at 174 kB/s)
Progress (4): 1.0/1.4 MB | 0.3/8.5 MB | 123 kB | 6.4 kB
                                                       
Downloading from central: https://repo.maven.apache.org/maven2/org/junit/jupiter/junit-jupiter-api/5.11.4/junit-jupiter-api-5.11.4.jar
Progress (4): 1.0/1.4 MB | 0.3/8.5 MB | 123 kB | 6.4 kB
Progress (4): 1.0/1.4 MB | 0.3/8.5 MB | 123 kB | 6.4 kB
Progress (4): 1.0/1.4 MB | 0.3/8.5 MB | 123 kB | 6.4 kB
Progress (4): 1.0/1.4 MB | 0.3/8.5 MB | 123 kB | 6.4 kB
                                                       
Downloaded from central: https://repo.maven.apache.org/maven2/org/junit/jupiter/junit-jupiter/5.11.4/junit-jupiter-5.11.4.jar (6.4 kB at 11 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar
Progress (3): 1.0/1.4 MB | 0.3/8.5 MB | 123 kB
Progress (3): 1.0/1.4 MB | 0.3/8.5 MB | 123 kB
Progress (3): 1.1/1.4 MB | 0.3/8.5 MB | 123 kB
Progress (3): 1.1/1.4 MB | 0.3/8.5 MB | 123 kB
                                              
Downloaded from central: https://repo.maven.apache.org/maven2/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar (123 kB at 221 kB/s)
Progress (2): 1.1/1.4 MB | 0.3/8.5 MB
                                     
Downloading from central: https://repo.maven.apache.org/maven2/org/junit/platform/junit-platform-commons/1.11.4/junit-platform-commons-1.11.4.jar
Progress (2): 1.1/1.4 MB | 0.3/8.5 MB
Progress (2): 1.1/1.4 MB | 0.3/8.5 MB
Progress (2): 1.1/1.4 MB | 0.3/8.5 MB
Progress (2): 1.1/1.4 MB | 0.3/8.5 MB
Progress (2): 1.1/1.4 MB | 0.3/8.5 MB
Progress (2): 1.2/1.4 MB | 0.3/8.5 MB
Progress (2): 1.2/1.4 MB | 0.3/8.5 MB
Progress (2): 1.2/1.4 MB | 0.4/8.5 MB
Progress (2): 1.2/1.4 MB | 0.4/8.5 MB
Progress (2): 1.2/1.4 MB | 0.4/8.5 MB
Progress (2): 1.2/1.4 MB | 0.4/8.5 MB
Progress (2): 1.2/1.4 MB | 0.4/8.5 MB
Progress (2): 1.2/1.4 MB | 0.4/8.5 MB
Progress (2): 1.2/1.4 MB | 0.4/8.5 MB
Progress (2): 1.2/1.4 MB | 0.4/8.5 MB
Progress (2): 1.3/1.4 MB | 0.4/8.5 MB
Progress (2): 1.3/1.4 MB | 0.4/8.5 MB
Progress (2): 1.3/1.4 MB | 0.4/8.5 MB
Progress (2): 1.3/1.4 MB | 0.4/8.5 MB
Progress (2): 1.3/1.4 MB | 0.4/8.5 MB
Progress (2): 1.3/1.4 MB | 0.4/8.5 MB
Progress (2): 1.3/1.4 MB | 0.4/8.5 MB
Progress (2): 1.3/1.4 MB | 0.4/8.5 MB
Progress (2): 1.3/1.4 MB | 0.4/8.5 MB
Progress (2): 1.3/1.4 MB | 0.5/8.5 MB
Progress (2): 1.4/1.4 MB | 0.5/8.5 MB
Progress (2): 1.4/1.4 MB | 0.5/8.5 MB
Progress (2): 1.4/1.4 MB | 0.5/8.5 MB
Progress (2): 1.4 MB | 0.5/8.5 MB    
Progress (2): 1.4 MB | 0.5/8.5 MB
Progress (2): 1.4 MB | 0.5/8.5 MB
Progress (2): 1.4 MB | 0.5/8.5 MB
Progress (2): 1.4 MB | 0.5/8.5 MB
Progress (2): 1.4 MB | 0.6/8.5 MB
Progress (2): 1.4 MB | 0.6/8.5 MB
Progress (2): 1.4 MB | 0.6/8.5 MB
Progress (2): 1.4 MB | 0.6/8.5 MB
Progress (2): 1.4 MB | 0.6/8.5 MB
Progress (2): 1.4 MB | 0.6/8.5 MB
Progress (2): 1.4 MB | 0.7/8.5 MB
Progress (3): 1.4 MB | 0.7/8.5 MB | 14 kB
Progress (3): 1.4 MB | 0.7/8.5 MB | 14 kB
Progress (4): 1.4 MB | 0.7/8.5 MB | 14 kB | 16/216 kB
Progress (4): 1.4 MB | 0.7/8.5 MB | 14 kB | 16/216 kB
Progress (4): 1.4 MB | 0.7/8.5 MB | 14 kB | 33/216 kB
Progress (4): 1.4 MB | 0.7/8.5 MB | 14 kB | 49/216 kB
Progress (4): 1.4 MB | 0.7/8.5 MB | 14 kB | 49/216 kB
Progress (5): 1.4 MB | 0.7/8.5 MB | 14 kB | 49/216 kB | 16/142 kB
Progress (5): 1.4 MB | 0.7/8.5 MB | 14 kB | 64/216 kB | 16/142 kB
Progress (5): 1.4 MB | 0.7/8.5 MB | 14 kB | 64/216 kB | 16/142 kB
Progress (5): 1.4 MB | 0.7/8.5 MB | 14 kB | 64/216 kB | 33/142 kB
Progress (5): 1.4 MB | 0.7/8.5 MB | 14 kB | 80/216 kB | 33/142 kB
Progress (5): 1.4 MB | 0.7/8.5 MB | 14 kB | 80/216 kB | 49/142 kB
Progress (5): 1.4 MB | 0.7/8.5 MB | 14 kB | 80/216 kB | 63/142 kB
Progress (5): 1.4 MB | 0.7/8.5 MB | 14 kB | 80/216 kB | 63/142 kB
Progress (5): 1.4 MB | 0.7/8.5 MB | 14 kB | 80/216 kB | 79/142 kB
Progress (5): 1.4 MB | 0.7/8.5 MB | 14 kB | 96/216 kB | 79/142 kB
Progress (5): 1.4 MB | 0.8/8.5 MB | 14 kB | 96/216 kB | 79/142 kB
Progress (5): 1.4 MB | 0.8/8.5 MB | 14 kB | 96/216 kB | 95/142 kB
Progress (5): 1.4 MB | 0.8/8.5 MB | 14 kB | 113/216 kB | 95/142 kB
Progress (5): 1.4 MB | 0.8/8.5 MB | 14 kB | 113/216 kB | 112/142 kB
Progress (5): 1.4 MB | 0.8/8.5 MB | 14 kB | 129/216 kB | 112/142 kB
Progress (5): 1.4 MB | 0.8/8.5 MB | 14 kB | 129/216 kB | 128/142 kB
Progress (5): 1.4 MB | 0.8/8.5 MB | 14 kB | 145/216 kB | 128/142 kB
Progress (5): 1.4 MB | 0.8/8.5 MB | 14 kB | 145/216 kB | 128/142 kB
Progress (5): 1.4 MB | 0.8/8.5 MB | 14 kB | 145/216 kB | 142 kB    
                                                               
Downloaded from central: https://repo.maven.apache.org/maven2/org/assertj/assertj-core/3.26.3/assertj-core-3.26.3.jar (1.4 MB at 2.4 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar
Progress (4): 0.8/8.5 MB | 14 kB | 162/216 kB | 142 kB
Progress (4): 0.8/8.5 MB | 14 kB | 162/216 kB | 142 kB
Progress (4): 0.8/8.5 MB | 14 kB | 178/216 kB | 142 kB
Progress (4): 0.8/8.5 MB | 14 kB | 195/216 kB | 142 kB
Progress (4): 0.8/8.5 MB | 14 kB | 211/216 kB | 142 kB
Progress (4): 0.8/8.5 MB | 14 kB | 216 kB | 142 kB    
Progress (4): 0.8/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 0.8/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 0.8/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 0.8/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 0.9/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 0.9/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 0.9/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 0.9/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 0.9/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 0.9/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.0/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.0/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.0/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.0/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.0/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.0/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.1/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.1/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.1/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.1/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.1/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.1/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.2/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.2/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.2/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.2/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.2/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.2/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.3/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.3/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.3/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.3/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.3/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.3/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.4/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.4/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.4/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.4/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.4/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.4/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.5/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.5/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.5/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.5/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.5/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.5/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.6/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.6/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.6/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.6/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.6/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.6/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.7/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.7/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.7/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.7/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.7/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.7/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.7/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.8/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.8/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.8/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.8/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.8/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.8/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.9/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.9/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.9/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.9/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.9/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 1.9/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 2.0/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 2.0/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 2.0/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 2.0/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 2.0/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (4): 2.0/8.5 MB | 14 kB | 216 kB | 142 kB
Progress (5): 2.0/8.5 MB | 14 kB | 216 kB | 142 kB | 6.8 kB
Progress (5): 2.1/8.5 MB | 14 kB | 216 kB | 142 kB | 6.8 kB
Progress (5): 2.1/8.5 MB | 14 kB | 216 kB | 142 kB | 6.8 kB
Progress (5): 2.1/8.5 MB | 14 kB | 216 kB | 142 kB | 6.8 kB
                                                           
Downloaded from central: https://repo.maven.apache.org/maven2/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar (14 kB at 22 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/junit/jupiter/junit-jupiter-params/5.11.4/junit-jupiter-params-5.11.4.jar
Progress (4): 2.1/8.5 MB | 216 kB | 142 kB | 6.8 kB
Progress (4): 2.1/8.5 MB | 216 kB | 142 kB | 6.8 kB
Progress (4): 2.1/8.5 MB | 216 kB | 142 kB | 6.8 kB
Progress (4): 2.2/8.5 MB | 216 kB | 142 kB | 6.8 kB
Progress (4): 2.2/8.5 MB | 216 kB | 142 kB | 6.8 kB
Progress (4): 2.2/8.5 MB | 216 kB | 142 kB | 6.8 kB
Progress (4): 2.2/8.5 MB | 216 kB | 142 kB | 6.8 kB
Progress (4): 2.2/8.5 MB | 216 kB | 142 kB | 6.8 kB
Progress (4): 2.2/8.5 MB | 216 kB | 142 kB | 6.8 kB
Progress (4): 2.3/8.5 MB | 216 kB | 142 kB | 6.8 kB
Progress (4): 2.3/8.5 MB | 216 kB | 142 kB | 6.8 kB
                                                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/junit/platform/junit-platform-commons/1.11.4/junit-platform-commons-1.11.4.jar (142 kB at 223 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/junit/jupiter/junit-jupiter-engine/5.11.4/junit-jupiter-engine-5.11.4.jar
Progress (3): 2.3/8.5 MB | 216 kB | 6.8 kB
Progress (3): 2.3/8.5 MB | 216 kB | 6.8 kB
Progress (3): 2.3/8.5 MB | 216 kB | 6.8 kB
Progress (3): 2.3/8.5 MB | 216 kB | 6.8 kB
                                          
Downloaded from central: https://repo.maven.apache.org/maven2/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar (6.8 kB at 11 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/junit/platform/junit-platform-engine/1.11.4/junit-platform-engine-1.11.4.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/junit/jupiter/junit-jupiter-api/5.11.4/junit-jupiter-api-5.11.4.jar (216 kB at 339 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/mockito/mockito-core/5.14.2/mockito-core-5.14.2.jar
Progress (1): 2.4/8.5 MB
Progress (1): 2.4/8.5 MB
Progress (1): 2.4/8.5 MB
Progress (1): 2.4/8.5 MB
Progress (1): 2.4/8.5 MB
Progress (1): 2.4/8.5 MB
Progress (1): 2.5/8.5 MB
Progress (1): 2.5/8.5 MB
Progress (1): 2.5/8.5 MB
Progress (1): 2.5/8.5 MB
Progress (1): 2.5/8.5 MB
Progress (1): 2.5/8.5 MB
Progress (1): 2.6/8.5 MB
Progress (1): 2.6/8.5 MB
Progress (1): 2.6/8.5 MB
Progress (1): 2.6/8.5 MB
Progress (1): 2.6/8.5 MB
Progress (1): 2.6/8.5 MB
Progress (1): 2.7/8.5 MB
Progress (1): 2.7/8.5 MB
Progress (1): 2.7/8.5 MB
Progress (1): 2.7/8.5 MB
Progress (1): 2.7/8.5 MB
Progress (2): 2.7/8.5 MB | 16/592 kB
Progress (2): 2.7/8.5 MB | 16/592 kB
Progress (2): 2.7/8.5 MB | 16/592 kB
Progress (2): 2.7/8.5 MB | 33/592 kB
Progress (2): 2.8/8.5 MB | 33/592 kB
Progress (2): 2.8/8.5 MB | 49/592 kB
Progress (2): 2.8/8.5 MB | 49/592 kB
Progress (2): 2.8/8.5 MB | 66/592 kB
Progress (2): 2.8/8.5 MB | 82/592 kB
Progress (2): 2.8/8.5 MB | 98/592 kB
Progress (2): 2.8/8.5 MB | 115/592 kB
Progress (2): 2.8/8.5 MB | 131/592 kB
Progress (2): 2.8/8.5 MB | 131/592 kB
Progress (2): 2.8/8.5 MB | 147/592 kB
Progress (2): 2.8/8.5 MB | 164/592 kB
Progress (2): 2.8/8.5 MB | 180/592 kB
Progress (2): 2.8/8.5 MB | 180/592 kB
Progress (2): 2.8/8.5 MB | 197/592 kB
Progress (2): 2.8/8.5 MB | 197/592 kB
Progress (2): 2.8/8.5 MB | 213/592 kB
Progress (2): 2.8/8.5 MB | 213/592 kB
Progress (2): 2.8/8.5 MB | 229/592 kB
Progress (2): 2.8/8.5 MB | 246/592 kB
Progress (3): 2.8/8.5 MB | 246/592 kB | 16/260 kB
Progress (3): 2.8/8.5 MB | 256/592 kB | 16/260 kB
Progress (3): 2.8/8.5 MB | 256/592 kB | 32/260 kB
Progress (3): 2.8/8.5 MB | 272/592 kB | 32/260 kB
Progress (4): 2.8/8.5 MB | 272/592 kB | 32/260 kB | 16/708 kB
Progress (4): 2.8/8.5 MB | 272/592 kB | 49/260 kB | 16/708 kB
Progress (5): 2.8/8.5 MB | 272/592 kB | 49/260 kB | 16/708 kB | 16/247 kB
Progress (5): 2.8/8.5 MB | 289/592 kB | 49/260 kB | 16/708 kB | 16/247 kB
Progress (5): 2.8/8.5 MB | 305/592 kB | 49/260 kB | 16/708 kB | 16/247 kB
Progress (5): 2.8/8.5 MB | 305/592 kB | 65/260 kB | 16/708 kB | 16/247 kB
Progress (5): 2.9/8.5 MB | 305/592 kB | 65/260 kB | 16/708 kB | 16/247 kB
Progress (5): 2.9/8.5 MB | 305/592 kB | 65/260 kB | 16/708 kB | 32/247 kB
Progress (5): 2.9/8.5 MB | 305/592 kB | 65/260 kB | 33/708 kB | 32/247 kB
Progress (5): 2.9/8.5 MB | 321/592 kB | 65/260 kB | 33/708 kB | 32/247 kB
Progress (5): 2.9/8.5 MB | 321/592 kB | 81/260 kB | 33/708 kB | 32/247 kB
Progress (5): 2.9/8.5 MB | 321/592 kB | 81/260 kB | 33/708 kB | 49/247 kB
Progress (5): 2.9/8.5 MB | 338/592 kB | 81/260 kB | 33/708 kB | 49/247 kB
Progress (5): 2.9/8.5 MB | 338/592 kB | 98/260 kB | 33/708 kB | 49/247 kB
Progress (5): 2.9/8.5 MB | 338/592 kB | 98/260 kB | 49/708 kB | 49/247 kB
Progress (5): 2.9/8.5 MB | 338/592 kB | 98/260 kB | 49/708 kB | 49/247 kB
Progress (5): 2.9/8.5 MB | 354/592 kB | 98/260 kB | 49/708 kB | 49/247 kB
Progress (5): 2.9/8.5 MB | 354/592 kB | 114/260 kB | 49/708 kB | 49/247 kB
Progress (5): 2.9/8.5 MB | 354/592 kB | 114/260 kB | 49/708 kB | 65/247 kB
Progress (5): 2.9/8.5 MB | 354/592 kB | 114/260 kB | 63/708 kB | 65/247 kB
Progress (5): 2.9/8.5 MB | 354/592 kB | 114/260 kB | 63/708 kB | 65/247 kB
Progress (5): 2.9/8.5 MB | 370/592 kB | 114/260 kB | 63/708 kB | 65/247 kB
Progress (5): 2.9/8.5 MB | 370/592 kB | 114/260 kB | 63/708 kB | 81/247 kB
Progress (5): 2.9/8.5 MB | 370/592 kB | 114/260 kB | 79/708 kB | 81/247 kB
Progress (5): 2.9/8.5 MB | 370/592 kB | 131/260 kB | 79/708 kB | 81/247 kB
Progress (5): 2.9/8.5 MB | 370/592 kB | 131/260 kB | 79/708 kB | 81/247 kB
Progress (5): 2.9/8.5 MB | 387/592 kB | 131/260 kB | 79/708 kB | 81/247 kB
Progress (5): 2.9/8.5 MB | 387/592 kB | 131/260 kB | 79/708 kB | 98/247 kB
Progress (5): 2.9/8.5 MB | 387/592 kB | 147/260 kB | 79/708 kB | 98/247 kB
Progress (5): 2.9/8.5 MB | 387/592 kB | 147/260 kB | 96/708 kB | 98/247 kB
Progress (5): 2.9/8.5 MB | 387/592 kB | 147/260 kB | 96/708 kB | 98/247 kB
Progress (5): 2.9/8.5 MB | 387/592 kB | 147/260 kB | 96/708 kB | 114/247 kB
Progress (5): 2.9/8.5 MB | 403/592 kB | 147/260 kB | 96/708 kB | 114/247 kB
Progress (5): 2.9/8.5 MB | 403/592 kB | 147/260 kB | 112/708 kB | 114/247 kB
Progress (5): 2.9/8.5 MB | 403/592 kB | 163/260 kB | 112/708 kB | 114/247 kB
Progress (5): 2.9/8.5 MB | 403/592 kB | 163/260 kB | 112/708 kB | 130/247 kB
Progress (5): 2.9/8.5 MB | 420/592 kB | 163/260 kB | 112/708 kB | 130/247 kB
Progress (5): 2.9/8.5 MB | 420/592 kB | 180/260 kB | 112/708 kB | 130/247 kB
Progress (5): 2.9/8.5 MB | 420/592 kB | 180/260 kB | 129/708 kB | 130/247 kB
Progress (5): 2.9/8.5 MB | 420/592 kB | 180/260 kB | 129/708 kB | 147/247 kB
Progress (5): 2.9/8.5 MB | 436/592 kB | 180/260 kB | 129/708 kB | 147/247 kB
Progress (5): 2.9/8.5 MB | 436/592 kB | 196/260 kB | 129/708 kB | 147/247 kB
Progress (5): 2.9/8.5 MB | 436/592 kB | 196/260 kB | 145/708 kB | 147/247 kB
Progress (5): 2.9/8.5 MB | 436/592 kB | 196/260 kB | 145/708 kB | 163/247 kB
Progress (5): 2.9/8.5 MB | 436/592 kB | 196/260 kB | 145/708 kB | 163/247 kB
Progress (5): 2.9/8.5 MB | 452/592 kB | 196/260 kB | 145/708 kB | 163/247 kB
Progress (5): 2.9/8.5 MB | 452/592 kB | 212/260 kB | 145/708 kB | 163/247 kB
Progress (5): 2.9/8.5 MB | 452/592 kB | 212/260 kB | 161/708 kB | 163/247 kB
Progress (5): 2.9/8.5 MB | 452/592 kB | 212/260 kB | 161/708 kB | 180/247 kB
Progress (5): 3.0/8.5 MB | 452/592 kB | 212/260 kB | 161/708 kB | 180/247 kB
Progress (5): 3.0/8.5 MB | 452/592 kB | 229/260 kB | 161/708 kB | 180/247 kB
Progress (5): 3.0/8.5 MB | 469/592 kB | 229/260 kB | 161/708 kB | 180/247 kB
Progress (5): 3.0/8.5 MB | 469/592 kB | 229/260 kB | 161/708 kB | 196/247 kB
Progress (5): 3.0/8.5 MB | 469/592 kB | 229/260 kB | 178/708 kB | 196/247 kB
Progress (5): 3.0/8.5 MB | 469/592 kB | 245/260 kB | 178/708 kB | 196/247 kB
Progress (5): 3.0/8.5 MB | 469/592 kB | 245/260 kB | 178/708 kB | 196/247 kB
Progress (5): 3.0/8.5 MB | 485/592 kB | 245/260 kB | 178/708 kB | 196/247 kB
Progress (5): 3.0/8.5 MB | 485/592 kB | 245/260 kB | 178/708 kB | 212/247 kB
Progress (5): 3.0/8.5 MB | 485/592 kB | 260 kB | 178/708 kB | 212/247 kB    
Progress (5): 3.0/8.5 MB | 485/592 kB | 260 kB | 194/708 kB | 212/247 kB
Progress (5): 3.0/8.5 MB | 502/592 kB | 260 kB | 194/708 kB | 212/247 kB
Progress (5): 3.0/8.5 MB | 502/592 kB | 260 kB | 194/708 kB | 229/247 kB
Progress (5): 3.0/8.5 MB | 518/592 kB | 260 kB | 194/708 kB | 229/247 kB
Progress (5): 3.0/8.5 MB | 518/592 kB | 260 kB | 194/708 kB | 245/247 kB
Progress (5): 3.0/8.5 MB | 518/592 kB | 260 kB | 210/708 kB | 245/247 kB
Progress (5): 3.0/8.5 MB | 518/592 kB | 260 kB | 210/708 kB | 247 kB    
Progress (5): 3.0/8.5 MB | 534/592 kB | 260 kB | 210/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 534/592 kB | 260 kB | 227/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 551/592 kB | 260 kB | 227/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 551/592 kB | 260 kB | 243/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 567/592 kB | 260 kB | 243/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 567/592 kB | 260 kB | 260/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 583/592 kB | 260 kB | 260/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 260/708 kB | 247 kB    
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 276/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 292/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 309/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 325/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 342/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 358/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 374/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 391/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 407/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 423/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 440/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 456/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 473/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 489/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 505/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 522/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 538/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 555/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 571/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 587/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 604/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 620/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 636/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 653/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 669/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 686/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 702/708 kB | 247 kB
Progress (5): 3.0/8.5 MB | 592 kB | 260 kB | 708 kB | 247 kB    
                                                            
Downloaded from central: https://repo.maven.apache.org/maven2/org/junit/platform/junit-platform-engine/1.11.4/junit-platform-engine-1.11.4.jar (247 kB at 369 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/junit/jupiter/junit-jupiter-engine/5.11.4/junit-jupiter-engine-5.11.4.jar (260 kB at 389 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/objenesis/objenesis/3.3/objenesis-3.3.jar
Progress (3): 3.0/8.5 MB | 592 kB | 708 kB
Progress (3): 3.0/8.5 MB | 592 kB | 708 kB
Progress (3): 3.0/8.5 MB | 592 kB | 708 kB
Progress (3): 3.0/8.5 MB | 592 kB | 708 kB
Progress (3): 3.1/8.5 MB | 592 kB | 708 kB
Progress (3): 3.1/8.5 MB | 592 kB | 708 kB
Progress (3): 3.1/8.5 MB | 592 kB | 708 kB
Progress (3): 3.1/8.5 MB | 592 kB | 708 kB
                                          
Downloaded from central: https://repo.maven.apache.org/maven2/org/mockito/mockito-core/5.14.2/mockito-core-5.14.2.jar (708 kB at 1.1 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/mockito/mockito-junit-jupiter/5.14.2/mockito-junit-jupiter-5.14.2.jar
Progress (2): 3.1/8.5 MB | 592 kB
Progress (2): 3.1/8.5 MB | 592 kB
Progress (2): 3.2/8.5 MB | 592 kB
Progress (2): 3.2/8.5 MB | 592 kB
Progress (2): 3.2/8.5 MB | 592 kB
Progress (2): 3.2/8.5 MB | 592 kB
Progress (2): 3.2/8.5 MB | 592 kB
Progress (2): 3.2/8.5 MB | 592 kB
Progress (2): 3.3/8.5 MB | 592 kB
Progress (2): 3.3/8.5 MB | 592 kB
Progress (2): 3.3/8.5 MB | 592 kB
Progress (2): 3.3/8.5 MB | 592 kB
Progress (2): 3.3/8.5 MB | 592 kB
Progress (2): 3.3/8.5 MB | 592 kB
Progress (2): 3.4/8.5 MB | 592 kB
Progress (2): 3.4/8.5 MB | 592 kB
Progress (2): 3.4/8.5 MB | 592 kB
Progress (2): 3.4/8.5 MB | 592 kB
Progress (2): 3.4/8.5 MB | 592 kB
Progress (2): 3.4/8.5 MB | 592 kB
Progress (2): 3.5/8.5 MB | 592 kB
Progress (2): 3.5/8.5 MB | 592 kB
Progress (2): 3.5/8.5 MB | 592 kB
Progress (2): 3.5/8.5 MB | 592 kB
Progress (2): 3.5/8.5 MB | 592 kB
Progress (2): 3.5/8.5 MB | 592 kB
Progress (2): 3.6/8.5 MB | 592 kB
Progress (2): 3.6/8.5 MB | 592 kB
Progress (2): 3.6/8.5 MB | 592 kB
Progress (2): 3.6/8.5 MB | 592 kB
                                 
Downloaded from central: https://repo.maven.apache.org/maven2/org/junit/jupiter/junit-jupiter-params/5.11.4/junit-jupiter-params-5.11.4.jar (592 kB at 865 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar
Progress (1): 3.6/8.5 MB
Progress (1): 3.6/8.5 MB
Progress (2): 3.6/8.5 MB | 16/365 kB
Progress (2): 3.7/8.5 MB | 16/365 kB
Progress (2): 3.7/8.5 MB | 16/365 kB
Progress (2): 3.7/8.5 MB | 32/365 kB
Progress (2): 3.7/8.5 MB | 32/365 kB
Progress (2): 3.7/8.5 MB | 49/365 kB
Progress (2): 3.7/8.5 MB | 49/365 kB
Progress (2): 3.7/8.5 MB | 65/365 kB
Progress (2): 3.7/8.5 MB | 65/365 kB
Progress (2): 3.7/8.5 MB | 81/365 kB
Progress (2): 3.7/8.5 MB | 81/365 kB
Progress (2): 3.7/8.5 MB | 98/365 kB
Progress (2): 3.7/8.5 MB | 98/365 kB
Progress (2): 3.7/8.5 MB | 114/365 kB
Progress (2): 3.8/8.5 MB | 114/365 kB
Progress (2): 3.8/8.5 MB | 130/365 kB
Progress (2): 3.8/8.5 MB | 130/365 kB
Progress (2): 3.8/8.5 MB | 147/365 kB
Progress (2): 3.8/8.5 MB | 147/365 kB
Progress (2): 3.8/8.5 MB | 163/365 kB
Progress (2): 3.8/8.5 MB | 163/365 kB
Progress (2): 3.8/8.5 MB | 180/365 kB
Progress (2): 3.8/8.5 MB | 180/365 kB
Progress (2): 3.8/8.5 MB | 196/365 kB
Progress (2): 3.8/8.5 MB | 196/365 kB
Progress (2): 3.8/8.5 MB | 212/365 kB
Progress (2): 3.9/8.5 MB | 212/365 kB
Progress (2): 3.9/8.5 MB | 229/365 kB
Progress (2): 3.9/8.5 MB | 229/365 kB
Progress (2): 3.9/8.5 MB | 245/365 kB
Progress (2): 3.9/8.5 MB | 245/365 kB
Progress (2): 3.9/8.5 MB | 262/365 kB
Progress (2): 3.9/8.5 MB | 262/365 kB
Progress (2): 3.9/8.5 MB | 278/365 kB
Progress (3): 3.9/8.5 MB | 278/365 kB | 16/49 kB
Progress (3): 3.9/8.5 MB | 278/365 kB | 16/49 kB
Progress (3): 3.9/8.5 MB | 294/365 kB | 16/49 kB
Progress (3): 3.9/8.5 MB | 294/365 kB | 33/49 kB
Progress (3): 3.9/8.5 MB | 294/365 kB | 33/49 kB
Progress (3): 3.9/8.5 MB | 311/365 kB | 33/49 kB
Progress (3): 3.9/8.5 MB | 311/365 kB | 49/49 kB
Progress (3): 3.9/8.5 MB | 327/365 kB | 49/49 kB
Progress (3): 4.0/8.5 MB | 327/365 kB | 49/49 kB
Progress (3): 4.0/8.5 MB | 327/365 kB | 49 kB   
Progress (3): 4.0/8.5 MB | 343/365 kB | 49 kB
Progress (3): 4.0/8.5 MB | 343/365 kB | 49 kB
Progress (3): 4.0/8.5 MB | 360/365 kB | 49 kB
Progress (3): 4.0/8.5 MB | 360/365 kB | 49 kB
Progress (3): 4.0/8.5 MB | 365 kB | 49 kB    
Progress (3): 4.0/8.5 MB | 365 kB | 49 kB
Progress (3): 4.0/8.5 MB | 365 kB | 49 kB
Progress (3): 4.0/8.5 MB | 365 kB | 49 kB
Progress (3): 4.1/8.5 MB | 365 kB | 49 kB
Progress (3): 4.1/8.5 MB | 365 kB | 49 kB
Progress (3): 4.1/8.5 MB | 365 kB | 49 kB
Progress (3): 4.1/8.5 MB | 365 kB | 49 kB
Progress (3): 4.1/8.5 MB | 365 kB | 49 kB
Progress (3): 4.1/8.5 MB | 365 kB | 49 kB
Progress (3): 4.2/8.5 MB | 365 kB | 49 kB
Progress (3): 4.2/8.5 MB | 365 kB | 49 kB
Progress (4): 4.2/8.5 MB | 365 kB | 49 kB | 8.9 kB
Progress (4): 4.2/8.5 MB | 365 kB | 49 kB | 8.9 kB
Progress (4): 4.2/8.5 MB | 365 kB | 49 kB | 8.9 kB
Progress (4): 4.2/8.5 MB | 365 kB | 49 kB | 8.9 kB
Progress (4): 4.2/8.5 MB | 365 kB | 49 kB | 8.9 kB
Progress (4): 4.3/8.5 MB | 365 kB | 49 kB | 8.9 kB
Progress (4): 4.3/8.5 MB | 365 kB | 49 kB | 8.9 kB
Progress (4): 4.3/8.5 MB | 365 kB | 49 kB | 8.9 kB
Progress (4): 4.3/8.5 MB | 365 kB | 49 kB | 8.9 kB
Progress (4): 4.3/8.5 MB | 365 kB | 49 kB | 8.9 kB
Progress (4): 4.3/8.5 MB | 365 kB | 49 kB | 8.9 kB
Progress (4): 4.4/8.5 MB | 365 kB | 49 kB | 8.9 kB
Progress (4): 4.4/8.5 MB | 365 kB | 49 kB | 8.9 kB
Progress (4): 4.4/8.5 MB | 365 kB | 49 kB | 8.9 kB
Progress (4): 4.4/8.5 MB | 365 kB | 49 kB | 8.9 kB
Progress (4): 4.4/8.5 MB | 365 kB | 49 kB | 8.9 kB
Progress (4): 4.4/8.5 MB | 365 kB | 49 kB | 8.9 kB
Progress (4): 4.5/8.5 MB | 365 kB | 49 kB | 8.9 kB
                                                  
Downloaded from central: https://repo.maven.apache.org/maven2/org/objenesis/objenesis/3.3/objenesis-3.3.jar (49 kB at 71 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar
Progress (3): 4.5/8.5 MB | 365 kB | 8.9 kB
Progress (3): 4.5/8.5 MB | 365 kB | 8.9 kB
Progress (3): 4.5/8.5 MB | 365 kB | 8.9 kB
                                          
Downloaded from central: https://repo.maven.apache.org/maven2/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.jar (365 kB at 522 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/spring-test/6.2.6/spring-test-6.2.6.jar
Progress (2): 4.5/8.5 MB | 8.9 kB
Progress (3): 4.5/8.5 MB | 8.9 kB | 16/31 kB
Progress (3): 4.5/8.5 MB | 8.9 kB | 16/31 kB
Progress (3): 4.5/8.5 MB | 8.9 kB | 31 kB   
Progress (3): 4.6/8.5 MB | 8.9 kB | 31 kB
Progress (3): 4.6/8.5 MB | 8.9 kB | 31 kB
Progress (3): 4.6/8.5 MB | 8.9 kB | 31 kB
Progress (3): 4.6/8.5 MB | 8.9 kB | 31 kB
                                         
Downloaded from central: https://repo.maven.apache.org/maven2/org/mockito/mockito-junit-jupiter/5.14.2/mockito-junit-jupiter-5.14.2.jar (8.9 kB at 13 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/xmlunit/xmlunit-core/2.10.0/xmlunit-core-2.10.0.jar
Progress (2): 4.6/8.5 MB | 31 kB
Progress (2): 4.6/8.5 MB | 31 kB
Progress (2): 4.6/8.5 MB | 31 kB
Progress (2): 4.7/8.5 MB | 31 kB
Progress (2): 4.7/8.5 MB | 31 kB
Progress (2): 4.7/8.5 MB | 31 kB
Progress (2): 4.7/8.5 MB | 31 kB
Progress (2): 4.7/8.5 MB | 31 kB
Progress (2): 4.7/8.5 MB | 31 kB
Progress (2): 4.8/8.5 MB | 31 kB
Progress (2): 4.8/8.5 MB | 31 kB
Progress (2): 4.8/8.5 MB | 31 kB
Progress (2): 4.8/8.5 MB | 31 kB
Progress (2): 4.8/8.5 MB | 31 kB
Progress (2): 4.8/8.5 MB | 31 kB
                                
Downloaded from central: https://repo.maven.apache.org/maven2/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar (31 kB at 44 kB/s)
Progress (1): 4.9/8.5 MB
Progress (1): 4.9/8.5 MB
Progress (1): 4.9/8.5 MB
Progress (1): 4.9/8.5 MB
Progress (1): 4.9/8.5 MB
Progress (1): 4.9/8.5 MB
Progress (1): 5.0/8.5 MB
Progress (1): 5.0/8.5 MB
Progress (1): 5.0/8.5 MB
Progress (1): 5.0/8.5 MB
Progress (1): 5.0/8.5 MB
Progress (1): 5.0/8.5 MB
Progress (2): 5.0/8.5 MB | 16/18 kB
Progress (2): 5.0/8.5 MB | 18 kB   
Progress (2): 5.1/8.5 MB | 18 kB
Progress (2): 5.1/8.5 MB | 18 kB
Progress (2): 5.1/8.5 MB | 18 kB
Progress (2): 5.1/8.5 MB | 18 kB
Progress (3): 5.1/8.5 MB | 18 kB | 16/181 kB
Progress (4): 5.1/8.5 MB | 18 kB | 16/181 kB | 0/1.0 MB
Progress (4): 5.1/8.5 MB | 18 kB | 16/181 kB | 0/1.0 MB
Progress (4): 5.1/8.5 MB | 18 kB | 32/181 kB | 0/1.0 MB
Progress (4): 5.1/8.5 MB | 18 kB | 32/181 kB | 0/1.0 MB
Progress (4): 5.1/8.5 MB | 18 kB | 32/181 kB | 0/1.0 MB
Progress (4): 5.1/8.5 MB | 18 kB | 32/181 kB | 0/1.0 MB
Progress (4): 5.1/8.5 MB | 18 kB | 49/181 kB | 0/1.0 MB
Progress (4): 5.2/8.5 MB | 18 kB | 49/181 kB | 0/1.0 MB
Progress (4): 5.2/8.5 MB | 18 kB | 49/181 kB | 0.1/1.0 MB
Progress (4): 5.2/8.5 MB | 18 kB | 65/181 kB | 0.1/1.0 MB
Progress (4): 5.2/8.5 MB | 18 kB | 65/181 kB | 0.1/1.0 MB
Progress (4): 5.2/8.5 MB | 18 kB | 65/181 kB | 0.1/1.0 MB
Progress (4): 5.2/8.5 MB | 18 kB | 81/181 kB | 0.1/1.0 MB
Progress (4): 5.2/8.5 MB | 18 kB | 81/181 kB | 0.1/1.0 MB
Progress (4): 5.2/8.5 MB | 18 kB | 81/181 kB | 0.1/1.0 MB
Progress (4): 5.2/8.5 MB | 18 kB | 98/181 kB | 0.1/1.0 MB
Progress (4): 5.2/8.5 MB | 18 kB | 98/181 kB | 0.1/1.0 MB
Progress (4): 5.2/8.5 MB | 18 kB | 98/181 kB | 0.1/1.0 MB
Progress (4): 5.2/8.5 MB | 18 kB | 114/181 kB | 0.1/1.0 MB
Progress (4): 5.2/8.5 MB | 18 kB | 114/181 kB | 0.1/1.0 MB
Progress (4): 5.2/8.5 MB | 18 kB | 114/181 kB | 0.1/1.0 MB
Progress (4): 5.2/8.5 MB | 18 kB | 130/181 kB | 0.1/1.0 MB
Progress (4): 5.2/8.5 MB | 18 kB | 130/181 kB | 0.1/1.0 MB
Progress (4): 5.2/8.5 MB | 18 kB | 147/181 kB | 0.1/1.0 MB
Progress (4): 5.2/8.5 MB | 18 kB | 147/181 kB | 0.1/1.0 MB
Progress (4): 5.2/8.5 MB | 18 kB | 147/181 kB | 0.2/1.0 MB
Progress (4): 5.2/8.5 MB | 18 kB | 163/181 kB | 0.2/1.0 MB
Progress (4): 5.3/8.5 MB | 18 kB | 163/181 kB | 0.2/1.0 MB
Progress (4): 5.3/8.5 MB | 18 kB | 163/181 kB | 0.2/1.0 MB
Progress (4): 5.3/8.5 MB | 18 kB | 180/181 kB | 0.2/1.0 MB
Progress (4): 5.3/8.5 MB | 18 kB | 181 kB | 0.2/1.0 MB    
Progress (4): 5.3/8.5 MB | 18 kB | 181 kB | 0.2/1.0 MB
Progress (4): 5.3/8.5 MB | 18 kB | 181 kB | 0.2/1.0 MB
Progress (4): 5.3/8.5 MB | 18 kB | 181 kB | 0.2/1.0 MB
Progress (4): 5.3/8.5 MB | 18 kB | 181 kB | 0.2/1.0 MB
Progress (4): 5.3/8.5 MB | 18 kB | 181 kB | 0.2/1.0 MB
Progress (4): 5.3/8.5 MB | 18 kB | 181 kB | 0.2/1.0 MB
Progress (4): 5.3/8.5 MB | 18 kB | 181 kB | 0.2/1.0 MB
Progress (4): 5.3/8.5 MB | 18 kB | 181 kB | 0.2/1.0 MB
Progress (4): 5.3/8.5 MB | 18 kB | 181 kB | 0.2/1.0 MB
Progress (4): 5.3/8.5 MB | 18 kB | 181 kB | 0.3/1.0 MB
Progress (4): 5.4/8.5 MB | 18 kB | 181 kB | 0.3/1.0 MB
                                                      
Downloaded from central: https://repo.maven.apache.org/maven2/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar (18 kB at 25 kB/s)
Progress (3): 5.4/8.5 MB | 181 kB | 0.3/1.0 MB
Progress (3): 5.4/8.5 MB | 181 kB | 0.3/1.0 MB
Progress (3): 5.4/8.5 MB | 181 kB | 0.3/1.0 MB
Progress (3): 5.4/8.5 MB | 181 kB | 0.3/1.0 MB
Progress (3): 5.4/8.5 MB | 181 kB | 0.3/1.0 MB
Progress (3): 5.4/8.5 MB | 181 kB | 0.3/1.0 MB
Progress (3): 5.4/8.5 MB | 181 kB | 0.3/1.0 MB
Progress (3): 5.4/8.5 MB | 181 kB | 0.3/1.0 MB
Progress (3): 5.4/8.5 MB | 181 kB | 0.3/1.0 MB
Progress (3): 5.4/8.5 MB | 181 kB | 0.4/1.0 MB
Progress (3): 5.4/8.5 MB | 181 kB | 0.4/1.0 MB
Progress (3): 5.4/8.5 MB | 181 kB | 0.4/1.0 MB
Progress (3): 5.4/8.5 MB | 181 kB | 0.4/1.0 MB
Progress (3): 5.5/8.5 MB | 181 kB | 0.4/1.0 MB
Progress (3): 5.5/8.5 MB | 181 kB | 0.4/1.0 MB
Progress (3): 5.5/8.5 MB | 181 kB | 0.4/1.0 MB
Progress (3): 5.5/8.5 MB | 181 kB | 0.4/1.0 MB
Progress (3): 5.5/8.5 MB | 181 kB | 0.4/1.0 MB
Progress (3): 5.5/8.5 MB | 181 kB | 0.4/1.0 MB
Progress (3): 5.5/8.5 MB | 181 kB | 0.4/1.0 MB
Progress (3): 5.5/8.5 MB | 181 kB | 0.5/1.0 MB
Progress (3): 5.5/8.5 MB | 181 kB | 0.5/1.0 MB
Progress (3): 5.5/8.5 MB | 181 kB | 0.5/1.0 MB
Progress (3): 5.5/8.5 MB | 181 kB | 0.5/1.0 MB
Progress (3): 5.5/8.5 MB | 181 kB | 0.5/1.0 MB
Progress (3): 5.5/8.5 MB | 181 kB | 0.5/1.0 MB
Progress (3): 5.6/8.5 MB | 181 kB | 0.5/1.0 MB
Progress (3): 5.6/8.5 MB | 181 kB | 0.5/1.0 MB
Progress (3): 5.6/8.5 MB | 181 kB | 0.5/1.0 MB
Progress (3): 5.6/8.5 MB | 181 kB | 0.5/1.0 MB
Progress (3): 5.6/8.5 MB | 181 kB | 0.5/1.0 MB
Progress (3): 5.6/8.5 MB | 181 kB | 0.5/1.0 MB
Progress (3): 5.6/8.5 MB | 181 kB | 0.5/1.0 MB
Progress (3): 5.6/8.5 MB | 181 kB | 0.6/1.0 MB
Progress (3): 5.6/8.5 MB | 181 kB | 0.6/1.0 MB
Progress (3): 5.6/8.5 MB | 181 kB | 0.6/1.0 MB
Progress (3): 5.6/8.5 MB | 181 kB | 0.6/1.0 MB
Progress (3): 5.6/8.5 MB | 181 kB | 0.6/1.0 MB
Progress (3): 5.6/8.5 MB | 181 kB | 0.6/1.0 MB
Progress (3): 5.6/8.5 MB | 181 kB | 0.6/1.0 MB
Progress (3): 5.7/8.5 MB | 181 kB | 0.6/1.0 MB
Progress (3): 5.7/8.5 MB | 181 kB | 0.6/1.0 MB
Progress (3): 5.7/8.5 MB | 181 kB | 0.6/1.0 MB
Progress (3): 5.7/8.5 MB | 181 kB | 0.6/1.0 MB
Progress (3): 5.7/8.5 MB | 181 kB | 0.6/1.0 MB
Progress (3): 5.7/8.5 MB | 181 kB | 0.7/1.0 MB
Progress (3): 5.7/8.5 MB | 181 kB | 0.7/1.0 MB
Progress (3): 5.7/8.5 MB | 181 kB | 0.7/1.0 MB
Progress (3): 5.7/8.5 MB | 181 kB | 0.7/1.0 MB
Progress (3): 5.7/8.5 MB | 181 kB | 0.7/1.0 MB
Progress (3): 5.7/8.5 MB | 181 kB | 0.7/1.0 MB
Progress (3): 5.7/8.5 MB | 181 kB | 0.7/1.0 MB
Progress (3): 5.8/8.5 MB | 181 kB | 0.7/1.0 MB
Progress (3): 5.8/8.5 MB | 181 kB | 0.7/1.0 MB
Progress (3): 5.8/8.5 MB | 181 kB | 0.7/1.0 MB
Progress (3): 5.8/8.5 MB | 181 kB | 0.7/1.0 MB
Progress (3): 5.8/8.5 MB | 181 kB | 0.7/1.0 MB
Progress (3): 5.8/8.5 MB | 181 kB | 0.8/1.0 MB
Progress (3): 5.8/8.5 MB | 181 kB | 0.8/1.0 MB
                                              
Downloaded from central: https://repo.maven.apache.org/maven2/org/xmlunit/xmlunit-core/2.10.0/xmlunit-core-2.10.0.jar (181 kB at 247 kB/s)
Progress (2): 5.8/8.5 MB | 0.8/1.0 MB
Progress (2): 5.8/8.5 MB | 0.8/1.0 MB
Progress (2): 5.8/8.5 MB | 0.8/1.0 MB
Progress (2): 5.8/8.5 MB | 0.8/1.0 MB
Progress (2): 5.8/8.5 MB | 0.8/1.0 MB
Progress (2): 5.9/8.5 MB | 0.8/1.0 MB
Progress (2): 5.9/8.5 MB | 0.8/1.0 MB
Progress (2): 5.9/8.5 MB | 0.8/1.0 MB
Progress (2): 5.9/8.5 MB | 0.8/1.0 MB
Progress (2): 5.9/8.5 MB | 0.8/1.0 MB
Progress (2): 5.9/8.5 MB | 0.9/1.0 MB
Progress (2): 5.9/8.5 MB | 0.9/1.0 MB
Progress (2): 5.9/8.5 MB | 0.9/1.0 MB
Progress (2): 5.9/8.5 MB | 0.9/1.0 MB
Progress (2): 5.9/8.5 MB | 0.9/1.0 MB
Progress (2): 5.9/8.5 MB | 0.9/1.0 MB
Progress (2): 5.9/8.5 MB | 0.9/1.0 MB
Progress (2): 5.9/8.5 MB | 0.9/1.0 MB
Progress (2): 6.0/8.5 MB | 0.9/1.0 MB
Progress (2): 6.0/8.5 MB | 0.9/1.0 MB
Progress (2): 6.0/8.5 MB | 0.9/1.0 MB
Progress (2): 6.0/8.5 MB | 1.0/1.0 MB
Progress (2): 6.0/8.5 MB | 1.0/1.0 MB
Progress (2): 6.0/8.5 MB | 1.0/1.0 MB
Progress (2): 6.0/8.5 MB | 1.0/1.0 MB
Progress (2): 6.0/8.5 MB | 1.0/1.0 MB
Progress (2): 6.0/8.5 MB | 1.0/1.0 MB
Progress (2): 6.0/8.5 MB | 1.0/1.0 MB
Progress (2): 6.0/8.5 MB | 1.0/1.0 MB
Progress (2): 6.0/8.5 MB | 1.0/1.0 MB
Progress (2): 6.0/8.5 MB | 1.0 MB    
Progress (2): 6.1/8.5 MB | 1.0 MB
Progress (2): 6.1/8.5 MB | 1.0 MB
Progress (2): 6.1/8.5 MB | 1.0 MB
Progress (2): 6.1/8.5 MB | 1.0 MB
Progress (2): 6.1/8.5 MB | 1.0 MB
Progress (2): 6.1/8.5 MB | 1.0 MB
Progress (2): 6.2/8.5 MB | 1.0 MB
Progress (2): 6.2/8.5 MB | 1.0 MB
Progress (2): 6.2/8.5 MB | 1.0 MB
Progress (2): 6.2/8.5 MB | 1.0 MB
Progress (2): 6.2/8.5 MB | 1.0 MB
Progress (2): 6.2/8.5 MB | 1.0 MB
Progress (2): 6.3/8.5 MB | 1.0 MB
Progress (2): 6.3/8.5 MB | 1.0 MB
Progress (2): 6.3/8.5 MB | 1.0 MB
Progress (2): 6.3/8.5 MB | 1.0 MB
Progress (2): 6.3/8.5 MB | 1.0 MB
Progress (2): 6.3/8.5 MB | 1.0 MB
Progress (2): 6.4/8.5 MB | 1.0 MB
Progress (2): 6.4/8.5 MB | 1.0 MB
Progress (2): 6.4/8.5 MB | 1.0 MB
Progress (2): 6.4/8.5 MB | 1.0 MB
Progress (2): 6.4/8.5 MB | 1.0 MB
Progress (2): 6.4/8.5 MB | 1.0 MB
Progress (2): 6.5/8.5 MB | 1.0 MB
Progress (2): 6.5/8.5 MB | 1.0 MB
Progress (2): 6.5/8.5 MB | 1.0 MB
Progress (2): 6.5/8.5 MB | 1.0 MB
Progress (2): 6.5/8.5 MB | 1.0 MB
Progress (2): 6.5/8.5 MB | 1.0 MB
Progress (2): 6.6/8.5 MB | 1.0 MB
Progress (2): 6.6/8.5 MB | 1.0 MB
                                 
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/spring-test/6.2.6/spring-test-6.2.6.jar (1.0 MB at 1.4 MB/s)
Progress (1): 6.6/8.5 MB
Progress (1): 6.6/8.5 MB
Progress (1): 6.6/8.5 MB
Progress (1): 6.6/8.5 MB
Progress (1): 6.6/8.5 MB
Progress (1): 6.7/8.5 MB
Progress (1): 6.7/8.5 MB
Progress (1): 6.7/8.5 MB
Progress (1): 6.7/8.5 MB
Progress (1): 6.7/8.5 MB
Progress (1): 6.7/8.5 MB
Progress (1): 6.8/8.5 MB
Progress (1): 6.8/8.5 MB
Progress (1): 6.8/8.5 MB
Progress (1): 6.8/8.5 MB
Progress (1): 6.8/8.5 MB
Progress (1): 6.8/8.5 MB
Progress (1): 6.9/8.5 MB
Progress (1): 6.9/8.5 MB
Progress (1): 6.9/8.5 MB
Progress (1): 6.9/8.5 MB
Progress (1): 6.9/8.5 MB
Progress (1): 6.9/8.5 MB
Progress (1): 7.0/8.5 MB
Progress (1): 7.0/8.5 MB
Progress (1): 7.0/8.5 MB
Progress (1): 7.0/8.5 MB
Progress (1): 7.0/8.5 MB
Progress (1): 7.0/8.5 MB
Progress (1): 7.1/8.5 MB
Progress (1): 7.1/8.5 MB
Progress (1): 7.1/8.5 MB
Progress (1): 7.1/8.5 MB
Progress (1): 7.1/8.5 MB
Progress (1): 7.1/8.5 MB
Progress (1): 7.2/8.5 MB
Progress (1): 7.2/8.5 MB
Progress (1): 7.2/8.5 MB
Progress (1): 7.2/8.5 MB
Progress (1): 7.2/8.5 MB
Progress (1): 7.2/8.5 MB
Progress (1): 7.3/8.5 MB
Progress (1): 7.3/8.5 MB
Progress (1): 7.3/8.5 MB
Progress (1): 7.3/8.5 MB
Progress (1): 7.3/8.5 MB
Progress (1): 7.3/8.5 MB
Progress (1): 7.4/8.5 MB
Progress (1): 7.4/8.5 MB
Progress (1): 7.4/8.5 MB
Progress (1): 7.4/8.5 MB
Progress (1): 7.4/8.5 MB
Progress (1): 7.4/8.5 MB
Progress (1): 7.5/8.5 MB
Progress (1): 7.5/8.5 MB
Progress (1): 7.5/8.5 MB
Progress (1): 7.5/8.5 MB
Progress (1): 7.5/8.5 MB
Progress (1): 7.5/8.5 MB
Progress (1): 7.5/8.5 MB
Progress (1): 7.6/8.5 MB
Progress (1): 7.6/8.5 MB
Progress (1): 7.6/8.5 MB
Progress (1): 7.6/8.5 MB
Progress (1): 7.6/8.5 MB
Progress (1): 7.6/8.5 MB
Progress (1): 7.7/8.5 MB
Progress (1): 7.7/8.5 MB
Progress (1): 7.7/8.5 MB
Progress (1): 7.7/8.5 MB
Progress (1): 7.7/8.5 MB
Progress (1): 7.7/8.5 MB
Progress (1): 7.8/8.5 MB
Progress (1): 7.8/8.5 MB
Progress (1): 7.8/8.5 MB
Progress (1): 7.8/8.5 MB
Progress (1): 7.8/8.5 MB
Progress (1): 7.8/8.5 MB
Progress (1): 7.9/8.5 MB
Progress (1): 7.9/8.5 MB
Progress (1): 7.9/8.5 MB
Progress (1): 7.9/8.5 MB
Progress (1): 7.9/8.5 MB
Progress (1): 7.9/8.5 MB
Progress (1): 8.0/8.5 MB
Progress (1): 8.0/8.5 MB
Progress (1): 8.0/8.5 MB
Progress (1): 8.0/8.5 MB
Progress (1): 8.0/8.5 MB
Progress (1): 8.0/8.5 MB
Progress (1): 8.1/8.5 MB
Progress (1): 8.1/8.5 MB
Progress (1): 8.1/8.5 MB
Progress (1): 8.1/8.5 MB
Progress (1): 8.1/8.5 MB
Progress (1): 8.1/8.5 MB
Progress (1): 8.2/8.5 MB
Progress (1): 8.2/8.5 MB
Progress (1): 8.2/8.5 MB
Progress (1): 8.2/8.5 MB
Progress (1): 8.2/8.5 MB
Progress (1): 8.2/8.5 MB
Progress (1): 8.3/8.5 MB
Progress (1): 8.3/8.5 MB
Progress (1): 8.3/8.5 MB
Progress (1): 8.3/8.5 MB
Progress (1): 8.3/8.5 MB
Progress (1): 8.3/8.5 MB
Progress (1): 8.4/8.5 MB
Progress (1): 8.4/8.5 MB
Progress (1): 8.4/8.5 MB
Progress (1): 8.4/8.5 MB
Progress (1): 8.4/8.5 MB
Progress (1): 8.4/8.5 MB
Progress (1): 8.5/8.5 MB
Progress (1): 8.5/8.5 MB
Progress (1): 8.5 MB    
                    
Downloaded from central: https://repo.maven.apache.org/maven2/net/bytebuddy/byte-buddy/1.15.11/byte-buddy-1.15.11.jar (8.5 MB at 10 MB/s)
[INFO] 
[INFO] --- clean:3.4.1:clean (default-clean) @ esocial-novo ---
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/4.0.1/plexus-utils-4.0.1.pom
Progress (1): 7.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/4.0.1/plexus-utils-4.0.1.pom (7.8 kB at 340 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/17/plexus-17.pom
Progress (1): 16/28 kB
Progress (1): 28 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/17/plexus-17.pom (28 kB at 909 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/4.0.1/plexus-utils-4.0.1.jar
Progress (1): 16/193 kB
Progress (1): 33/193 kB
Progress (1): 49/193 kB
Progress (1): 58/193 kB
Progress (1): 75/193 kB
Progress (1): 91/193 kB
Progress (1): 107/193 kB
Progress (1): 124/193 kB
Progress (1): 140/193 kB
Progress (1): 157/193 kB
Progress (1): 173/193 kB
Progress (1): 189/193 kB
Progress (1): 193 kB    
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/4.0.1/plexus-utils-4.0.1.jar (193 kB at 6.2 MB/s)
[INFO] 
[INFO] --- jaxb2:3.1.0:xjc (xjc) @ esocial-novo ---
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-xjc/3.0.0/jaxb-xjc-3.0.0.pom
Progress (1): 16/18 kB
Progress (1): 18 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-xjc/3.0.0/jaxb-xjc-3.0.0.pom (18 kB at 577 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/mvn/jaxb-bundles/3.0.0/jaxb-bundles-3.0.0.pom
Progress (1): 1.3 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/mvn/jaxb-bundles/3.0.0/jaxb-bundles-3.0.0.pom (1.3 kB at 42 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/mvn/jaxb-parent/3.0.0/jaxb-parent-3.0.0.pom
Progress (1): 15/29 kB
Progress (1): 29 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/mvn/jaxb-parent/3.0.0/jaxb-parent-3.0.0.pom (29 kB at 615 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-bom-ext/3.0.0/jaxb-bom-ext-3.0.0.pom
Progress (1): 3.4 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-bom-ext/3.0.0/jaxb-bom-ext-3.0.0.pom (3.4 kB at 55 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/glassfish/jaxb/jaxb-bom/3.0.0/jaxb-bom-3.0.0.pom
Progress (1): 8.9 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/glassfish/jaxb/jaxb-bom/3.0.0/jaxb-bom-3.0.0.pom (8.9 kB at 277 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-core/3.0.0/jaxb-core-3.0.0.pom
Progress (1): 13 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-core/3.0.0/jaxb-core-3.0.0.pom (13 kB at 808 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/jakarta/xml/bind/jakarta.xml.bind-api/3.0.0/jakarta.xml.bind-api-3.0.0.pom
Progress (1): 13 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/jakarta/xml/bind/jakarta.xml.bind-api/3.0.0/jakarta.xml.bind-api-3.0.0.pom (13 kB at 427 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/jakarta/xml/bind/jakarta.xml.bind-api-parent/3.0.0/jakarta.xml.bind-api-parent-3.0.0.pom
Progress (1): 9.5 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/jakarta/xml/bind/jakarta.xml.bind-api-parent/3.0.0/jakarta.xml.bind-api-parent-3.0.0.pom (9.5 kB at 296 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/activation/jakarta.activation/2.0.0/jakarta.activation-2.0.0.pom
Progress (1): 3.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/activation/jakarta.activation/2.0.0/jakarta.activation-2.0.0.pom (3.6 kB at 116 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/activation/all/2.0.0/all-2.0.0.pom
Progress (1): 16/17 kB
Progress (1): 17 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/activation/all/2.0.0/all-2.0.0.pom (17 kB at 537 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-jxc/3.0.0/jaxb-jxc-3.0.0.pom
Progress (1): 15/15 kB
Progress (1): 15 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-jxc/3.0.0/jaxb-jxc-3.0.0.pom (15 kB at 489 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-impl/3.0.0/jaxb-impl-3.0.0.pom
Progress (1): 13 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-impl/3.0.0/jaxb-impl-3.0.0.pom (13 kB at 621 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/thoughtworks/qdox/qdox/2.0.1/qdox-2.0.1.pom
Progress (1): 16 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/thoughtworks/qdox/qdox/2.0.1/qdox-2.0.1.pom (16 kB at 500 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-compiler-api/2.11.1/plexus-compiler-api-2.11.1.pom
Progress (1): 1.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-compiler-api/2.11.1/plexus-compiler-api-2.11.1.pom (1.1 kB at 33 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-compiler/2.11.1/plexus-compiler-2.11.1.pom
Progress (1): 8.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-compiler/2.11.1/plexus-compiler-2.11.1.pom (8.1 kB at 271 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-components/6.6/plexus-components-6.6.pom
Progress (1): 2.7 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-components/6.6/plexus-components-6.6.pom (2.7 kB at 86 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/8/plexus-8.pom
Progress (1): 16/25 kB
Progress (1): 25 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/8/plexus-8.pom (25 kB at 688 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/3.4.1/plexus-utils-3.4.1.pom
Progress (1): 8.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/3.4.1/plexus-utils-3.4.1.pom (8.0 kB at 249 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/3.1.0/plexus-utils-3.1.0.pom
Progress (1): 4.7 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/3.1.0/plexus-utils-3.1.0.pom (4.7 kB at 150 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/4.0/plexus-4.0.pom
Progress (1): 16/22 kB
Progress (1): 22 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/4.0/plexus-4.0.pom (22 kB at 672 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/forge/forge-parent/10/forge-parent-10.pom
Progress (1): 14 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/forge/forge-parent/10/forge-parent-10.pom (14 kB at 399 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/plexus/plexus-build-api/0.0.7/plexus-build-api-0.0.7.pom
Progress (1): 3.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/plexus/plexus-build-api/0.0.7/plexus-build-api-0.0.7.pom (3.2 kB at 114 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/spice/spice-parent/15/spice-parent-15.pom
Progress (1): 8.4 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/spice/spice-parent/15/spice-parent-15.pom (8.4 kB at 557 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/forge/forge-parent/5/forge-parent-5.pom
Progress (1): 8.4 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/forge/forge-parent/5/forge-parent-5.pom (8.4 kB at 40 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/1.5.8/plexus-utils-1.5.8.pom
Progress (1): 8.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/1.5.8/plexus-utils-1.5.8.pom (8.1 kB at 269 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/2.0.2/plexus-2.0.2.pom
Progress (1): 12 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/2.0.2/plexus-2.0.2.pom (12 kB at 363 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/istack/istack-commons-runtime/4.0.0/istack-commons-runtime-4.0.0.pom
Progress (1): 4.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/istack/istack-commons-runtime/4.0.0/istack-commons-runtime-4.0.0.pom (4.8 kB at 102 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/istack/istack-commons/4.0.0/istack-commons-4.0.0.pom
Progress (1): 16/23 kB
Progress (1): 23 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/istack/istack-commons/4.0.0/istack-commons-4.0.0.pom (23 kB at 741 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-xjc/3.0.0/jaxb-xjc-3.0.0.jar
Progress (1): 0/1.9 MB
Progress (1): 0/1.9 MB
Progress (1): 0/1.9 MB
Progress (1): 0.1/1.9 MB
Progress (1): 0.1/1.9 MB
Progress (1): 0.1/1.9 MB
Progress (1): 0.1/1.9 MB
Progress (1): 0.1/1.9 MB
Progress (1): 0.1/1.9 MB
Progress (1): 0.2/1.9 MB
Progress (1): 0.2/1.9 MB
Progress (1): 0.2/1.9 MB
Progress (1): 0.2/1.9 MB
Progress (1): 0.2/1.9 MB
Progress (1): 0.2/1.9 MB
Progress (1): 0.3/1.9 MB
Progress (1): 0.3/1.9 MB
Progress (1): 0.3/1.9 MB
Progress (1): 0.3/1.9 MB
Progress (1): 0.3/1.9 MB
Progress (1): 0.3/1.9 MB
Progress (1): 0.4/1.9 MB
Progress (1): 0.4/1.9 MB
Progress (1): 0.4/1.9 MB
Progress (1): 0.4/1.9 MB
Progress (1): 0.4/1.9 MB
Progress (1): 0.4/1.9 MB
Progress (1): 0.5/1.9 MB
Progress (1): 0.5/1.9 MB
Progress (1): 0.5/1.9 MB
Progress (1): 0.5/1.9 MB
Progress (1): 0.5/1.9 MB
Progress (1): 0.5/1.9 MB
Progress (1): 0.6/1.9 MB
Progress (1): 0.6/1.9 MB
Progress (1): 0.6/1.9 MB
Progress (1): 0.6/1.9 MB
Progress (1): 0.6/1.9 MB
Progress (1): 0.6/1.9 MB
Progress (1): 0.7/1.9 MB
Progress (1): 0.7/1.9 MB
Progress (1): 0.7/1.9 MB
Progress (1): 0.7/1.9 MB
Progress (1): 0.7/1.9 MB
Progress (1): 0.7/1.9 MB
Progress (1): 0.8/1.9 MB
Progress (1): 0.8/1.9 MB
Progress (1): 0.8/1.9 MB
Progress (1): 0.8/1.9 MB
Progress (1): 0.8/1.9 MB
Progress (1): 0.8/1.9 MB
Progress (1): 0.8/1.9 MB
Progress (1): 0.8/1.9 MB
Progress (1): 0.9/1.9 MB
Progress (1): 0.9/1.9 MB
Progress (1): 0.9/1.9 MB
Progress (1): 0.9/1.9 MB
Progress (1): 0.9/1.9 MB
Progress (1): 0.9/1.9 MB
Progress (1): 1.0/1.9 MB
Progress (1): 1.0/1.9 MB
Progress (1): 1.0/1.9 MB
Progress (1): 1.0/1.9 MB
Progress (1): 1.0/1.9 MB
Progress (1): 1.0/1.9 MB
Progress (1): 1.1/1.9 MB
Progress (1): 1.1/1.9 MB
Progress (1): 1.1/1.9 MB
Progress (1): 1.1/1.9 MB
Progress (1): 1.1/1.9 MB
Progress (1): 1.1/1.9 MB
Progress (1): 1.2/1.9 MB
Progress (1): 1.2/1.9 MB
Progress (1): 1.2/1.9 MB
Progress (1): 1.2/1.9 MB
Progress (1): 1.2/1.9 MB
Progress (1): 1.2/1.9 MB
Progress (1): 1.3/1.9 MB
Progress (1): 1.3/1.9 MB
Progress (1): 1.3/1.9 MB
Progress (1): 1.3/1.9 MB
Progress (1): 1.3/1.9 MB
Progress (1): 1.3/1.9 MB
Progress (1): 1.3/1.9 MB
Progress (1): 1.4/1.9 MB
Progress (1): 1.4/1.9 MB
Progress (1): 1.4/1.9 MB
Progress (1): 1.4/1.9 MB
Progress (1): 1.4/1.9 MB
Progress (1): 1.4/1.9 MB
Progress (1): 1.5/1.9 MB
Progress (1): 1.5/1.9 MB
Progress (1): 1.5/1.9 MB
Progress (1): 1.5/1.9 MB
Progress (1): 1.5/1.9 MB
Progress (1): 1.5/1.9 MB
Progress (1): 1.6/1.9 MB
Progress (1): 1.6/1.9 MB
Progress (1): 1.6/1.9 MB
Progress (1): 1.6/1.9 MB
Progress (1): 1.6/1.9 MB
Progress (1): 1.6/1.9 MB
Progress (1): 1.7/1.9 MB
Progress (1): 1.7/1.9 MB
Progress (1): 1.7/1.9 MB
Progress (1): 1.7/1.9 MB
Progress (1): 1.7/1.9 MB
Progress (1): 1.7/1.9 MB
Progress (1): 1.7/1.9 MB
Progress (1): 1.8/1.9 MB
Progress (1): 1.8/1.9 MB
Progress (1): 1.8/1.9 MB
Progress (1): 1.8/1.9 MB
Progress (1): 1.8/1.9 MB
Progress (1): 1.8/1.9 MB
Progress (1): 1.9/1.9 MB
Progress (1): 1.9/1.9 MB
Progress (1): 1.9/1.9 MB
Progress (1): 1.9/1.9 MB
Progress (1): 1.9 MB    
                    
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-xjc/3.0.0/jaxb-xjc-3.0.0.jar (1.9 MB at 40 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-core/3.0.0/jaxb-core-3.0.0.jar
Progress (1): 16/238 kB
Progress (1): 33/238 kB
Progress (1): 49/238 kB
Progress (1): 66/238 kB
Progress (1): 82/238 kB
Progress (1): 98/238 kB
Progress (1): 114/238 kB
Progress (1): 131/238 kB
Progress (1): 147/238 kB
Progress (1): 163/238 kB
Progress (1): 180/238 kB
Progress (1): 196/238 kB
Progress (1): 213/238 kB
Progress (1): 229/238 kB
Progress (1): 238 kB    
                    
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/activation/jakarta.activation/2.0.0/jakarta.activation-2.0.0.jar
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-core/3.0.0/jaxb-core-3.0.0.jar (238 kB at 646 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-compiler-api/2.11.1/plexus-compiler-api-2.11.1.jar
Progress (1): 16/63 kB
Progress (1): 33/63 kB
Progress (1): 49/63 kB
Progress (1): 63 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/activation/jakarta.activation/2.0.0/jakarta.activation-2.0.0.jar (63 kB at 160 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/3.1.0/plexus-utils-3.1.0.jar
Progress (1): 15/27 kB
Progress (1): 27 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-compiler-api/2.11.1/plexus-compiler-api-2.11.1.jar (27 kB at 69 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/plexus/plexus-build-api/0.0.7/plexus-build-api-0.0.7.jar
Progress (1): 16/262 kB
Progress (1): 33/262 kB
Progress (1): 49/262 kB
Progress (1): 62/262 kB
Progress (1): 78/262 kB
Progress (1): 94/262 kB
Progress (1): 111/262 kB
Progress (1): 127/262 kB
Progress (1): 144/262 kB
Progress (1): 160/262 kB
Progress (1): 176/262 kB
Progress (1): 193/262 kB
Progress (1): 209/262 kB
Progress (1): 225/262 kB
Progress (1): 242/262 kB
Progress (1): 258/262 kB
Progress (1): 262 kB    
Progress (2): 262 kB | 8.5 kB
                             
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/3.1.0/plexus-utils-3.1.0.jar (262 kB at 618 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/istack/istack-commons-runtime/4.0.0/istack-commons-runtime-4.0.0.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/plexus/plexus-build-api/0.0.7/plexus-build-api-0.0.7.jar (8.5 kB at 20 kB/s)
Progress (1): 16/30 kB
Progress (1): 30 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/istack/istack-commons-runtime/4.0.0/istack-commons-runtime-4.0.0.jar (30 kB at 68 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-jxc/3.0.0/jaxb-jxc-3.0.0.jar
Progress (1): 16/117 kB
Progress (1): 32/117 kB
Progress (1): 49/117 kB
Progress (1): 65/117 kB
Progress (1): 79/117 kB
Progress (1): 95/117 kB
Progress (1): 112/117 kB
Progress (1): 117 kB    
                    
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-jxc/3.0.0/jaxb-jxc-3.0.0.jar (117 kB at 144 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/thoughtworks/qdox/qdox/2.0.1/qdox-2.0.1.jar
Downloading from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-impl/3.0.0/jaxb-impl-3.0.0.jar
Progress (1): 16/334 kB
Progress (1): 32/334 kB
Progress (1): 49/334 kB
Progress (1): 65/334 kB
Progress (2): 65/334 kB | 16/935 kB
Progress (2): 81/334 kB | 16/935 kB
Progress (2): 98/334 kB | 16/935 kB
Progress (2): 98/334 kB | 33/935 kB
Progress (2): 114/334 kB | 33/935 kB
Progress (2): 130/334 kB | 33/935 kB
Progress (2): 130/334 kB | 49/935 kB
Progress (2): 147/334 kB | 49/935 kB
Progress (2): 147/334 kB | 66/935 kB
Progress (2): 163/334 kB | 66/935 kB
Progress (2): 180/334 kB | 66/935 kB
Progress (2): 180/334 kB | 79/935 kB
Progress (2): 196/334 kB | 79/935 kB
Progress (2): 196/334 kB | 95/935 kB
Progress (2): 212/334 kB | 95/935 kB
Progress (2): 229/334 kB | 95/935 kB
Progress (2): 229/334 kB | 112/935 kB
Progress (2): 245/334 kB | 112/935 kB
Progress (2): 262/334 kB | 112/935 kB
Progress (2): 262/334 kB | 128/935 kB
Progress (2): 278/334 kB | 128/935 kB
Progress (2): 294/334 kB | 128/935 kB
Progress (2): 294/334 kB | 144/935 kB
Progress (2): 311/334 kB | 144/935 kB
Progress (2): 327/334 kB | 144/935 kB
Progress (2): 327/334 kB | 161/935 kB
Progress (2): 334 kB | 161/935 kB    
Progress (2): 334 kB | 177/935 kB
Progress (2): 334 kB | 194/935 kB
Progress (2): 334 kB | 210/935 kB
Progress (2): 334 kB | 226/935 kB
Progress (2): 334 kB | 243/935 kB
Progress (2): 334 kB | 259/935 kB
Progress (2): 334 kB | 275/935 kB
Progress (2): 334 kB | 292/935 kB
Progress (2): 334 kB | 308/935 kB
Progress (2): 334 kB | 325/935 kB
Progress (2): 334 kB | 341/935 kB
Progress (2): 334 kB | 357/935 kB
Progress (2): 334 kB | 374/935 kB
Progress (2): 334 kB | 390/935 kB
Progress (2): 334 kB | 407/935 kB
Progress (2): 334 kB | 423/935 kB
Progress (2): 334 kB | 439/935 kB
Progress (2): 334 kB | 456/935 kB
Progress (2): 334 kB | 472/935 kB
Progress (2): 334 kB | 488/935 kB
Progress (2): 334 kB | 505/935 kB
Progress (2): 334 kB | 521/935 kB
Progress (2): 334 kB | 538/935 kB
Progress (2): 334 kB | 554/935 kB
Progress (2): 334 kB | 570/935 kB
Progress (2): 334 kB | 587/935 kB
Progress (2): 334 kB | 603/935 kB
Progress (2): 334 kB | 620/935 kB
                                 
Downloaded from central: https://repo.maven.apache.org/maven2/com/thoughtworks/qdox/qdox/2.0.1/qdox-2.0.1.jar (334 kB at 263 kB/s)
Progress (1): 636/935 kB
Progress (1): 652/935 kB
Progress (1): 669/935 kB
Progress (1): 685/935 kB
Progress (1): 701/935 kB
Progress (1): 718/935 kB
Progress (1): 734/935 kB
Progress (1): 751/935 kB
Progress (1): 767/935 kB
Progress (1): 783/935 kB
Progress (1): 800/935 kB
Progress (1): 816/935 kB
Progress (1): 833/935 kB
Progress (1): 849/935 kB
Progress (1): 865/935 kB
Progress (1): 882/935 kB
Progress (1): 898/935 kB
Progress (1): 914/935 kB
Progress (1): 931/935 kB
Progress (1): 935 kB    
                    
Downloaded from central: https://repo.maven.apache.org/maven2/com/sun/xml/bind/jaxb-impl/3.0.0/jaxb-impl-3.0.0.jar (935 kB at 722 kB/s)
[INFO] Created EpisodePath [C:\esocial-novo\target\generated-sources\jaxb\META-INF\JAXB]: true
[INFO] Ignored given or default xjbSources [C:\esocial-novo\src\main\xjb], since it is not an existent file or directory.
[INFO] 
[INFO] --- build-helper:3.3.0:add-source (add-source) @ esocial-novo ---
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-core/3.0/maven-core-3.0.pom
Progress (1): 6.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-core/3.0/maven-core-3.0.pom (6.6 kB at 127 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven/3.0/maven-3.0.pom
Progress (1): 16/22 kB
Progress (1): 22 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven/3.0/maven-3.0.pom (22 kB at 466 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/15/maven-parent-15.pom
Progress (1): 16/24 kB
Progress (1): 24 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/15/maven-parent-15.pom (24 kB at 511 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/apache/6/apache-6.pom
Progress (1): 13 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/apache/6/apache-6.pom (13 kB at 400 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-model/3.0/maven-model-3.0.pom
Progress (1): 3.9 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-model/3.0/maven-model-3.0.pom (3.9 kB at 83 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/2.0.4/plexus-utils-2.0.4.pom
Progress (1): 3.3 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/2.0.4/plexus-utils-2.0.4.pom (3.3 kB at 111 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/2.0.6/plexus-2.0.6.pom
Progress (1): 16/17 kB
Progress (1): 17 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/2.0.6/plexus-2.0.6.pom (17 kB at 621 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-settings/3.0/maven-settings-3.0.pom
Progress (1): 1.9 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-settings/3.0/maven-settings-3.0.pom (1.9 kB at 61 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-settings-builder/3.0/maven-settings-builder-3.0.pom
Progress (1): 2.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-settings-builder/3.0/maven-settings-builder-3.0.pom (2.2 kB at 148 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-interpolation/1.14/plexus-interpolation-1.14.pom
Progress (1): 910 B
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-interpolation/1.14/plexus-interpolation-1.14.pom (910 B at 57 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-components/1.1.18/plexus-components-1.1.18.pom
Progress (1): 5.4 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-components/1.1.18/plexus-components-1.1.18.pom (5.4 kB at 173 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/2.0.7/plexus-2.0.7.pom
Progress (1): 16/17 kB
Progress (1): 17 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/2.0.7/plexus-2.0.7.pom (17 kB at 665 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-component-annotations/1.5.5/plexus-component-annotations-1.5.5.pom
Progress (1): 815 B
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-component-annotations/1.5.5/plexus-component-annotations-1.5.5.pom (815 B at 39 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-containers/1.5.5/plexus-containers-1.5.5.pom
Progress (1): 4.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-containers/1.5.5/plexus-containers-1.5.5.pom (4.2 kB at 265 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/plexus/plexus-sec-dispatcher/1.3/plexus-sec-dispatcher-1.3.pom
Progress (1): 3.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/plexus/plexus-sec-dispatcher/1.3/plexus-sec-dispatcher-1.3.pom (3.0 kB at 93 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/spice/spice-parent/12/spice-parent-12.pom
Progress (1): 6.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/spice/spice-parent/12/spice-parent-12.pom (6.8 kB at 206 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/forge/forge-parent/4/forge-parent-4.pom
Progress (1): 8.4 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/forge/forge-parent/4/forge-parent-4.pom (8.4 kB at 254 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/1.5.5/plexus-utils-1.5.5.pom
Progress (1): 5.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/1.5.5/plexus-utils-1.5.5.pom (5.1 kB at 343 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/1.0.11/plexus-1.0.11.pom
Progress (1): 9.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/1.0.11/plexus-1.0.11.pom (9.0 kB at 280 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/plexus/plexus-cipher/1.4/plexus-cipher-1.4.pom
Progress (1): 2.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/plexus/plexus-cipher/1.4/plexus-cipher-1.4.pom (2.1 kB at 65 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-repository-metadata/3.0/maven-repository-metadata-3.0.pom
Progress (1): 1.9 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-repository-metadata/3.0/maven-repository-metadata-3.0.pom (1.9 kB at 60 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-artifact/3.0/maven-artifact-3.0.pom
Progress (1): 1.9 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-artifact/3.0/maven-artifact-3.0.pom (1.9 kB at 120 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-plugin-api/3.8.4/maven-plugin-api-3.8.4.pom
Progress (1): 3.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-plugin-api/3.8.4/maven-plugin-api-3.8.4.pom (3.0 kB at 122 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven/3.8.4/maven-3.8.4.pom
Progress (1): 16/27 kB
Progress (1): 27 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven/3.8.4/maven-3.8.4.pom (27 kB at 1.1 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/34/maven-parent-34.pom
Progress (1): 16/43 kB
Progress (1): 33/43 kB
Progress (1): 43 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/34/maven-parent-34.pom (43 kB at 1.3 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/apache/23/apache-23.pom
Progress (1): 16/18 kB
Progress (1): 18 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/apache/23/apache-23.pom (18 kB at 576 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-model/3.8.4/maven-model-3.8.4.pom
Progress (1): 2.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-model/3.8.4/maven-model-3.8.4.pom (2.8 kB at 90 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/3.3.0/plexus-utils-3.3.0.pom
Progress (1): 5.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/3.3.0/plexus-utils-3.3.0.pom (5.2 kB at 157 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/5.1/plexus-5.1.pom
Progress (1): 16/23 kB
Progress (1): 23 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/5.1/plexus-5.1.pom (23 kB at 1.4 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-artifact/3.8.4/maven-artifact-3.8.4.pom
Progress (1): 2.4 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-artifact/3.8.4/maven-artifact-3.8.4.pom (2.4 kB at 150 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-lang3/3.8.1/commons-lang3-3.8.1.pom
Progress (1): 16/28 kB
Progress (1): 28 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-lang3/3.8.1/commons-lang3-3.8.1.pom (28 kB at 900 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-parent/47/commons-parent-47.pom
Progress (1): 16/78 kB
Progress (1): 32/78 kB
Progress (1): 49/78 kB
Progress (1): 65/78 kB
Progress (1): 78 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-parent/47/commons-parent-47.pom (78 kB at 2.4 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/apache/19/apache-19.pom
Progress (1): 15 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/apache/19/apache-19.pom (15 kB at 774 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/eclipse/sisu/org.eclipse.sisu.plexus/0.3.5/org.eclipse.sisu.plexus-0.3.5.pom
Progress (1): 4.3 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/eclipse/sisu/org.eclipse.sisu.plexus/0.3.5/org.eclipse.sisu.plexus-0.3.5.pom (4.3 kB at 238 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/eclipse/sisu/sisu-plexus/0.3.5/sisu-plexus-0.3.5.pom
Progress (1): 14 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/eclipse/sisu/sisu-plexus/0.3.5/sisu-plexus-0.3.5.pom (14 kB at 473 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/javax/annotation/javax.annotation-api/1.2/javax.annotation-api-1.2.pom
Progress (1): 13 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/javax/annotation/javax.annotation-api/1.2/javax.annotation-api-1.2.pom (13 kB at 420 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/net/java/jvnet-parent/3/jvnet-parent-3.pom
Progress (1): 4.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/net/java/jvnet-parent/3/jvnet-parent-3.pom (4.8 kB at 282 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/eclipse/sisu/org.eclipse.sisu.inject/0.3.5/org.eclipse.sisu.inject-0.3.5.pom
Progress (1): 2.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/eclipse/sisu/org.eclipse.sisu.inject/0.3.5/org.eclipse.sisu.inject-0.3.5.pom (2.6 kB at 85 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/eclipse/sisu/sisu-inject/0.3.5/sisu-inject-0.3.5.pom
Progress (1): 14 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/eclipse/sisu/sisu-inject/0.3.5/sisu-inject-0.3.5.pom (14 kB at 313 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-classworlds/2.5.2/plexus-classworlds-2.5.2.pom
Progress (1): 7.3 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-classworlds/2.5.2/plexus-classworlds-2.5.2.pom (7.3 kB at 192 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/3.3.1/plexus-3.3.1.pom
Progress (1): 16/20 kB
Progress (1): 20 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/3.3.1/plexus-3.3.1.pom (20 kB at 852 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/spice/spice-parent/17/spice-parent-17.pom
Progress (1): 6.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/spice/spice-parent/17/spice-parent-17.pom (6.8 kB at 218 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/3.0.24/plexus-utils-3.0.24.pom
Progress (1): 4.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/3.0.24/plexus-utils-3.0.24.pom (4.1 kB at 133 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-classworlds/2.6.0/plexus-classworlds-2.6.0.pom
Progress (1): 7.9 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-classworlds/2.6.0/plexus-classworlds-2.6.0.pom (7.9 kB at 283 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-model-builder/3.0/maven-model-builder-3.0.pom
Progress (1): 2.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-model-builder/3.0/maven-model-builder-3.0.pom (2.2 kB at 72 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-aether-provider/3.0/maven-aether-provider-3.0.pom
Progress (1): 2.5 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-aether-provider/3.0/maven-aether-provider-3.0.pom (2.5 kB at 80 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/aether/aether-api/1.7/aether-api-1.7.pom
Progress (1): 1.7 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/aether/aether-api/1.7/aether-api-1.7.pom (1.7 kB at 36 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/aether/aether-parent/1.7/aether-parent-1.7.pom
Progress (1): 7.7 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/aether/aether-parent/1.7/aether-parent-1.7.pom (7.7 kB at 483 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/forge/forge-parent/6/forge-parent-6.pom
Progress (1): 11 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/forge/forge-parent/6/forge-parent-6.pom (11 kB at 717 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/aether/aether-util/1.7/aether-util-1.7.pom
Progress (1): 2.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/aether/aether-util/1.7/aether-util-1.7.pom (2.1 kB at 129 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/aether/aether-impl/1.7/aether-impl-1.7.pom
Progress (1): 3.7 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/aether/aether-impl/1.7/aether-impl-1.7.pom (3.7 kB at 119 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/aether/aether-spi/1.7/aether-spi-1.7.pom
Progress (1): 1.7 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/aether/aether-spi/1.7/aether-spi-1.7.pom (1.7 kB at 109 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/sisu/sisu-inject-plexus/1.4.2/sisu-inject-plexus-1.4.2.pom
Progress (1): 5.4 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/sisu/sisu-inject-plexus/1.4.2/sisu-inject-plexus-1.4.2.pom (5.4 kB at 168 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/sisu/inject/guice-plexus/1.4.2/guice-plexus-1.4.2.pom
Progress (1): 3.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/sisu/inject/guice-plexus/1.4.2/guice-plexus-1.4.2.pom (3.1 kB at 101 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/sisu/inject/guice-bean/1.4.2/guice-bean-1.4.2.pom
Progress (1): 2.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/sisu/inject/guice-bean/1.4.2/guice-bean-1.4.2.pom (2.6 kB at 84 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/sisu/sisu-inject/1.4.2/sisu-inject-1.4.2.pom
Progress (1): 1.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/sisu/sisu-inject/1.4.2/sisu-inject-1.4.2.pom (1.2 kB at 40 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/sisu/sisu-parent/1.4.2/sisu-parent-1.4.2.pom
Progress (1): 7.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/sisu/sisu-parent/1.4.2/sisu-parent-1.4.2.pom (7.8 kB at 216 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-component-annotations/1.5.4/plexus-component-annotations-1.5.4.pom
Progress (1): 815 B
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-component-annotations/1.5.4/plexus-component-annotations-1.5.4.pom (815 B at 26 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-containers/1.5.4/plexus-containers-1.5.4.pom
Progress (1): 4.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-containers/1.5.4/plexus-containers-1.5.4.pom (4.2 kB at 137 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/2.0.5/plexus-2.0.5.pom
Progress (1): 16/17 kB
Progress (1): 17 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/2.0.5/plexus-2.0.5.pom (17 kB at 457 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-classworlds/2.2.3/plexus-classworlds-2.2.3.pom
Progress (1): 4.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-classworlds/2.2.3/plexus-classworlds-2.2.3.pom (4.0 kB at 266 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/2.0.5/plexus-utils-2.0.5.pom
Progress (1): 3.3 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/2.0.5/plexus-utils-2.0.5.pom (3.3 kB at 208 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/sisu/sisu-inject-bean/1.4.2/sisu-inject-bean-1.4.2.pom
Progress (1): 5.5 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/sisu/sisu-inject-bean/1.4.2/sisu-inject-bean-1.4.2.pom (5.5 kB at 176 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/sisu/sisu-guice/2.1.7/sisu-guice-2.1.7.pom
Progress (1): 11 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/sisu/sisu-guice/2.1.7/sisu-guice-2.1.7.pom (11 kB at 461 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-plugin-api/3.0/maven-plugin-api-3.0.pom
Progress (1): 2.3 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-plugin-api/3.0/maven-plugin-api-3.0.pom (2.3 kB at 99 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache-extras/beanshell/bsh/2.0b6/bsh-2.0b6.pom
Progress (1): 5.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache-extras/beanshell/bsh/2.0b6/bsh-2.0b6.pom (5.0 kB at 161 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/file-management/3.0.0/file-management-3.0.0.pom
Progress (1): 4.7 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/file-management/3.0.0/file-management-3.0.0.pom (4.7 kB at 247 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-components/22/maven-shared-components-22.pom
Progress (1): 5.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-components/22/maven-shared-components-22.pom (5.1 kB at 138 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/27/maven-parent-27.pom
Progress (1): 16/41 kB
Progress (1): 32/41 kB
Progress (1): 41 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/27/maven-parent-27.pom (41 kB at 1.5 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/apache/17/apache-17.pom
Progress (1): 16/16 kB
Progress (1): 16 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/apache/17/apache-17.pom (16 kB at 1.0 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-io/3.0.0/maven-shared-io-3.0.0.pom
Progress (1): 4.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-io/3.0.0/maven-shared-io-3.0.0.pom (4.2 kB at 154 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-compat/3.0/maven-compat-3.0.pom
Progress (1): 4.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-compat/3.0/maven-compat-3.0.pom (4.0 kB at 134 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/wagon/wagon-provider-api/1.0-beta-6/wagon-provider-api-1.0-beta-6.pom
Progress (1): 1.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/wagon/wagon-provider-api/1.0-beta-6/wagon-provider-api-1.0-beta-6.pom (1.8 kB at 65 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/wagon/wagon/1.0-beta-6/wagon-1.0-beta-6.pom
Progress (1): 12 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/wagon/wagon/1.0-beta-6/wagon-1.0-beta-6.pom (12 kB at 476 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/11/maven-parent-11.pom
Progress (1): 16/32 kB
Progress (1): 32 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/11/maven-parent-11.pom (32 kB at 2.3 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/apache/5/apache-5.pom
Progress (1): 4.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/apache/5/apache-5.pom (4.1 kB at 132 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/1.4.2/plexus-utils-1.4.2.pom
Progress (1): 2.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/1.4.2/plexus-utils-1.4.2.pom (2.0 kB at 61 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/wagon/wagon-provider-api/2.10/wagon-provider-api-2.10.pom
Progress (1): 1.7 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/wagon/wagon-provider-api/2.10/wagon-provider-api-2.10.pom (1.7 kB at 54 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/wagon/wagon/2.10/wagon-2.10.pom
Progress (1): 16/21 kB
Progress (1): 21 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/wagon/wagon/2.10/wagon-2.10.pom (21 kB at 662 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/26/maven-parent-26.pom
Progress (1): 16/40 kB
Progress (1): 33/40 kB
Progress (1): 40 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/26/maven-parent-26.pom (40 kB at 2.5 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/apache/16/apache-16.pom
Progress (1): 15 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/apache/16/apache-16.pom (15 kB at 810 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/3.0.15/plexus-utils-3.0.15.pom
Progress (1): 3.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/3.0.15/plexus-utils-3.0.15.pom (3.1 kB at 157 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-utils/3.0.0/maven-shared-utils-3.0.0.pom
Progress (1): 5.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-utils/3.0.0/maven-shared-utils-3.0.0.pom (5.6 kB at 207 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-components/21/maven-shared-components-21.pom
Progress (1): 5.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-components/21/maven-shared-components-21.pom (5.1 kB at 160 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/25/maven-parent-25.pom
Progress (1): 16/37 kB
Progress (1): 32/37 kB
Progress (1): 37 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/25/maven-parent-25.pom (37 kB at 1.2 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/apache/15/apache-15.pom
Progress (1): 15 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/apache/15/apache-15.pom (15 kB at 476 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/commons-io/commons-io/2.4/commons-io-2.4.pom
Progress (1): 10 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/commons-io/commons-io/2.4/commons-io-2.4.pom (10 kB at 254 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-parent/25/commons-parent-25.pom
Progress (1): 16/48 kB
Progress (1): 33/48 kB
Progress (1): 48 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-parent/25/commons-parent-25.pom (48 kB at 2.0 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/apache/9/apache-9.pom
Progress (1): 15 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/apache/9/apache-9.pom (15 kB at 659 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/google/code/findbugs/jsr305/2.0.1/jsr305-2.0.1.pom
Progress (1): 965 B
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/google/code/findbugs/jsr305/2.0.1/jsr305-2.0.1.pom (965 B at 44 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/3.0.22/plexus-utils-3.0.22.pom
Progress (1): 3.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/3.0.22/plexus-utils-3.0.22.pom (3.8 kB at 182 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-core/3.0/maven-core-3.0.jar
Progress (1): 16/527 kB
Progress (1): 33/527 kB
Progress (1): 49/527 kB
Progress (1): 64/527 kB
Progress (1): 80/527 kB
Progress (1): 97/527 kB
Progress (1): 113/527 kB
Progress (1): 129/527 kB
Progress (1): 146/527 kB
Progress (1): 162/527 kB
Progress (1): 179/527 kB
Progress (1): 195/527 kB
Progress (1): 211/527 kB
Progress (1): 228/527 kB
Progress (1): 244/527 kB
Progress (1): 260/527 kB
Progress (1): 277/527 kB
Progress (1): 293/527 kB
Progress (1): 310/527 kB
Progress (1): 326/527 kB
Progress (1): 342/527 kB
Progress (1): 359/527 kB
Progress (1): 375/527 kB
Progress (1): 392/527 kB
Progress (1): 408/527 kB
Progress (1): 424/527 kB
Progress (1): 441/527 kB
Progress (1): 457/527 kB
Progress (1): 473/527 kB
Progress (1): 490/527 kB
Progress (1): 506/527 kB
Progress (1): 523/527 kB
Progress (1): 527 kB    
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-core/3.0/maven-core-3.0.jar (527 kB at 13 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-artifact/3.0/maven-artifact-3.0.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-model/3.0/maven-model-3.0.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-settings/3.0/maven-settings-3.0.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-settings-builder/3.0/maven-settings-builder-3.0.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-repository-metadata/3.0/maven-repository-metadata-3.0.jar
Progress (1): 16/52 kB
Progress (1): 33/52 kB
Progress (1): 49/52 kB
Progress (1): 52 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-artifact/3.0/maven-artifact-3.0.jar (52 kB at 433 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-model-builder/3.0/maven-model-builder-3.0.jar
Progress (1): 16/38 kB
Progress (2): 16/38 kB | 16/30 kB
Progress (3): 16/38 kB | 16/30 kB | 16/47 kB
Progress (3): 33/38 kB | 16/30 kB | 16/47 kB
Progress (3): 33/38 kB | 30 kB | 16/47 kB   
Progress (3): 38 kB | 30 kB | 16/47 kB   
Progress (4): 38 kB | 30 kB | 16/47 kB | 16/165 kB
Progress (4): 38 kB | 30 kB | 32/47 kB | 16/165 kB
Progress (4): 38 kB | 30 kB | 47 kB | 16/165 kB   
Progress (4): 38 kB | 30 kB | 47 kB | 32/165 kB
Progress (4): 38 kB | 30 kB | 47 kB | 49/165 kB
Progress (4): 38 kB | 30 kB | 47 kB | 65/165 kB
Progress (4): 38 kB | 30 kB | 47 kB | 81/165 kB
Progress (4): 38 kB | 30 kB | 47 kB | 98/165 kB
Progress (4): 38 kB | 30 kB | 47 kB | 114/165 kB
Progress (4): 38 kB | 30 kB | 47 kB | 130/165 kB
Progress (4): 38 kB | 30 kB | 47 kB | 147/165 kB
Progress (4): 38 kB | 30 kB | 47 kB | 163/165 kB
Progress (4): 38 kB | 30 kB | 47 kB | 165 kB    
                                            
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-repository-metadata/3.0/maven-repository-metadata-3.0.jar (30 kB at 212 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-aether-provider/3.0/maven-aether-provider-3.0.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-settings-builder/3.0/maven-settings-builder-3.0.jar (38 kB at 266 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/aether/aether-impl/1.7/aether-impl-1.7.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-settings/3.0/maven-settings-3.0.jar (47 kB at 329 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/aether/aether-spi/1.7/aether-spi-1.7.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-model/3.0/maven-model-3.0.jar (165 kB at 1.1 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/aether/aether-api/1.7/aether-api-1.7.jar
Progress (1): 16/148 kB
Progress (1): 32/148 kB
Progress (1): 49/148 kB
Progress (1): 65/148 kB
Progress (1): 81/148 kB
Progress (1): 98/148 kB
Progress (1): 114/148 kB
Progress (1): 130/148 kB
Progress (1): 147/148 kB
Progress (1): 148 kB    
Progress (2): 148 kB | 16/51 kB
Progress (3): 148 kB | 16/51 kB | 16/106 kB
Progress (3): 148 kB | 32/51 kB | 16/106 kB
Progress (3): 148 kB | 32/51 kB | 33/106 kB
Progress (4): 148 kB | 32/51 kB | 33/106 kB | 14 kB
Progress (4): 148 kB | 49/51 kB | 33/106 kB | 14 kB
Progress (4): 148 kB | 49/51 kB | 49/106 kB | 14 kB
Progress (4): 148 kB | 51 kB | 49/106 kB | 14 kB   
Progress (4): 148 kB | 51 kB | 66/106 kB | 14 kB
Progress (4): 148 kB | 51 kB | 82/106 kB | 14 kB
Progress (4): 148 kB | 51 kB | 98/106 kB | 14 kB
                                                
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-model-builder/3.0/maven-model-builder-3.0.jar (148 kB at 1.0 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/aether/aether-util/1.7/aether-util-1.7.jar
Progress (3): 51 kB | 106 kB | 14 kB
Progress (4): 51 kB | 106 kB | 14 kB | 16/74 kB
Progress (4): 51 kB | 106 kB | 14 kB | 32/74 kB
Progress (4): 51 kB | 106 kB | 14 kB | 49/74 kB
Progress (4): 51 kB | 106 kB | 14 kB | 65/74 kB
Progress (4): 51 kB | 106 kB | 14 kB | 74 kB   
                                            
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/aether/aether-spi/1.7/aether-spi-1.7.jar (14 kB at 86 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/sisu/sisu-inject-plexus/1.4.2/sisu-inject-plexus-1.4.2.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-aether-provider/3.0/maven-aether-provider-3.0.jar (51 kB at 324 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/sisu/sisu-inject-bean/1.4.2/sisu-inject-bean-1.4.2.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/aether/aether-impl/1.7/aether-impl-1.7.jar (106 kB at 673 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/sisu/sisu-guice/2.1.7/sisu-guice-2.1.7-noaop.jar
Progress (2): 74 kB | 16/108 kB
                               
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/aether/aether-api/1.7/aether-api-1.7.jar (74 kB at 469 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-interpolation/1.14/plexus-interpolation-1.14.jar
Progress (1): 33/108 kB
Progress (1): 49/108 kB
Progress (1): 66/108 kB
Progress (1): 82/108 kB
Progress (1): 98/108 kB
Progress (1): 108 kB   
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/aether/aether-util/1.7/aether-util-1.7.jar (108 kB at 620 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-classworlds/2.2.3/plexus-classworlds-2.2.3.jar
Progress (1): 16/472 kB
Progress (2): 16/472 kB | 16/153 kB
Progress (2): 33/472 kB | 16/153 kB
Progress (3): 33/472 kB | 16/153 kB | 16/202 kB
Progress (3): 33/472 kB | 33/153 kB | 16/202 kB
Progress (3): 49/472 kB | 33/153 kB | 16/202 kB
Progress (3): 49/472 kB | 49/153 kB | 16/202 kB
Progress (3): 49/472 kB | 49/153 kB | 32/202 kB
Progress (3): 65/472 kB | 49/153 kB | 32/202 kB
Progress (3): 65/472 kB | 64/153 kB | 32/202 kB
Progress (3): 65/472 kB | 64/153 kB | 49/202 kB
Progress (3): 65/472 kB | 80/153 kB | 49/202 kB
Progress (3): 65/472 kB | 80/153 kB | 65/202 kB
Progress (3): 81/472 kB | 80/153 kB | 65/202 kB
Progress (3): 81/472 kB | 96/153 kB | 65/202 kB
Progress (3): 81/472 kB | 96/153 kB | 81/202 kB
Progress (3): 81/472 kB | 113/153 kB | 81/202 kB
Progress (3): 97/472 kB | 113/153 kB | 81/202 kB
Progress (3): 97/472 kB | 113/153 kB | 98/202 kB
Progress (3): 97/472 kB | 129/153 kB | 98/202 kB
Progress (3): 114/472 kB | 129/153 kB | 98/202 kB
Progress (3): 114/472 kB | 129/153 kB | 114/202 kB
Progress (3): 114/472 kB | 145/153 kB | 114/202 kB
Progress (3): 130/472 kB | 145/153 kB | 114/202 kB
Progress (3): 130/472 kB | 153 kB | 114/202 kB    
Progress (3): 130/472 kB | 153 kB | 130/202 kB
Progress (3): 147/472 kB | 153 kB | 130/202 kB
Progress (3): 147/472 kB | 153 kB | 147/202 kB
Progress (3): 163/472 kB | 153 kB | 147/202 kB
Progress (3): 163/472 kB | 153 kB | 163/202 kB
Progress (3): 179/472 kB | 153 kB | 163/202 kB
Progress (3): 179/472 kB | 153 kB | 180/202 kB
Progress (4): 179/472 kB | 153 kB | 180/202 kB | 16/61 kB
Progress (4): 196/472 kB | 153 kB | 180/202 kB | 16/61 kB
Progress (4): 196/472 kB | 153 kB | 196/202 kB | 16/61 kB
Progress (4): 196/472 kB | 153 kB | 196/202 kB | 33/61 kB
Progress (4): 196/472 kB | 153 kB | 202 kB | 33/61 kB    
Progress (4): 212/472 kB | 153 kB | 202 kB | 33/61 kB
Progress (4): 212/472 kB | 153 kB | 202 kB | 49/61 kB
Progress (4): 229/472 kB | 153 kB | 202 kB | 49/61 kB
Progress (4): 229/472 kB | 153 kB | 202 kB | 61 kB   
Progress (4): 245/472 kB | 153 kB | 202 kB | 61 kB
Progress (4): 261/472 kB | 153 kB | 202 kB | 61 kB
Progress (4): 278/472 kB | 153 kB | 202 kB | 61 kB
Progress (4): 294/472 kB | 153 kB | 202 kB | 61 kB
Progress (4): 310/472 kB | 153 kB | 202 kB | 61 kB
Progress (4): 327/472 kB | 153 kB | 202 kB | 61 kB
Progress (4): 343/472 kB | 153 kB | 202 kB | 61 kB
Progress (4): 360/472 kB | 153 kB | 202 kB | 61 kB
Progress (4): 376/472 kB | 153 kB | 202 kB | 61 kB
Progress (4): 392/472 kB | 153 kB | 202 kB | 61 kB
Progress (4): 409/472 kB | 153 kB | 202 kB | 61 kB
Progress (4): 425/472 kB | 153 kB | 202 kB | 61 kB
Progress (4): 442/472 kB | 153 kB | 202 kB | 61 kB
Progress (4): 458/472 kB | 153 kB | 202 kB | 61 kB
Progress (4): 472 kB | 153 kB | 202 kB | 61 kB    
                                              
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/sisu/sisu-inject-bean/1.4.2/sisu-inject-bean-1.4.2.jar (153 kB at 864 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-component-annotations/1.5.5/plexus-component-annotations-1.5.5.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/sisu/sisu-inject-plexus/1.4.2/sisu-inject-plexus-1.4.2.jar (202 kB at 1.0 MB/s)
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-interpolation/1.14/plexus-interpolation-1.14.jar (61 kB at 312 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/plexus/plexus-sec-dispatcher/1.3/plexus-sec-dispatcher-1.3.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/sonatype/plexus/plexus-cipher/1.4/plexus-cipher-1.4.jar
Progress (2): 472 kB | 16/46 kB
Progress (2): 472 kB | 33/46 kB
Progress (2): 472 kB | 46 kB   
                            
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/sisu/sisu-guice/2.1.7/sisu-guice-2.1.7-noaop.jar (472 kB at 2.4 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-plugin-api/3.0/maven-plugin-api-3.0.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-classworlds/2.2.3/plexus-classworlds-2.2.3.jar (46 kB at 235 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/3.0.24/plexus-utils-3.0.24.jar
Progress (1): 4.2 kB
Progress (2): 4.2 kB | 16/29 kB
Progress (3): 4.2 kB | 16/29 kB | 13 kB
Progress (3): 4.2 kB | 29 kB | 13 kB   
Progress (4): 4.2 kB | 29 kB | 13 kB | 16/49 kB
Progress (4): 4.2 kB | 29 kB | 13 kB | 32/49 kB
Progress (4): 4.2 kB | 29 kB | 13 kB | 49/49 kB
Progress (4): 4.2 kB | 29 kB | 13 kB | 49 kB   
                                            
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-component-annotations/1.5.5/plexus-component-annotations-1.5.5.jar (4.2 kB at 20 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache-extras/beanshell/bsh/2.0b6/bsh-2.0b6.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/plexus/plexus-sec-dispatcher/1.3/plexus-sec-dispatcher-1.3.jar (29 kB at 137 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/file-management/3.0.0/file-management-3.0.0.jar
Progress (3): 13 kB | 49 kB | 15/247 kB
Progress (3): 13 kB | 49 kB | 31/247 kB
Progress (3): 13 kB | 49 kB | 48/247 kB
Progress (3): 13 kB | 49 kB | 64/247 kB
Progress (3): 13 kB | 49 kB | 80/247 kB
Progress (3): 13 kB | 49 kB | 97/247 kB
                                       
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-plugin-api/3.0/maven-plugin-api-3.0.jar (49 kB at 220 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-io/3.0.0/maven-shared-io-3.0.0.jar
Progress (2): 13 kB | 113/247 kB
Progress (2): 13 kB | 130/247 kB
Progress (2): 13 kB | 146/247 kB
Progress (2): 13 kB | 162/247 kB
Progress (2): 13 kB | 179/247 kB
Progress (2): 13 kB | 195/247 kB
Progress (2): 13 kB | 208/247 kB
Progress (2): 13 kB | 225/247 kB
Progress (2): 13 kB | 241/247 kB
Progress (2): 13 kB | 247 kB    
Progress (3): 13 kB | 247 kB | 16/389 kB
Progress (3): 13 kB | 247 kB | 33/389 kB
Progress (3): 13 kB | 247 kB | 49/389 kB
Progress (3): 13 kB | 247 kB | 66/389 kB
                                        
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/3.0.24/plexus-utils-3.0.24.jar (247 kB at 1.1 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-compat/3.0/maven-compat-3.0.jar
Progress (2): 13 kB | 80/389 kB
Progress (2): 13 kB | 96/389 kB
Progress (2): 13 kB | 113/389 kB
Progress (2): 13 kB | 129/389 kB
Progress (2): 13 kB | 145/389 kB
Progress (2): 13 kB | 162/389 kB
Progress (2): 13 kB | 178/389 kB
Progress (2): 13 kB | 195/389 kB
Progress (2): 13 kB | 211/389 kB
Progress (2): 13 kB | 227/389 kB
Progress (2): 13 kB | 244/389 kB
Progress (2): 13 kB | 260/389 kB
Progress (2): 13 kB | 276/389 kB
Progress (2): 13 kB | 293/389 kB
Progress (2): 13 kB | 309/389 kB
Progress (2): 13 kB | 326/389 kB
Progress (3): 13 kB | 326/389 kB | 16/41 kB
Progress (3): 13 kB | 342/389 kB | 16/41 kB
Progress (3): 13 kB | 342/389 kB | 33/41 kB
Progress (3): 13 kB | 358/389 kB | 33/41 kB
Progress (3): 13 kB | 358/389 kB | 41 kB   
Progress (3): 13 kB | 375/389 kB | 41 kB
Progress (3): 13 kB | 389 kB | 41 kB    
                                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-io/3.0.0/maven-shared-io-3.0.0.jar (41 kB at 173 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/wagon/wagon-provider-api/2.10/wagon-provider-api-2.10.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache-extras/beanshell/bsh/2.0b6/bsh-2.0b6.jar (389 kB at 1.6 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-utils/3.0.0/maven-shared-utils-3.0.0.jar
Progress (2): 13 kB | 16/285 kB
Progress (2): 13 kB | 33/285 kB
Progress (2): 13 kB | 49/285 kB
Progress (2): 13 kB | 66/285 kB
Progress (2): 13 kB | 82/285 kB
Progress (2): 13 kB | 98/285 kB
Progress (2): 13 kB | 115/285 kB
Progress (2): 13 kB | 131/285 kB
Progress (2): 13 kB | 147/285 kB
Progress (2): 13 kB | 164/285 kB
Progress (2): 13 kB | 180/285 kB
Progress (3): 13 kB | 180/285 kB | 16/35 kB
Progress (3): 13 kB | 197/285 kB | 16/35 kB
Progress (3): 13 kB | 197/285 kB | 33/35 kB
Progress (3): 13 kB | 197/285 kB | 35 kB   
Progress (3): 13 kB | 213/285 kB | 35 kB
Progress (3): 13 kB | 229/285 kB | 35 kB
Progress (3): 13 kB | 246/285 kB | 35 kB
Progress (3): 13 kB | 262/285 kB | 35 kB
Progress (3): 13 kB | 279/285 kB | 35 kB
Progress (3): 13 kB | 285 kB | 35 kB    
Progress (4): 13 kB | 285 kB | 35 kB | 16/54 kB
                                               
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/file-management/3.0.0/file-management-3.0.0.jar (35 kB at 140 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/commons-io/commons-io/2.4/commons-io-2.4.jar
Progress (3): 13 kB | 285 kB | 32/54 kB
Progress (3): 13 kB | 285 kB | 49/54 kB
Progress (3): 13 kB | 285 kB | 54 kB   
Progress (4): 13 kB | 285 kB | 54 kB | 16/155 kB
Progress (4): 13 kB | 285 kB | 54 kB | 33/155 kB
Progress (4): 13 kB | 285 kB | 54 kB | 49/155 kB
Progress (4): 13 kB | 285 kB | 54 kB | 66/155 kB
Progress (4): 13 kB | 285 kB | 54 kB | 82/155 kB
Progress (4): 13 kB | 285 kB | 54 kB | 98/155 kB
Progress (4): 13 kB | 285 kB | 54 kB | 115/155 kB
Progress (4): 13 kB | 285 kB | 54 kB | 131/155 kB
Progress (4): 13 kB | 285 kB | 54 kB | 147/155 kB
Progress (4): 13 kB | 285 kB | 54 kB | 155 kB    
                                             
Downloaded from central: https://repo.maven.apache.org/maven2/org/sonatype/plexus/plexus-cipher/1.4/plexus-cipher-1.4.jar (13 kB at 53 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/google/code/findbugs/jsr305/2.0.1/jsr305-2.0.1.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-compat/3.0/maven-compat-3.0.jar (285 kB at 1.1 MB/s)
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-utils/3.0.0/maven-shared-utils-3.0.0.jar (155 kB at 611 kB/s)
Progress (2): 54 kB | 16/185 kB
                               
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/wagon/wagon-provider-api/2.10/wagon-provider-api-2.10.jar (54 kB at 199 kB/s)
Progress (1): 33/185 kB
Progress (1): 49/185 kB
Progress (1): 62/185 kB
Progress (1): 78/185 kB
Progress (1): 95/185 kB
Progress (1): 111/185 kB
Progress (1): 128/185 kB
Progress (1): 144/185 kB
Progress (1): 160/185 kB
Progress (1): 177/185 kB
Progress (1): 185 kB    
Progress (2): 185 kB | 16/32 kB
Progress (2): 185 kB | 32 kB   
                            
Downloaded from central: https://repo.maven.apache.org/maven2/commons-io/commons-io/2.4/commons-io-2.4.jar (185 kB at 686 kB/s)
Downloaded from central: https://repo.maven.apache.org/maven2/com/google/code/findbugs/jsr305/2.0.1/jsr305-2.0.1.jar (32 kB at 118 kB/s)
[INFO] Source directory: C:\esocial-novo\target\generated-sources\jaxb added.
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ esocial-novo ---
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-interpolation/1.26/plexus-interpolation-1.26.pom
Progress (1): 2.7 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-interpolation/1.26/plexus-interpolation-1.26.pom (2.7 kB at 83 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/3.5.1/plexus-utils-3.5.1.pom
Progress (1): 8.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/3.5.1/plexus-utils-3.5.1.pom (8.8 kB at 283 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/10/plexus-10.pom
Progress (1): 16/25 kB
Progress (1): 25 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/10/plexus-10.pom (25 kB at 819 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-filtering/3.3.1/maven-filtering-3.3.1.pom
Progress (1): 6.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-filtering/3.3.1/maven-filtering-3.3.1.pom (6.0 kB at 151 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-components/39/maven-shared-components-39.pom
Progress (1): 3.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-components/39/maven-shared-components-39.pom (3.2 kB at 140 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/javax/inject/javax.inject/1/javax.inject-1.pom
Progress (1): 612 B
                   
Downloaded from central: https://repo.maven.apache.org/maven2/javax/inject/javax.inject/1/javax.inject-1.pom (612 B at 41 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.pom
Progress (1): 2.7 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.pom (2.7 kB at 102 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/slf4j/slf4j-parent/1.7.36/slf4j-parent-1.7.36.pom
Progress (1): 14 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/slf4j/slf4j-parent/1.7.36/slf4j-parent-1.7.36.pom (14 kB at 881 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/3.5.0/plexus-utils-3.5.0.pom
Progress (1): 8.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/3.5.0/plexus-utils-3.5.0.pom (8.0 kB at 501 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/commons-io/commons-io/2.11.0/commons-io-2.11.0.pom
Progress (1): 16/20 kB
Progress (1): 20 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/commons-io/commons-io/2.11.0/commons-io-2.11.0.pom (20 kB at 636 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-parent/52/commons-parent-52.pom
Progress (1): 16/79 kB
Progress (1): 33/79 kB
Progress (1): 49/79 kB
Progress (1): 66/79 kB
Progress (1): 79 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-parent/52/commons-parent-52.pom (79 kB at 2.6 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/junit/junit-bom/5.7.2/junit-bom-5.7.2.pom
Progress (1): 5.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/junit/junit-bom/5.7.2/junit-bom-5.7.2.pom (5.1 kB at 196 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.pom
Progress (1): 16/31 kB
Progress (1): 31 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.pom (31 kB at 989 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/junit/junit-bom/5.7.1/junit-bom-5.7.1.pom
Progress (1): 5.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/junit/junit-bom/5.7.1/junit-bom-5.7.1.pom (5.1 kB at 159 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-interpolation/1.26/plexus-interpolation-1.26.jar
Progress (1): 16/85 kB
Progress (1): 33/85 kB
Progress (1): 49/85 kB
Progress (1): 66/85 kB
Progress (1): 82/85 kB
Progress (1): 85 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-interpolation/1.26/plexus-interpolation-1.26.jar (85 kB at 5.3 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/3.5.1/plexus-utils-3.5.1.jar
Progress (1): 16/269 kB
Progress (1): 32/269 kB
Progress (1): 49/269 kB
Progress (1): 63/269 kB
Progress (1): 79/269 kB
Progress (1): 96/269 kB
Progress (1): 112/269 kB
Progress (1): 128/269 kB
Progress (1): 145/269 kB
Progress (1): 161/269 kB
Progress (1): 178/269 kB
Progress (1): 194/269 kB
Progress (1): 210/269 kB
Progress (1): 227/269 kB
Progress (1): 243/269 kB
Progress (1): 260/269 kB
Progress (1): 269 kB    
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/3.5.1/plexus-utils-3.5.1.jar (269 kB at 4.3 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-filtering/3.3.1/maven-filtering-3.3.1.jar
Downloading from central: https://repo.maven.apache.org/maven2/javax/inject/javax.inject/1/javax.inject-1.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar
Downloading from central: https://repo.maven.apache.org/maven2/commons-io/commons-io/2.11.0/commons-io-2.11.0.jar
Progress (1): 16/587 kB
Progress (1): 33/587 kB
Progress (1): 49/587 kB
Progress (1): 66/587 kB
Progress (1): 82/587 kB
Progress (1): 98/587 kB
Progress (1): 115/587 kB
Progress (1): 131/587 kB
Progress (1): 147/587 kB
Progress (1): 164/587 kB
Progress (1): 180/587 kB
Progress (1): 197/587 kB
Progress (1): 213/587 kB
Progress (1): 229/587 kB
Progress (1): 246/587 kB
Progress (1): 262/587 kB
Progress (1): 279/587 kB
Progress (1): 295/587 kB
Progress (1): 311/587 kB
Progress (1): 328/587 kB
Progress (1): 344/587 kB
Progress (1): 360/587 kB
Progress (1): 377/587 kB
Progress (1): 393/587 kB
Progress (1): 410/587 kB
Progress (1): 426/587 kB
Progress (1): 442/587 kB
Progress (1): 459/587 kB
Progress (1): 475/587 kB
Progress (1): 492/587 kB
Progress (1): 508/587 kB
Progress (1): 524/587 kB
Progress (1): 541/587 kB
Progress (2): 541/587 kB | 16/55 kB
Progress (2): 557/587 kB | 16/55 kB
Progress (2): 573/587 kB | 16/55 kB
Progress (2): 573/587 kB | 32/55 kB
Progress (2): 585/587 kB | 32/55 kB
Progress (2): 585/587 kB | 49/55 kB
Progress (2): 587 kB | 49/55 kB    
Progress (2): 587 kB | 55 kB   
Progress (3): 587 kB | 55 kB | 2.5 kB
Progress (4): 587 kB | 55 kB | 2.5 kB | 16/41 kB
Progress (4): 587 kB | 55 kB | 2.5 kB | 33/41 kB
Progress (4): 587 kB | 55 kB | 2.5 kB | 41 kB   
Progress (5): 587 kB | 55 kB | 2.5 kB | 41 kB | 16/327 kB
Progress (5): 587 kB | 55 kB | 2.5 kB | 41 kB | 33/327 kB
Progress (5): 587 kB | 55 kB | 2.5 kB | 41 kB | 49/327 kB
Progress (5): 587 kB | 55 kB | 2.5 kB | 41 kB | 63/327 kB
Progress (5): 587 kB | 55 kB | 2.5 kB | 41 kB | 79/327 kB
Progress (5): 587 kB | 55 kB | 2.5 kB | 41 kB | 95/327 kB
Progress (5): 587 kB | 55 kB | 2.5 kB | 41 kB | 112/327 kB
Progress (5): 587 kB | 55 kB | 2.5 kB | 41 kB | 128/327 kB
Progress (5): 587 kB | 55 kB | 2.5 kB | 41 kB | 145/327 kB
Progress (5): 587 kB | 55 kB | 2.5 kB | 41 kB | 161/327 kB
Progress (5): 587 kB | 55 kB | 2.5 kB | 41 kB | 177/327 kB
Progress (5): 587 kB | 55 kB | 2.5 kB | 41 kB | 194/327 kB
Progress (5): 587 kB | 55 kB | 2.5 kB | 41 kB | 210/327 kB
Progress (5): 587 kB | 55 kB | 2.5 kB | 41 kB | 226/327 kB
Progress (5): 587 kB | 55 kB | 2.5 kB | 41 kB | 243/327 kB
Progress (5): 587 kB | 55 kB | 2.5 kB | 41 kB | 259/327 kB
Progress (5): 587 kB | 55 kB | 2.5 kB | 41 kB | 276/327 kB
Progress (5): 587 kB | 55 kB | 2.5 kB | 41 kB | 292/327 kB
                                                          
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar (587 kB at 6.3 MB/s)
Progress (4): 55 kB | 2.5 kB | 41 kB | 308/327 kB
Progress (4): 55 kB | 2.5 kB | 41 kB | 325/327 kB
Progress (4): 55 kB | 2.5 kB | 41 kB | 327 kB    
                                             
Downloaded from central: https://repo.maven.apache.org/maven2/javax/inject/javax.inject/1/javax.inject-1.jar (2.5 kB at 27 kB/s)
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-filtering/3.3.1/maven-filtering-3.3.1.jar (55 kB at 504 kB/s)
Downloaded from central: https://repo.maven.apache.org/maven2/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar (41 kB at 377 kB/s)
Downloaded from central: https://repo.maven.apache.org/maven2/commons-io/commons-io/2.11.0/commons-io-2.11.0.jar (327 kB at 3.0 MB/s)
[INFO] Copying 1 resource from src\main\resources to target\classes
[INFO] Copying 5 resources from src\main\resources to target\classes
[INFO] Copying 1 resource from target\generated-sources\jaxb to target\classes
[INFO] 
[INFO] --- compiler:3.13.0:compile (default-compile) @ esocial-novo ---
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-utils/3.4.2/maven-shared-utils-3.4.2.pom
Progress (1): 5.9 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-utils/3.4.2/maven-shared-utils-3.4.2.pom (5.9 kB at 190 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-incremental/1.1/maven-shared-incremental-1.1.pom
Progress (1): 4.7 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-incremental/1.1/maven-shared-incremental-1.1.pom (4.7 kB at 153 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-components/19/maven-shared-components-19.pom
Progress (1): 6.4 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-components/19/maven-shared-components-19.pom (6.4 kB at 205 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/23/maven-parent-23.pom
Progress (1): 16/33 kB
Progress (1): 33 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/23/maven-parent-23.pom (33 kB at 1.1 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-java/1.2.0/plexus-java-1.2.0.pom
Progress (1): 4.3 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-java/1.2.0/plexus-java-1.2.0.pom (4.3 kB at 126 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-languages/1.2.0/plexus-languages-1.2.0.pom
Progress (1): 3.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-languages/1.2.0/plexus-languages-1.2.0.pom (3.2 kB at 228 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/15/plexus-15.pom
Progress (1): 16/28 kB
Progress (1): 28 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/15/plexus-15.pom (28 kB at 897 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/junit/junit-bom/5.10.0/junit-bom-5.10.0.pom
Progress (1): 5.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/junit/junit-bom/5.10.0/junit-bom-5.10.0.pom (5.6 kB at 157 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/ow2/asm/asm/9.6/asm-9.6.pom
Progress (1): 2.4 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/ow2/asm/asm/9.6/asm-9.6.pom (2.4 kB at 74 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/thoughtworks/qdox/qdox/2.0.3/qdox-2.0.3.pom
Progress (1): 16/17 kB
Progress (1): 17 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/thoughtworks/qdox/qdox/2.0.3/qdox-2.0.3.pom (17 kB at 522 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-compiler-api/2.15.0/plexus-compiler-api-2.15.0.pom
Progress (1): 1.4 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-compiler-api/2.15.0/plexus-compiler-api-2.15.0.pom (1.4 kB at 43 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-compiler/2.15.0/plexus-compiler-2.15.0.pom
Progress (1): 7.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-compiler/2.15.0/plexus-compiler-2.15.0.pom (7.6 kB at 245 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/4.0.0/plexus-utils-4.0.0.pom
Progress (1): 8.7 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/4.0.0/plexus-utils-4.0.0.pom (8.7 kB at 270 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/13/plexus-13.pom
Progress (1): 16/27 kB
Progress (1): 27 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/13/plexus-13.pom (27 kB at 856 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/junit/junit-bom/5.9.3/junit-bom-5.9.3.pom
Progress (1): 5.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/junit/junit-bom/5.9.3/junit-bom-5.9.3.pom (5.6 kB at 352 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-compiler-manager/2.15.0/plexus-compiler-manager-2.15.0.pom
Progress (1): 1.3 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-compiler-manager/2.15.0/plexus-compiler-manager-2.15.0.pom (1.3 kB at 104 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-xml/3.0.0/plexus-xml-3.0.0.pom
Progress (1): 3.7 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-xml/3.0.0/plexus-xml-3.0.0.pom (3.7 kB at 121 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-compiler-javac/2.15.0/plexus-compiler-javac-2.15.0.pom
Progress (1): 1.3 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-compiler-javac/2.15.0/plexus-compiler-javac-2.15.0.pom (1.3 kB at 40 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-compilers/2.15.0/plexus-compilers-2.15.0.pom
Progress (1): 1.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-compilers/2.15.0/plexus-compilers-2.15.0.pom (1.6 kB at 121 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-utils/3.4.2/maven-shared-utils-3.4.2.jar
Progress (1): 16/151 kB
Progress (1): 33/151 kB
Progress (1): 49/151 kB
Progress (1): 63/151 kB
Progress (1): 79/151 kB
Progress (1): 96/151 kB
Progress (1): 112/151 kB
Progress (1): 129/151 kB
Progress (1): 145/151 kB
Progress (1): 151 kB    
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-utils/3.4.2/maven-shared-utils-3.4.2.jar (151 kB at 10 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-incremental/1.1/maven-shared-incremental-1.1.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-java/1.2.0/plexus-java-1.2.0.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/ow2/asm/asm/9.6/asm-9.6.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-compiler-api/2.15.0/plexus-compiler-api-2.15.0.jar
Downloading from central: https://repo.maven.apache.org/maven2/com/thoughtworks/qdox/qdox/2.0.3/qdox-2.0.3.jar
Progress (1): 16/124 kB
Progress (1): 33/124 kB
Progress (1): 49/124 kB
Progress (1): 66/124 kB
Progress (1): 82/124 kB
Progress (1): 98/124 kB
Progress (1): 115/124 kB
Progress (1): 124 kB    
Progress (2): 124 kB | 16/29 kB
Progress (2): 124 kB | 29 kB   
Progress (3): 124 kB | 29 kB | 16/334 kB
Progress (3): 124 kB | 29 kB | 33/334 kB
Progress (3): 124 kB | 29 kB | 49/334 kB
Progress (3): 124 kB | 29 kB | 65/334 kB
Progress (3): 124 kB | 29 kB | 81/334 kB
Progress (3): 124 kB | 29 kB | 97/334 kB
Progress (3): 124 kB | 29 kB | 114/334 kB
Progress (3): 124 kB | 29 kB | 130/334 kB
Progress (3): 124 kB | 29 kB | 146/334 kB
Progress (3): 124 kB | 29 kB | 163/334 kB
Progress (3): 124 kB | 29 kB | 179/334 kB
Progress (3): 124 kB | 29 kB | 196/334 kB
Progress (3): 124 kB | 29 kB | 212/334 kB
Progress (3): 124 kB | 29 kB | 228/334 kB
Progress (3): 124 kB | 29 kB | 245/334 kB
Progress (3): 124 kB | 29 kB | 261/334 kB
Progress (3): 124 kB | 29 kB | 273/334 kB
Progress (3): 124 kB | 29 kB | 290/334 kB
Progress (3): 124 kB | 29 kB | 306/334 kB
Progress (3): 124 kB | 29 kB | 323/334 kB
Progress (3): 124 kB | 29 kB | 327/334 kB
Progress (3): 124 kB | 29 kB | 334 kB    
                                     
Downloaded from central: https://repo.maven.apache.org/maven2/org/ow2/asm/asm/9.6/asm-9.6.jar (124 kB at 7.7 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-compiler-manager/2.15.0/plexus-compiler-manager-2.15.0.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-compiler-api/2.15.0/plexus-compiler-api-2.15.0.jar (29 kB at 1.8 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-xml/3.0.0/plexus-xml-3.0.0.jar
Downloaded from central: https://repo.maven.apache.org/maven2/com/thoughtworks/qdox/qdox/2.0.3/qdox-2.0.3.jar (334 kB at 10 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-compiler-javac/2.15.0/plexus-compiler-javac-2.15.0.jar
Progress (1): 14 kB
Progress (2): 14 kB | 5.2 kB
Progress (3): 14 kB | 5.2 kB | 16/93 kB
Progress (3): 14 kB | 5.2 kB | 33/93 kB
Progress (3): 14 kB | 5.2 kB | 49/93 kB
Progress (3): 14 kB | 5.2 kB | 66/93 kB
Progress (3): 14 kB | 5.2 kB | 82/93 kB
Progress (3): 14 kB | 5.2 kB | 93 kB   
                                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-incremental/1.1/maven-shared-incremental-1.1.jar (14 kB at 212 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/4.0.0/plexus-utils-4.0.0.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-compiler-manager/2.15.0/plexus-compiler-manager-2.15.0.jar (5.2 kB at 109 kB/s)
Progress (2): 93 kB | 16/26 kB
Progress (2): 93 kB | 26 kB   
                           
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-xml/3.0.0/plexus-xml-3.0.0.jar (93 kB at 1.9 MB/s)
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-compiler-javac/2.15.0/plexus-compiler-javac-2.15.0.jar (26 kB at 459 kB/s)
Progress (1): 16/58 kB
Progress (1): 33/58 kB
Progress (1): 49/58 kB
Progress (1): 58 kB   
Progress (2): 58 kB | 16/192 kB
Progress (2): 58 kB | 33/192 kB
Progress (2): 58 kB | 49/192 kB
Progress (2): 58 kB | 63/192 kB
Progress (2): 58 kB | 79/192 kB
Progress (2): 58 kB | 96/192 kB
Progress (2): 58 kB | 112/192 kB
Progress (2): 58 kB | 128/192 kB
Progress (2): 58 kB | 145/192 kB
Progress (2): 58 kB | 161/192 kB
Progress (2): 58 kB | 177/192 kB
Progress (2): 58 kB | 192 kB    
                            
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-java/1.2.0/plexus-java-1.2.0.jar (58 kB at 899 kB/s)
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-utils/4.0.0/plexus-utils-4.0.0.jar (192 kB at 3.0 MB/s)
[INFO] Recompiling the module because of changed dependency.
[INFO] Compiling 9 source files with javac [debug parameters release 17] to target\classes
[INFO] 
[INFO] --- resources:3.3.1:testResources (default-testResources) @ esocial-novo ---
[INFO] skip non existing resourceDirectory C:\esocial-novo\src\test\resources
[INFO] 
[INFO] --- compiler:3.13.0:testCompile (default-testCompile) @ esocial-novo ---
[INFO] Recompiling the module because of changed dependency.
[INFO] Compiling 1 source file with javac [debug parameters release 17] to target\test-classes
[INFO] 
[INFO] --- surefire:3.5.3:test (default-test) @ esocial-novo ---
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire-api/3.5.3/surefire-api-3.5.3.pom
Progress (1): 3.7 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire-api/3.5.3/surefire-api-3.5.3.pom (3.7 kB at 119 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire-logger-api/3.5.3/surefire-logger-api-3.5.3.pom
Progress (1): 3.5 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire-logger-api/3.5.3/surefire-logger-api-3.5.3.pom (3.5 kB at 218 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire-shared-utils/3.5.3/surefire-shared-utils-3.5.3.pom
Progress (1): 4.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire-shared-utils/3.5.3/surefire-shared-utils-3.5.3.pom (4.0 kB at 124 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire-extensions-api/3.5.3/surefire-extensions-api-3.5.3.pom
Progress (1): 3.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire-extensions-api/3.5.3/surefire-extensions-api-3.5.3.pom (3.6 kB at 99 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/maven-surefire-common/3.5.3/maven-surefire-common-3.5.3.pom
Progress (1): 7.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/maven-surefire-common/3.5.3/maven-surefire-common-3.5.3.pom (7.8 kB at 490 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire-booter/3.5.3/surefire-booter-3.5.3.pom
Progress (1): 5.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire-booter/3.5.3/surefire-booter-3.5.3.pom (5.0 kB at 161 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire-extensions-spi/3.5.3/surefire-extensions-spi-3.5.3.pom
Progress (1): 1.7 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire-extensions-spi/3.5.3/surefire-extensions-spi-3.5.3.pom (1.7 kB at 55 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/resolver/maven-resolver-util/1.4.1/maven-resolver-util-1.4.1.pom
Progress (1): 2.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/resolver/maven-resolver-util/1.4.1/maven-resolver-util-1.4.1.pom (2.8 kB at 90 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/resolver/maven-resolver/1.4.1/maven-resolver-1.4.1.pom
Progress (1): 16/18 kB
Progress (1): 18 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/resolver/maven-resolver/1.4.1/maven-resolver-1.4.1.pom (18 kB at 586 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/33/maven-parent-33.pom
Progress (1): 16/44 kB
Progress (1): 32/44 kB
Progress (1): 44 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/33/maven-parent-33.pom (44 kB at 2.9 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/resolver/maven-resolver-api/1.4.1/maven-resolver-api-1.4.1.pom
Progress (1): 2.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/resolver/maven-resolver-api/1.4.1/maven-resolver-api-1.4.1.pom (2.6 kB at 85 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-common-artifact-filters/3.4.0/maven-common-artifact-filters-3.4.0.pom
Progress (1): 5.4 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-common-artifact-filters/3.4.0/maven-common-artifact-filters-3.4.0.pom (5.4 kB at 336 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-components/42/maven-shared-components-42.pom
Progress (1): 3.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-components/42/maven-shared-components-42.pom (3.8 kB at 122 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-java/1.4.0/plexus-java-1.4.0.pom
Progress (1): 4.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-java/1.4.0/plexus-java-1.4.0.pom (4.1 kB at 128 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-languages/1.4.0/plexus-languages-1.4.0.pom
Progress (1): 3.9 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-languages/1.4.0/plexus-languages-1.4.0.pom (3.9 kB at 124 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/20/plexus-20.pom
Progress (1): 16/29 kB
Progress (1): 29 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/20/plexus-20.pom (29 kB at 941 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/thoughtworks/qdox/qdox/2.2.0/qdox-2.2.0.pom
Progress (1): 16/18 kB
Progress (1): 18 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/thoughtworks/qdox/qdox/2.2.0/qdox-2.2.0.pom (18 kB at 552 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire-api/3.5.3/surefire-api-3.5.3.jar
Progress (1): 16/172 kB
Progress (1): 32/172 kB
Progress (1): 49/172 kB
Progress (1): 65/172 kB
Progress (1): 81/172 kB
Progress (1): 98/172 kB
Progress (1): 114/172 kB
Progress (1): 130/172 kB
Progress (1): 147/172 kB
Progress (1): 163/172 kB
Progress (1): 172 kB    
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire-api/3.5.3/surefire-api-3.5.3.jar (172 kB at 5.5 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire-logger-api/3.5.3/surefire-logger-api-3.5.3.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire-shared-utils/3.5.3/surefire-shared-utils-3.5.3.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire-extensions-api/3.5.3/surefire-extensions-api-3.5.3.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/maven-surefire-common/3.5.3/maven-surefire-common-3.5.3.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire-booter/3.5.3/surefire-booter-3.5.3.jar
Progress (1): 14 kB
Progress (2): 14 kB | 16/312 kB
Progress (3): 14 kB | 16/312 kB | 16/26 kB
Progress (3): 14 kB | 16/312 kB | 26 kB   
Progress (3): 14 kB | 33/312 kB | 26 kB
Progress (3): 14 kB | 49/312 kB | 26 kB
Progress (3): 14 kB | 66/312 kB | 26 kB
Progress (3): 14 kB | 82/312 kB | 26 kB
Progress (3): 14 kB | 98/312 kB | 26 kB
Progress (3): 14 kB | 115/312 kB | 26 kB
Progress (3): 14 kB | 131/312 kB | 26 kB
Progress (3): 14 kB | 147/312 kB | 26 kB
Progress (3): 14 kB | 164/312 kB | 26 kB
Progress (3): 14 kB | 180/312 kB | 26 kB
Progress (3): 14 kB | 197/312 kB | 26 kB
Progress (3): 14 kB | 213/312 kB | 26 kB
Progress (3): 14 kB | 229/312 kB | 26 kB
Progress (3): 14 kB | 246/312 kB | 26 kB
Progress (3): 14 kB | 262/312 kB | 26 kB
Progress (3): 14 kB | 279/312 kB | 26 kB
                                        
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire-logger-api/3.5.3/surefire-logger-api-3.5.3.jar (14 kB at 425 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire-extensions-spi/3.5.3/surefire-extensions-spi-3.5.3.jar
Progress (2): 295/312 kB | 26 kB
Progress (2): 311/312 kB | 26 kB
Progress (2): 312 kB | 26 kB    
Progress (3): 312 kB | 26 kB | 0/2.9 MB
Progress (3): 312 kB | 26 kB | 0/2.9 MB
Progress (3): 312 kB | 26 kB | 0/2.9 MB
Progress (3): 312 kB | 26 kB | 0.1/2.9 MB
Progress (3): 312 kB | 26 kB | 0.1/2.9 MB
Progress (3): 312 kB | 26 kB | 0.1/2.9 MB
Progress (3): 312 kB | 26 kB | 0.1/2.9 MB
Progress (3): 312 kB | 26 kB | 0.1/2.9 MB
Progress (3): 312 kB | 26 kB | 0.1/2.9 MB
Progress (3): 312 kB | 26 kB | 0.2/2.9 MB
Progress (3): 312 kB | 26 kB | 0.2/2.9 MB
                                         
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire-extensions-api/3.5.3/surefire-extensions-api-3.5.3.jar (26 kB at 552 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/resolver/maven-resolver-util/1.4.1/maven-resolver-util-1.4.1.jar
Progress (2): 312 kB | 0.2/2.9 MB
Progress (2): 312 kB | 0.2/2.9 MB
Progress (2): 312 kB | 0.2/2.9 MB
Progress (2): 312 kB | 0.2/2.9 MB
Progress (2): 312 kB | 0.3/2.9 MB
Progress (2): 312 kB | 0.3/2.9 MB
Progress (2): 312 kB | 0.3/2.9 MB
Progress (2): 312 kB | 0.3/2.9 MB
Progress (2): 312 kB | 0.3/2.9 MB
Progress (2): 312 kB | 0.3/2.9 MB
Progress (2): 312 kB | 0.4/2.9 MB
Progress (2): 312 kB | 0.4/2.9 MB
Progress (2): 312 kB | 0.4/2.9 MB
Progress (2): 312 kB | 0.4/2.9 MB
Progress (2): 312 kB | 0.4/2.9 MB
Progress (2): 312 kB | 0.4/2.9 MB
Progress (2): 312 kB | 0.5/2.9 MB
Progress (2): 312 kB | 0.5/2.9 MB
Progress (2): 312 kB | 0.5/2.9 MB
Progress (2): 312 kB | 0.5/2.9 MB
                                 
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/maven-surefire-common/3.5.3/maven-surefire-common-3.5.3.jar (312 kB at 6.6 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/resolver/maven-resolver-api/1.4.1/maven-resolver-api-1.4.1.jar
Progress (1): 0.5/2.9 MB
Progress (1): 0.5/2.9 MB
Progress (1): 0.6/2.9 MB
Progress (1): 0.6/2.9 MB
Progress (2): 0.6/2.9 MB | 16/118 kB
Progress (2): 0.6/2.9 MB | 16/118 kB
Progress (2): 0.6/2.9 MB | 33/118 kB
Progress (2): 0.6/2.9 MB | 33/118 kB
Progress (2): 0.6/2.9 MB | 49/118 kB
Progress (2): 0.6/2.9 MB | 49/118 kB
Progress (2): 0.6/2.9 MB | 49/118 kB
Progress (2): 0.6/2.9 MB | 66/118 kB
Progress (2): 0.6/2.9 MB | 82/118 kB
Progress (2): 0.7/2.9 MB | 82/118 kB
Progress (2): 0.7/2.9 MB | 98/118 kB
Progress (2): 0.7/2.9 MB | 98/118 kB
Progress (2): 0.7/2.9 MB | 115/118 kB
Progress (2): 0.7/2.9 MB | 115/118 kB
Progress (2): 0.7/2.9 MB | 118 kB    
Progress (2): 0.7/2.9 MB | 118 kB
Progress (2): 0.7/2.9 MB | 118 kB
Progress (2): 0.7/2.9 MB | 118 kB
Progress (2): 0.7/2.9 MB | 118 kB
Progress (2): 0.8/2.9 MB | 118 kB
Progress (2): 0.8/2.9 MB | 118 kB
Progress (2): 0.8/2.9 MB | 118 kB
Progress (3): 0.8/2.9 MB | 118 kB | 8.2 kB
Progress (3): 0.8/2.9 MB | 118 kB | 8.2 kB
Progress (3): 0.8/2.9 MB | 118 kB | 8.2 kB
Progress (3): 0.8/2.9 MB | 118 kB | 8.2 kB
Progress (3): 0.9/2.9 MB | 118 kB | 8.2 kB
Progress (3): 0.9/2.9 MB | 118 kB | 8.2 kB
Progress (3): 0.9/2.9 MB | 118 kB | 8.2 kB
Progress (3): 0.9/2.9 MB | 118 kB | 8.2 kB
Progress (3): 0.9/2.9 MB | 118 kB | 8.2 kB
Progress (3): 0.9/2.9 MB | 118 kB | 8.2 kB
Progress (3): 1.0/2.9 MB | 118 kB | 8.2 kB
Progress (3): 1.0/2.9 MB | 118 kB | 8.2 kB
Progress (3): 1.0/2.9 MB | 118 kB | 8.2 kB
Progress (3): 1.0/2.9 MB | 118 kB | 8.2 kB
Progress (3): 1.0/2.9 MB | 118 kB | 8.2 kB
Progress (3): 1.0/2.9 MB | 118 kB | 8.2 kB
Progress (3): 1.1/2.9 MB | 118 kB | 8.2 kB
Progress (3): 1.1/2.9 MB | 118 kB | 8.2 kB
Progress (3): 1.1/2.9 MB | 118 kB | 8.2 kB
Progress (3): 1.1/2.9 MB | 118 kB | 8.2 kB
Progress (3): 1.1/2.9 MB | 118 kB | 8.2 kB
Progress (3): 1.1/2.9 MB | 118 kB | 8.2 kB
Progress (3): 1.2/2.9 MB | 118 kB | 8.2 kB
Progress (3): 1.2/2.9 MB | 118 kB | 8.2 kB
Progress (3): 1.2/2.9 MB | 118 kB | 8.2 kB
Progress (3): 1.2/2.9 MB | 118 kB | 8.2 kB
Progress (4): 1.2/2.9 MB | 118 kB | 8.2 kB | 16/168 kB
Progress (4): 1.2/2.9 MB | 118 kB | 8.2 kB | 16/168 kB
Progress (4): 1.2/2.9 MB | 118 kB | 8.2 kB | 32/168 kB
Progress (4): 1.2/2.9 MB | 118 kB | 8.2 kB | 32/168 kB
Progress (4): 1.2/2.9 MB | 118 kB | 8.2 kB | 49/168 kB
Progress (4): 1.3/2.9 MB | 118 kB | 8.2 kB | 49/168 kB
                                                      
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire-booter/3.5.3/surefire-booter-3.5.3.jar (118 kB at 2.5 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-common-artifact-filters/3.4.0/maven-common-artifact-filters-3.4.0.jar
Progress (3): 1.3/2.9 MB | 8.2 kB | 65/168 kB
Progress (3): 1.3/2.9 MB | 8.2 kB | 65/168 kB
Progress (3): 1.3/2.9 MB | 8.2 kB | 81/168 kB
Progress (3): 1.3/2.9 MB | 8.2 kB | 81/168 kB
                                             
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire-extensions-spi/3.5.3/surefire-extensions-spi-3.5.3.jar (8.2 kB at 174 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-java/1.4.0/plexus-java-1.4.0.jar
Progress (2): 1.3/2.9 MB | 98/168 kB
Progress (2): 1.3/2.9 MB | 98/168 kB
Progress (2): 1.3/2.9 MB | 114/168 kB
Progress (2): 1.3/2.9 MB | 114/168 kB
Progress (2): 1.3/2.9 MB | 130/168 kB
Progress (2): 1.3/2.9 MB | 130/168 kB
Progress (2): 1.3/2.9 MB | 147/168 kB
Progress (2): 1.3/2.9 MB | 147/168 kB
Progress (2): 1.3/2.9 MB | 163/168 kB
Progress (2): 1.3/2.9 MB | 168 kB    
Progress (2): 1.4/2.9 MB | 168 kB
Progress (2): 1.4/2.9 MB | 168 kB
Progress (2): 1.4/2.9 MB | 168 kB
Progress (2): 1.4/2.9 MB | 168 kB
Progress (2): 1.4/2.9 MB | 168 kB
Progress (2): 1.4/2.9 MB | 168 kB
Progress (2): 1.5/2.9 MB | 168 kB
Progress (2): 1.5/2.9 MB | 168 kB
Progress (2): 1.5/2.9 MB | 168 kB
Progress (2): 1.5/2.9 MB | 168 kB
Progress (2): 1.5/2.9 MB | 168 kB
Progress (2): 1.5/2.9 MB | 168 kB
Progress (2): 1.6/2.9 MB | 168 kB
Progress (2): 1.6/2.9 MB | 168 kB
Progress (2): 1.6/2.9 MB | 168 kB
Progress (2): 1.6/2.9 MB | 168 kB
Progress (2): 1.6/2.9 MB | 168 kB
Progress (2): 1.6/2.9 MB | 168 kB
Progress (3): 1.6/2.9 MB | 168 kB | 16/149 kB
Progress (3): 1.7/2.9 MB | 168 kB | 16/149 kB
Progress (3): 1.7/2.9 MB | 168 kB | 32/149 kB
Progress (3): 1.7/2.9 MB | 168 kB | 32/149 kB
Progress (3): 1.7/2.9 MB | 168 kB | 49/149 kB
Progress (3): 1.7/2.9 MB | 168 kB | 49/149 kB
Progress (3): 1.7/2.9 MB | 168 kB | 65/149 kB
Progress (3): 1.7/2.9 MB | 168 kB | 65/149 kB
Progress (3): 1.7/2.9 MB | 168 kB | 81/149 kB
Progress (3): 1.7/2.9 MB | 168 kB | 98/149 kB
Progress (3): 1.7/2.9 MB | 168 kB | 98/149 kB
Progress (3): 1.7/2.9 MB | 168 kB | 114/149 kB
Progress (3): 1.7/2.9 MB | 168 kB | 114/149 kB
Progress (3): 1.8/2.9 MB | 168 kB | 114/149 kB
Progress (3): 1.8/2.9 MB | 168 kB | 130/149 kB
Progress (3): 1.8/2.9 MB | 168 kB | 130/149 kB
Progress (3): 1.8/2.9 MB | 168 kB | 147/149 kB
Progress (3): 1.8/2.9 MB | 168 kB | 149 kB    
Progress (3): 1.8/2.9 MB | 168 kB | 149 kB
                                          
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/resolver/maven-resolver-util/1.4.1/maven-resolver-util-1.4.1.jar (168 kB at 2.7 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/thoughtworks/qdox/qdox/2.2.0/qdox-2.2.0.jar
Progress (2): 1.8/2.9 MB | 149 kB
Progress (2): 1.8/2.9 MB | 149 kB
Progress (2): 1.8/2.9 MB | 149 kB
Progress (2): 1.9/2.9 MB | 149 kB
Progress (2): 1.9/2.9 MB | 149 kB
Progress (2): 1.9/2.9 MB | 149 kB
Progress (2): 1.9/2.9 MB | 149 kB
Progress (2): 1.9/2.9 MB | 149 kB
Progress (2): 1.9/2.9 MB | 149 kB
Progress (2): 2.0/2.9 MB | 149 kB
Progress (2): 2.0/2.9 MB | 149 kB
Progress (2): 2.0/2.9 MB | 149 kB
Progress (2): 2.0/2.9 MB | 149 kB
Progress (2): 2.0/2.9 MB | 149 kB
Progress (2): 2.0/2.9 MB | 149 kB
Progress (2): 2.0/2.9 MB | 149 kB
Progress (2): 2.1/2.9 MB | 149 kB
Progress (2): 2.1/2.9 MB | 149 kB
Progress (2): 2.1/2.9 MB | 149 kB
Progress (2): 2.1/2.9 MB | 149 kB
Progress (3): 2.1/2.9 MB | 149 kB | 16/58 kB
Progress (3): 2.1/2.9 MB | 149 kB | 16/58 kB
Progress (4): 2.1/2.9 MB | 149 kB | 16/58 kB | 16/57 kB
Progress (4): 2.1/2.9 MB | 149 kB | 16/58 kB | 16/57 kB
Progress (4): 2.1/2.9 MB | 149 kB | 33/58 kB | 16/57 kB
Progress (4): 2.1/2.9 MB | 149 kB | 33/58 kB | 33/57 kB
Progress (4): 2.1/2.9 MB | 149 kB | 49/58 kB | 33/57 kB
Progress (4): 2.2/2.9 MB | 149 kB | 49/58 kB | 33/57 kB
Progress (4): 2.2/2.9 MB | 149 kB | 49/58 kB | 49/57 kB
Progress (4): 2.2/2.9 MB | 149 kB | 58 kB | 49/57 kB   
Progress (4): 2.2/2.9 MB | 149 kB | 58 kB | 57 kB   
Progress (4): 2.2/2.9 MB | 149 kB | 58 kB | 57 kB
Progress (4): 2.2/2.9 MB | 149 kB | 58 kB | 57 kB
Progress (4): 2.2/2.9 MB | 149 kB | 58 kB | 57 kB
Progress (4): 2.2/2.9 MB | 149 kB | 58 kB | 57 kB
                                                 
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/resolver/maven-resolver-api/1.4.1/maven-resolver-api-1.4.1.jar (149 kB at 1.9 MB/s)
Progress (3): 2.2/2.9 MB | 58 kB | 57 kB
Progress (3): 2.3/2.9 MB | 58 kB | 57 kB
Progress (3): 2.3/2.9 MB | 58 kB | 57 kB
Progress (3): 2.3/2.9 MB | 58 kB | 57 kB
Progress (3): 2.3/2.9 MB | 58 kB | 57 kB
Progress (3): 2.3/2.9 MB | 58 kB | 57 kB
Progress (3): 2.3/2.9 MB | 58 kB | 57 kB
Progress (3): 2.4/2.9 MB | 58 kB | 57 kB
Progress (3): 2.4/2.9 MB | 58 kB | 57 kB
Progress (3): 2.4/2.9 MB | 58 kB | 57 kB
Progress (3): 2.4/2.9 MB | 58 kB | 57 kB
Progress (4): 2.4/2.9 MB | 58 kB | 57 kB | 16/353 kB
Progress (4): 2.4/2.9 MB | 58 kB | 57 kB | 16/353 kB
Progress (4): 2.4/2.9 MB | 58 kB | 57 kB | 33/353 kB
Progress (4): 2.4/2.9 MB | 58 kB | 57 kB | 33/353 kB
Progress (4): 2.4/2.9 MB | 58 kB | 57 kB | 49/353 kB
Progress (4): 2.5/2.9 MB | 58 kB | 57 kB | 49/353 kB
Progress (4): 2.5/2.9 MB | 58 kB | 57 kB | 49/353 kB
Progress (4): 2.5/2.9 MB | 58 kB | 57 kB | 65/353 kB
Progress (4): 2.5/2.9 MB | 58 kB | 57 kB | 65/353 kB
Progress (4): 2.5/2.9 MB | 58 kB | 57 kB | 81/353 kB
Progress (4): 2.5/2.9 MB | 58 kB | 57 kB | 81/353 kB
Progress (4): 2.5/2.9 MB | 58 kB | 57 kB | 98/353 kB
Progress (4): 2.5/2.9 MB | 58 kB | 57 kB | 98/353 kB
Progress (4): 2.5/2.9 MB | 58 kB | 57 kB | 114/353 kB
Progress (4): 2.5/2.9 MB | 58 kB | 57 kB | 114/353 kB
Progress (4): 2.5/2.9 MB | 58 kB | 57 kB | 130/353 kB
Progress (4): 2.6/2.9 MB | 58 kB | 57 kB | 130/353 kB
Progress (4): 2.6/2.9 MB | 58 kB | 57 kB | 130/353 kB
Progress (4): 2.6/2.9 MB | 58 kB | 57 kB | 147/353 kB
Progress (4): 2.6/2.9 MB | 58 kB | 57 kB | 147/353 kB
Progress (4): 2.6/2.9 MB | 58 kB | 57 kB | 163/353 kB
Progress (4): 2.6/2.9 MB | 58 kB | 57 kB | 163/353 kB
Progress (4): 2.6/2.9 MB | 58 kB | 57 kB | 179/353 kB
Progress (4): 2.6/2.9 MB | 58 kB | 57 kB | 179/353 kB
Progress (4): 2.6/2.9 MB | 58 kB | 57 kB | 196/353 kB
Progress (4): 2.6/2.9 MB | 58 kB | 57 kB | 196/353 kB
Progress (4): 2.6/2.9 MB | 58 kB | 57 kB | 212/353 kB
Progress (4): 2.6/2.9 MB | 58 kB | 57 kB | 212/353 kB
Progress (4): 2.6/2.9 MB | 58 kB | 57 kB | 229/353 kB
Progress (4): 2.7/2.9 MB | 58 kB | 57 kB | 229/353 kB
Progress (4): 2.7/2.9 MB | 58 kB | 57 kB | 245/353 kB
                                                     
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-common-artifact-filters/3.4.0/maven-common-artifact-filters-3.4.0.jar (58 kB at 749 kB/s)
Progress (3): 2.7/2.9 MB | 57 kB | 261/353 kB
Progress (3): 2.7/2.9 MB | 57 kB | 261/353 kB
Progress (3): 2.7/2.9 MB | 57 kB | 261/353 kB
Progress (3): 2.7/2.9 MB | 57 kB | 278/353 kB
Progress (3): 2.7/2.9 MB | 57 kB | 278/353 kB
Progress (3): 2.7/2.9 MB | 57 kB | 294/353 kB
                                             
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-java/1.4.0/plexus-java-1.4.0.jar (57 kB at 735 kB/s)
Progress (2): 2.7/2.9 MB | 294/353 kB
Progress (2): 2.7/2.9 MB | 311/353 kB
Progress (2): 2.7/2.9 MB | 311/353 kB
Progress (2): 2.7/2.9 MB | 327/353 kB
Progress (2): 2.7/2.9 MB | 343/353 kB
Progress (2): 2.8/2.9 MB | 343/353 kB
Progress (2): 2.8/2.9 MB | 347/353 kB
Progress (2): 2.8/2.9 MB | 353 kB    
Progress (2): 2.8/2.9 MB | 353 kB
Progress (2): 2.8/2.9 MB | 353 kB
Progress (2): 2.8/2.9 MB | 353 kB
Progress (2): 2.8/2.9 MB | 353 kB
Progress (2): 2.8/2.9 MB | 353 kB
Progress (2): 2.9 MB | 353 kB    
                             
Downloaded from central: https://repo.maven.apache.org/maven2/com/thoughtworks/qdox/qdox/2.2.0/qdox-2.2.0.jar (353 kB at 3.6 MB/s)
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/surefire/surefire-shared-utils/3.5.3/surefire-shared-utils-3.5.3.jar (2.9 MB at 29 MB/s)
[INFO] Tests are skipped.
[INFO] 
[INFO] --- jar:3.4.2:jar (default-jar) @ esocial-novo ---
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/file-management/3.1.0/file-management-3.1.0.pom
Progress (1): 4.5 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/file-management/3.1.0/file-management-3.1.0.pom (4.5 kB at 145 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-components/36/maven-shared-components-36.pom
Progress (1): 4.9 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-components/36/maven-shared-components-36.pom (4.9 kB at 288 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/36/maven-parent-36.pom
Progress (1): 16/45 kB
Progress (1): 33/45 kB
Progress (1): 37/45 kB
Progress (1): 45 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/36/maven-parent-36.pom (45 kB at 3.0 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/apache/26/apache-26.pom
Progress (1): 16/21 kB
Progress (1): 21 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/apache/26/apache-26.pom (21 kB at 1.4 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/commons-io/commons-io/2.16.1/commons-io-2.16.1.pom
Progress (1): 16/20 kB
Progress (1): 20 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/commons-io/commons-io/2.16.1/commons-io-2.16.1.pom (20 kB at 727 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-parent/69/commons-parent-69.pom
Progress (1): 16/77 kB
Progress (1): 33/77 kB
Progress (1): 49/77 kB
Progress (1): 66/77 kB
Progress (1): 77 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-parent/69/commons-parent-69.pom (77 kB at 2.4 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-archiver/3.6.2/maven-archiver-3.6.2.pom
Progress (1): 4.4 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-archiver/3.6.2/maven-archiver-3.6.2.pom (4.4 kB at 141 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-components/41/maven-shared-components-41.pom
Progress (1): 3.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-components/41/maven-shared-components-41.pom (3.2 kB at 96 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-archiver/4.9.2/plexus-archiver-4.9.2.pom
Progress (1): 6.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-archiver/4.9.2/plexus-archiver-4.9.2.pom (6.0 kB at 352 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-io/3.4.2/plexus-io-3.4.2.pom
Progress (1): 3.9 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-io/3.4.2/plexus-io-3.4.2.pom (3.9 kB at 133 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/16/plexus-16.pom
Progress (1): 16/28 kB
Progress (1): 28 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus/16/plexus-16.pom (28 kB at 1.7 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/junit/junit-bom/5.10.1/junit-bom-5.10.1.pom
Progress (1): 5.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/junit/junit-bom/5.10.1/junit-bom-5.10.1.pom (5.6 kB at 177 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-compress/1.26.1/commons-compress-1.26.1.pom
Progress (1): 16/22 kB
Progress (1): 22 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-compress/1.26.1/commons-compress-1.26.1.pom (22 kB at 699 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-parent/66/commons-parent-66.pom
Progress (1): 16/77 kB
Progress (1): 33/77 kB
Progress (1): 49/77 kB
Progress (1): 66/77 kB
Progress (1): 77 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-parent/66/commons-parent-66.pom (77 kB at 5.1 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/commons-codec/commons-codec/1.16.1/commons-codec-1.16.1.pom
Progress (1): 16 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/commons-codec/commons-codec/1.16.1/commons-codec-1.16.1.pom (16 kB at 980 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-lang3/3.14.0/commons-lang3-3.14.0.pom
Progress (1): 16/31 kB
Progress (1): 31 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-lang3/3.14.0/commons-lang3-3.14.0.pom (31 kB at 997 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-parent/64/commons-parent-64.pom
Progress (1): 16/78 kB
Progress (1): 32/78 kB
Progress (1): 49/78 kB
Progress (1): 65/78 kB
Progress (1): 78 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-parent/64/commons-parent-64.pom (78 kB at 2.4 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/apache/30/apache-30.pom
Progress (1): 16/23 kB
Progress (1): 23 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/apache/30/apache-30.pom (23 kB at 750 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/iq80/snappy/snappy/0.4/snappy-0.4.pom
Progress (1): 15 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/iq80/snappy/snappy/0.4/snappy-0.4.pom (15 kB at 469 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/tukaani/xz/1.9/xz-1.9.pom
Progress (1): 2.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/tukaani/xz/1.9/xz-1.9.pom (2.0 kB at 66 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/github/luben/zstd-jni/1.5.5-11/zstd-jni-1.5.5-11.pom
Progress (1): 2.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/com/github/luben/zstd-jni/1.5.5-11/zstd-jni-1.5.5-11.pom (2.0 kB at 63 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-interpolation/1.27/plexus-interpolation-1.27.pom
Progress (1): 3.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-interpolation/1.27/plexus-interpolation-1.27.pom (3.0 kB at 97 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/file-management/3.1.0/file-management-3.1.0.jar
Progress (1): 16/36 kB
Progress (1): 33/36 kB
Progress (1): 36 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/file-management/3.1.0/file-management-3.1.0.jar (36 kB at 1.2 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-io/3.4.2/plexus-io-3.4.2.jar
Progress (1): 16/509 kB
Progress (1): 33/509 kB
Progress (1): 49/509 kB
Progress (1): 63/509 kB
Progress (1): 80/509 kB
Progress (1): 96/509 kB
Progress (1): 113/509 kB
Progress (1): 129/509 kB
Progress (1): 145/509 kB
Progress (1): 162/509 kB
Progress (1): 166/509 kB
Progress (1): 182/509 kB
Progress (1): 194/509 kB
Progress (1): 211/509 kB
Progress (1): 227/509 kB
Progress (1): 244/509 kB
Progress (1): 260/509 kB
Progress (1): 276/509 kB
Progress (1): 293/509 kB
Progress (1): 309/509 kB
Progress (1): 326/509 kB
Progress (1): 342/509 kB
Progress (1): 358/509 kB
Progress (1): 375/509 kB
Progress (1): 391/509 kB
Progress (1): 407/509 kB
Progress (1): 424/509 kB
Progress (1): 440/509 kB
Progress (1): 457/509 kB
Progress (2): 457/509 kB | 16/79 kB
Progress (2): 473/509 kB | 16/79 kB
Progress (2): 473/509 kB | 32/79 kB
Progress (2): 489/509 kB | 32/79 kB
Progress (2): 489/509 kB | 49/79 kB
Progress (2): 506/509 kB | 49/79 kB
Progress (2): 509 kB | 49/79 kB    
Progress (2): 509 kB | 65/79 kB
Progress (2): 509 kB | 79 kB   
                            
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-archiver/3.6.2/maven-archiver-3.6.2.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-interpolation/1.27/plexus-interpolation-1.27.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-archiver/4.9.2/plexus-archiver-4.9.2.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-io/3.4.2/plexus-io-3.4.2.jar (79 kB at 197 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-compress/1.26.1/commons-compress-1.26.1.jar
Progress (2): 509 kB | 16/27 kB
Progress (2): 509 kB | 27 kB   
Progress (3): 509 kB | 27 kB | 16/225 kB
Progress (4): 509 kB | 27 kB | 16/225 kB | 16/86 kB
Progress (4): 509 kB | 27 kB | 32/225 kB | 16/86 kB
Progress (4): 509 kB | 27 kB | 32/225 kB | 33/86 kB
Progress (4): 509 kB | 27 kB | 49/225 kB | 33/86 kB
Progress (4): 509 kB | 27 kB | 49/225 kB | 49/86 kB
Progress (4): 509 kB | 27 kB | 65/225 kB | 49/86 kB
Progress (4): 509 kB | 27 kB | 65/225 kB | 66/86 kB
Progress (4): 509 kB | 27 kB | 65/225 kB | 82/86 kB
Progress (4): 509 kB | 27 kB | 81/225 kB | 82/86 kB
Progress (4): 509 kB | 27 kB | 81/225 kB | 86 kB   
Progress (4): 509 kB | 27 kB | 98/225 kB | 86 kB
Progress (4): 509 kB | 27 kB | 114/225 kB | 86 kB
Progress (4): 509 kB | 27 kB | 130/225 kB | 86 kB
Progress (4): 509 kB | 27 kB | 147/225 kB | 86 kB
Progress (4): 509 kB | 27 kB | 163/225 kB | 86 kB
Progress (4): 509 kB | 27 kB | 180/225 kB | 86 kB
Progress (4): 509 kB | 27 kB | 196/225 kB | 86 kB
Progress (4): 509 kB | 27 kB | 212/225 kB | 86 kB
Progress (4): 509 kB | 27 kB | 225 kB | 86 kB    
                                             
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-archiver/3.6.2/maven-archiver-3.6.2.jar (27 kB at 63 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-lang3/3.14.0/commons-lang3-3.14.0.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-interpolation/1.27/plexus-interpolation-1.27.jar (86 kB at 202 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/commons-codec/commons-codec/1.16.1/commons-codec-1.16.1.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/plexus/plexus-archiver/4.9.2/plexus-archiver-4.9.2.jar (225 kB at 529 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/iq80/snappy/snappy/0.4/snappy-0.4.jar
Progress (2): 509 kB | 0/1.1 MB
Progress (2): 509 kB | 0/1.1 MB
Progress (2): 509 kB | 0/1.1 MB
Progress (2): 509 kB | 0.1/1.1 MB
Progress (2): 509 kB | 0.1/1.1 MB
Progress (2): 509 kB | 0.1/1.1 MB
Progress (2): 509 kB | 0.1/1.1 MB
Progress (2): 509 kB | 0.1/1.1 MB
Progress (2): 509 kB | 0.1/1.1 MB
Progress (2): 509 kB | 0.2/1.1 MB
                                 
Downloaded from central: https://repo.maven.apache.org/maven2/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar (509 kB at 1.2 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/tukaani/xz/1.9/xz-1.9.jar
Progress (1): 0.2/1.1 MB
Progress (1): 0.2/1.1 MB
Progress (1): 0.2/1.1 MB
Progress (1): 0.2/1.1 MB
Progress (1): 0.2/1.1 MB
Progress (1): 0.3/1.1 MB
Progress (1): 0.3/1.1 MB
Progress (1): 0.3/1.1 MB
Progress (1): 0.3/1.1 MB
Progress (1): 0.3/1.1 MB
Progress (1): 0.3/1.1 MB
Progress (1): 0.4/1.1 MB
Progress (1): 0.4/1.1 MB
Progress (1): 0.4/1.1 MB
Progress (1): 0.4/1.1 MB
Progress (1): 0.4/1.1 MB
Progress (1): 0.4/1.1 MB
Progress (1): 0.5/1.1 MB
Progress (1): 0.5/1.1 MB
Progress (2): 0.5/1.1 MB | 16/365 kB
Progress (2): 0.5/1.1 MB | 16/365 kB
Progress (2): 0.5/1.1 MB | 33/365 kB
Progress (3): 0.5/1.1 MB | 33/365 kB | 16/58 kB
Progress (3): 0.5/1.1 MB | 33/365 kB | 16/58 kB
Progress (3): 0.5/1.1 MB | 33/365 kB | 16/58 kB
Progress (3): 0.5/1.1 MB | 49/365 kB | 16/58 kB
Progress (3): 0.5/1.1 MB | 49/365 kB | 32/58 kB
Progress (3): 0.5/1.1 MB | 49/365 kB | 49/58 kB
Progress (3): 0.5/1.1 MB | 64/365 kB | 49/58 kB
Progress (3): 0.5/1.1 MB | 64/365 kB | 49/58 kB
Progress (3): 0.5/1.1 MB | 64/365 kB | 58 kB   
Progress (3): 0.5/1.1 MB | 81/365 kB | 58 kB
Progress (3): 0.6/1.1 MB | 81/365 kB | 58 kB
Progress (3): 0.6/1.1 MB | 97/365 kB | 58 kB
Progress (3): 0.6/1.1 MB | 113/365 kB | 58 kB
Progress (3): 0.6/1.1 MB | 113/365 kB | 58 kB
Progress (3): 0.6/1.1 MB | 130/365 kB | 58 kB
Progress (3): 0.6/1.1 MB | 146/365 kB | 58 kB
Progress (3): 0.6/1.1 MB | 146/365 kB | 58 kB
Progress (3): 0.6/1.1 MB | 163/365 kB | 58 kB
Progress (3): 0.6/1.1 MB | 167/365 kB | 58 kB
Progress (3): 0.6/1.1 MB | 167/365 kB | 58 kB
Progress (3): 0.6/1.1 MB | 183/365 kB | 58 kB
Progress (3): 0.6/1.1 MB | 183/365 kB | 58 kB
Progress (3): 0.6/1.1 MB | 195/365 kB | 58 kB
Progress (3): 0.6/1.1 MB | 212/365 kB | 58 kB
Progress (4): 0.6/1.1 MB | 212/365 kB | 58 kB | 16/658 kB
Progress (4): 0.6/1.1 MB | 228/365 kB | 58 kB | 16/658 kB
Progress (4): 0.6/1.1 MB | 244/365 kB | 58 kB | 16/658 kB
Progress (4): 0.6/1.1 MB | 244/365 kB | 58 kB | 16/658 kB
Progress (4): 0.6/1.1 MB | 244/365 kB | 58 kB | 33/658 kB
Progress (4): 0.6/1.1 MB | 261/365 kB | 58 kB | 33/658 kB
Progress (4): 0.6/1.1 MB | 261/365 kB | 58 kB | 49/658 kB
Progress (4): 0.6/1.1 MB | 265/365 kB | 58 kB | 49/658 kB
Progress (4): 0.7/1.1 MB | 265/365 kB | 58 kB | 49/658 kB
Progress (4): 0.7/1.1 MB | 281/365 kB | 58 kB | 49/658 kB
Progress (4): 0.7/1.1 MB | 281/365 kB | 58 kB | 66/658 kB
Progress (4): 0.7/1.1 MB | 281/365 kB | 58 kB | 66/658 kB
Progress (4): 0.7/1.1 MB | 298/365 kB | 58 kB | 66/658 kB
Progress (4): 0.7/1.1 MB | 298/365 kB | 58 kB | 82/658 kB
Progress (4): 0.7/1.1 MB | 298/365 kB | 58 kB | 98/658 kB
Progress (4): 0.7/1.1 MB | 314/365 kB | 58 kB | 98/658 kB
Progress (4): 0.7/1.1 MB | 314/365 kB | 58 kB | 98/658 kB
Progress (4): 0.7/1.1 MB | 326/365 kB | 58 kB | 98/658 kB
Progress (4): 0.7/1.1 MB | 326/365 kB | 58 kB | 115/658 kB
Progress (4): 0.7/1.1 MB | 326/365 kB | 58 kB | 115/658 kB
Progress (4): 0.7/1.1 MB | 343/365 kB | 58 kB | 115/658 kB
Progress (4): 0.7/1.1 MB | 343/365 kB | 58 kB | 131/658 kB
Progress (4): 0.7/1.1 MB | 359/365 kB | 58 kB | 131/658 kB
Progress (4): 0.7/1.1 MB | 359/365 kB | 58 kB | 147/658 kB
Progress (4): 0.7/1.1 MB | 365 kB | 58 kB | 147/658 kB    
Progress (4): 0.7/1.1 MB | 365 kB | 58 kB | 164/658 kB
Progress (4): 0.7/1.1 MB | 365 kB | 58 kB | 180/658 kB
Progress (4): 0.7/1.1 MB | 365 kB | 58 kB | 197/658 kB
Progress (4): 0.7/1.1 MB | 365 kB | 58 kB | 213/658 kB
Progress (4): 0.7/1.1 MB | 365 kB | 58 kB | 229/658 kB
Progress (4): 0.7/1.1 MB | 365 kB | 58 kB | 246/658 kB
Progress (4): 0.7/1.1 MB | 365 kB | 58 kB | 262/658 kB
Progress (4): 0.7/1.1 MB | 365 kB | 58 kB | 279/658 kB
Progress (4): 0.7/1.1 MB | 365 kB | 58 kB | 295/658 kB
Progress (4): 0.7/1.1 MB | 365 kB | 58 kB | 311/658 kB
Progress (4): 0.7/1.1 MB | 365 kB | 58 kB | 328/658 kB
Progress (4): 0.7/1.1 MB | 365 kB | 58 kB | 344/658 kB
Progress (4): 0.7/1.1 MB | 365 kB | 58 kB | 344/658 kB
Progress (4): 0.7/1.1 MB | 365 kB | 58 kB | 360/658 kB
Progress (4): 0.7/1.1 MB | 365 kB | 58 kB | 377/658 kB
                                                      
Downloaded from central: https://repo.maven.apache.org/maven2/org/iq80/snappy/snappy/0.4/snappy-0.4.jar (58 kB at 131 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/github/luben/zstd-jni/1.5.5-11/zstd-jni-1.5.5-11.jar
Progress (3): 0.7/1.1 MB | 365 kB | 377/658 kB
Progress (3): 0.7/1.1 MB | 365 kB | 393/658 kB
Progress (4): 0.7/1.1 MB | 365 kB | 393/658 kB | 16/116 kB
Progress (4): 0.7/1.1 MB | 365 kB | 410/658 kB | 16/116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 410/658 kB | 16/116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 426/658 kB | 16/116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 426/658 kB | 33/116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 442/658 kB | 33/116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 442/658 kB | 33/116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 442/658 kB | 49/116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 459/658 kB | 49/116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 459/658 kB | 66/116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 475/658 kB | 66/116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 475/658 kB | 66/116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 475/658 kB | 82/116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 492/658 kB | 82/116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 492/658 kB | 98/116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 508/658 kB | 98/116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 508/658 kB | 98/116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 508/658 kB | 115/116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 524/658 kB | 115/116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 524/658 kB | 116 kB    
Progress (4): 0.8/1.1 MB | 365 kB | 524/658 kB | 116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 541/658 kB | 116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 557/658 kB | 116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 557/658 kB | 116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 573/658 kB | 116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 585/658 kB | 116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 585/658 kB | 116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 601/658 kB | 116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 601/658 kB | 116 kB
Progress (4): 0.8/1.1 MB | 365 kB | 617/658 kB | 116 kB
Progress (4): 0.9/1.1 MB | 365 kB | 617/658 kB | 116 kB
Progress (4): 0.9/1.1 MB | 365 kB | 634/658 kB | 116 kB
Progress (4): 0.9/1.1 MB | 365 kB | 650/658 kB | 116 kB
Progress (4): 0.9/1.1 MB | 365 kB | 650/658 kB | 116 kB
Progress (4): 0.9/1.1 MB | 365 kB | 658 kB | 116 kB    
Progress (4): 0.9/1.1 MB | 365 kB | 658 kB | 116 kB
Progress (4): 0.9/1.1 MB | 365 kB | 658 kB | 116 kB
Progress (4): 0.9/1.1 MB | 365 kB | 658 kB | 116 kB
Progress (4): 0.9/1.1 MB | 365 kB | 658 kB | 116 kB
Progress (4): 1.0/1.1 MB | 365 kB | 658 kB | 116 kB
Progress (4): 1.0/1.1 MB | 365 kB | 658 kB | 116 kB
Progress (4): 1.0/1.1 MB | 365 kB | 658 kB | 116 kB
Progress (4): 1.0/1.1 MB | 365 kB | 658 kB | 116 kB
Progress (4): 1.0/1.1 MB | 365 kB | 658 kB | 116 kB
Progress (4): 1.0/1.1 MB | 365 kB | 658 kB | 116 kB
Progress (4): 1.1/1.1 MB | 365 kB | 658 kB | 116 kB
Progress (4): 1.1/1.1 MB | 365 kB | 658 kB | 116 kB
Progress (4): 1.1 MB | 365 kB | 658 kB | 116 kB    
                                               
Downloaded from central: https://repo.maven.apache.org/maven2/commons-codec/commons-codec/1.16.1/commons-codec-1.16.1.jar (365 kB at 791 kB/s)
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-lang3/3.14.0/commons-lang3-3.14.0.jar (658 kB at 1.4 MB/s)
Downloaded from central: https://repo.maven.apache.org/maven2/org/tukaani/xz/1.9/xz-1.9.jar (116 kB at 248 kB/s)
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-compress/1.26.1/commons-compress-1.26.1.jar (1.1 MB at 2.3 MB/s)
Progress (1): 0/6.8 MB
Progress (1): 0/6.8 MB
Progress (1): 0/6.8 MB
Progress (1): 0.1/6.8 MB
Progress (1): 0.1/6.8 MB
Progress (1): 0.1/6.8 MB
Progress (1): 0.1/6.8 MB
Progress (1): 0.1/6.8 MB
Progress (1): 0.1/6.8 MB
Progress (1): 0.2/6.8 MB
Progress (1): 0.2/6.8 MB
Progress (1): 0.2/6.8 MB
Progress (1): 0.2/6.8 MB
Progress (1): 0.2/6.8 MB
Progress (1): 0.2/6.8 MB
Progress (1): 0.3/6.8 MB
Progress (1): 0.3/6.8 MB
Progress (1): 0.3/6.8 MB
Progress (1): 0.3/6.8 MB
Progress (1): 0.3/6.8 MB
Progress (1): 0.3/6.8 MB
Progress (1): 0.4/6.8 MB
Progress (1): 0.4/6.8 MB
Progress (1): 0.4/6.8 MB
Progress (1): 0.4/6.8 MB
Progress (1): 0.4/6.8 MB
Progress (1): 0.4/6.8 MB
Progress (1): 0.5/6.8 MB
Progress (1): 0.5/6.8 MB
Progress (1): 0.5/6.8 MB
Progress (1): 0.5/6.8 MB
Progress (1): 0.5/6.8 MB
Progress (1): 0.5/6.8 MB
Progress (1): 0.5/6.8 MB
Progress (1): 0.6/6.8 MB
Progress (1): 0.6/6.8 MB
Progress (1): 0.6/6.8 MB
Progress (1): 0.6/6.8 MB
Progress (1): 0.6/6.8 MB
Progress (1): 0.6/6.8 MB
Progress (1): 0.7/6.8 MB
Progress (1): 0.7/6.8 MB
Progress (1): 0.7/6.8 MB
Progress (1): 0.7/6.8 MB
Progress (1): 0.7/6.8 MB
Progress (1): 0.7/6.8 MB
Progress (1): 0.8/6.8 MB
Progress (1): 0.8/6.8 MB
Progress (1): 0.8/6.8 MB
Progress (1): 0.8/6.8 MB
Progress (1): 0.8/6.8 MB
Progress (1): 0.8/6.8 MB
Progress (1): 0.9/6.8 MB
Progress (1): 0.9/6.8 MB
Progress (1): 0.9/6.8 MB
Progress (1): 0.9/6.8 MB
Progress (1): 0.9/6.8 MB
Progress (1): 0.9/6.8 MB
Progress (1): 1.0/6.8 MB
Progress (1): 1.0/6.8 MB
Progress (1): 1.0/6.8 MB
Progress (1): 1.0/6.8 MB
Progress (1): 1.0/6.8 MB
Progress (1): 1.0/6.8 MB
Progress (1): 1.1/6.8 MB
Progress (1): 1.1/6.8 MB
Progress (1): 1.1/6.8 MB
Progress (1): 1.1/6.8 MB
Progress (1): 1.1/6.8 MB
Progress (1): 1.1/6.8 MB
Progress (1): 1.2/6.8 MB
Progress (1): 1.2/6.8 MB
Progress (1): 1.2/6.8 MB
Progress (1): 1.2/6.8 MB
Progress (1): 1.2/6.8 MB
Progress (1): 1.2/6.8 MB
Progress (1): 1.2/6.8 MB
Progress (1): 1.3/6.8 MB
Progress (1): 1.3/6.8 MB
Progress (1): 1.3/6.8 MB
Progress (1): 1.3/6.8 MB
Progress (1): 1.3/6.8 MB
Progress (1): 1.3/6.8 MB
Progress (1): 1.4/6.8 MB
Progress (1): 1.4/6.8 MB
Progress (1): 1.4/6.8 MB
Progress (1): 1.4/6.8 MB
Progress (1): 1.4/6.8 MB
Progress (1): 1.4/6.8 MB
Progress (1): 1.5/6.8 MB
Progress (1): 1.5/6.8 MB
Progress (1): 1.5/6.8 MB
Progress (1): 1.5/6.8 MB
Progress (1): 1.5/6.8 MB
Progress (1): 1.5/6.8 MB
Progress (1): 1.6/6.8 MB
Progress (1): 1.6/6.8 MB
Progress (1): 1.6/6.8 MB
Progress (1): 1.6/6.8 MB
Progress (1): 1.6/6.8 MB
Progress (1): 1.6/6.8 MB
Progress (1): 1.7/6.8 MB
Progress (1): 1.7/6.8 MB
Progress (1): 1.7/6.8 MB
Progress (1): 1.7/6.8 MB
Progress (1): 1.7/6.8 MB
Progress (1): 1.7/6.8 MB
Progress (1): 1.8/6.8 MB
Progress (1): 1.8/6.8 MB
Progress (1): 1.8/6.8 MB
Progress (1): 1.8/6.8 MB
Progress (1): 1.8/6.8 MB
Progress (1): 1.8/6.8 MB
Progress (1): 1.9/6.8 MB
Progress (1): 1.9/6.8 MB
Progress (1): 1.9/6.8 MB
Progress (1): 1.9/6.8 MB
Progress (1): 1.9/6.8 MB
Progress (1): 1.9/6.8 MB
Progress (1): 2.0/6.8 MB
Progress (1): 2.0/6.8 MB
Progress (1): 2.0/6.8 MB
Progress (1): 2.0/6.8 MB
Progress (1): 2.0/6.8 MB
Progress (1): 2.0/6.8 MB
Progress (1): 2.1/6.8 MB
Progress (1): 2.1/6.8 MB
Progress (1): 2.1/6.8 MB
Progress (1): 2.1/6.8 MB
Progress (1): 2.1/6.8 MB
Progress (1): 2.1/6.8 MB
Progress (1): 2.1/6.8 MB
Progress (1): 2.2/6.8 MB
Progress (1): 2.2/6.8 MB
Progress (1): 2.2/6.8 MB
Progress (1): 2.2/6.8 MB
Progress (1): 2.2/6.8 MB
Progress (1): 2.2/6.8 MB
Progress (1): 2.3/6.8 MB
Progress (1): 2.3/6.8 MB
Progress (1): 2.3/6.8 MB
Progress (1): 2.3/6.8 MB
Progress (1): 2.3/6.8 MB
Progress (1): 2.3/6.8 MB
Progress (1): 2.4/6.8 MB
Progress (1): 2.4/6.8 MB
Progress (1): 2.4/6.8 MB
Progress (1): 2.4/6.8 MB
Progress (1): 2.4/6.8 MB
Progress (1): 2.4/6.8 MB
Progress (1): 2.5/6.8 MB
Progress (1): 2.5/6.8 MB
Progress (1): 2.5/6.8 MB
Progress (1): 2.5/6.8 MB
Progress (1): 2.5/6.8 MB
Progress (1): 2.5/6.8 MB
Progress (1): 2.5/6.8 MB
Progress (1): 2.6/6.8 MB
Progress (1): 2.6/6.8 MB
Progress (1): 2.6/6.8 MB
Progress (1): 2.6/6.8 MB
Progress (1): 2.6/6.8 MB
Progress (1): 2.6/6.8 MB
Progress (1): 2.7/6.8 MB
Progress (1): 2.7/6.8 MB
Progress (1): 2.7/6.8 MB
Progress (1): 2.7/6.8 MB
Progress (1): 2.7/6.8 MB
Progress (1): 2.7/6.8 MB
Progress (1): 2.8/6.8 MB
Progress (1): 2.8/6.8 MB
Progress (1): 2.8/6.8 MB
Progress (1): 2.8/6.8 MB
Progress (1): 2.8/6.8 MB
Progress (1): 2.8/6.8 MB
Progress (1): 2.9/6.8 MB
Progress (1): 2.9/6.8 MB
Progress (1): 2.9/6.8 MB
Progress (1): 2.9/6.8 MB
Progress (1): 2.9/6.8 MB
Progress (1): 2.9/6.8 MB
Progress (1): 3.0/6.8 MB
Progress (1): 3.0/6.8 MB
Progress (1): 3.0/6.8 MB
Progress (1): 3.0/6.8 MB
Progress (1): 3.0/6.8 MB
Progress (1): 3.0/6.8 MB
Progress (1): 3.1/6.8 MB
Progress (1): 3.1/6.8 MB
Progress (1): 3.1/6.8 MB
Progress (1): 3.1/6.8 MB
Progress (1): 3.1/6.8 MB
Progress (1): 3.1/6.8 MB
Progress (1): 3.2/6.8 MB
Progress (1): 3.2/6.8 MB
Progress (1): 3.2/6.8 MB
Progress (1): 3.2/6.8 MB
Progress (1): 3.2/6.8 MB
Progress (1): 3.2/6.8 MB
Progress (1): 3.2/6.8 MB
Progress (1): 3.3/6.8 MB
Progress (1): 3.3/6.8 MB
Progress (1): 3.3/6.8 MB
Progress (1): 3.3/6.8 MB
Progress (1): 3.3/6.8 MB
Progress (1): 3.3/6.8 MB
Progress (1): 3.4/6.8 MB
Progress (1): 3.4/6.8 MB
Progress (1): 3.4/6.8 MB
Progress (1): 3.4/6.8 MB
Progress (1): 3.4/6.8 MB
Progress (1): 3.4/6.8 MB
Progress (1): 3.5/6.8 MB
Progress (1): 3.5/6.8 MB
Progress (1): 3.5/6.8 MB
Progress (1): 3.5/6.8 MB
Progress (1): 3.5/6.8 MB
Progress (1): 3.5/6.8 MB
Progress (1): 3.6/6.8 MB
Progress (1): 3.6/6.8 MB
Progress (1): 3.6/6.8 MB
Progress (1): 3.6/6.8 MB
Progress (1): 3.6/6.8 MB
Progress (1): 3.6/6.8 MB
Progress (1): 3.7/6.8 MB
Progress (1): 3.7/6.8 MB
Progress (1): 3.7/6.8 MB
Progress (1): 3.7/6.8 MB
Progress (1): 3.7/6.8 MB
Progress (1): 3.7/6.8 MB
Progress (1): 3.8/6.8 MB
Progress (1): 3.8/6.8 MB
Progress (1): 3.8/6.8 MB
Progress (1): 3.8/6.8 MB
Progress (1): 3.8/6.8 MB
Progress (1): 3.8/6.8 MB
Progress (1): 3.8/6.8 MB
Progress (1): 3.9/6.8 MB
Progress (1): 3.9/6.8 MB
Progress (1): 3.9/6.8 MB
Progress (1): 3.9/6.8 MB
Progress (1): 3.9/6.8 MB
Progress (1): 3.9/6.8 MB
Progress (1): 4.0/6.8 MB
Progress (1): 4.0/6.8 MB
Progress (1): 4.0/6.8 MB
Progress (1): 4.0/6.8 MB
Progress (1): 4.0/6.8 MB
Progress (1): 4.0/6.8 MB
Progress (1): 4.1/6.8 MB
Progress (1): 4.1/6.8 MB
Progress (1): 4.1/6.8 MB
Progress (1): 4.1/6.8 MB
Progress (1): 4.1/6.8 MB
Progress (1): 4.1/6.8 MB
Progress (1): 4.2/6.8 MB
Progress (1): 4.2/6.8 MB
Progress (1): 4.2/6.8 MB
Progress (1): 4.2/6.8 MB
Progress (1): 4.2/6.8 MB
Progress (1): 4.2/6.8 MB
Progress (1): 4.3/6.8 MB
Progress (1): 4.3/6.8 MB
Progress (1): 4.3/6.8 MB
Progress (1): 4.3/6.8 MB
Progress (1): 4.3/6.8 MB
Progress (1): 4.3/6.8 MB
Progress (1): 4.4/6.8 MB
Progress (1): 4.4/6.8 MB
Progress (1): 4.4/6.8 MB
Progress (1): 4.4/6.8 MB
Progress (1): 4.4/6.8 MB
Progress (1): 4.4/6.8 MB
Progress (1): 4.5/6.8 MB
Progress (1): 4.5/6.8 MB
Progress (1): 4.5/6.8 MB
Progress (1): 4.5/6.8 MB
Progress (1): 4.5/6.8 MB
Progress (1): 4.5/6.8 MB
Progress (1): 4.5/6.8 MB
Progress (1): 4.6/6.8 MB
Progress (1): 4.6/6.8 MB
Progress (1): 4.6/6.8 MB
Progress (1): 4.6/6.8 MB
Progress (1): 4.6/6.8 MB
Progress (1): 4.6/6.8 MB
Progress (1): 4.7/6.8 MB
Progress (1): 4.7/6.8 MB
Progress (1): 4.7/6.8 MB
Progress (1): 4.7/6.8 MB
Progress (1): 4.7/6.8 MB
Progress (1): 4.7/6.8 MB
Progress (1): 4.8/6.8 MB
Progress (1): 4.8/6.8 MB
Progress (1): 4.8/6.8 MB
Progress (1): 4.8/6.8 MB
Progress (1): 4.8/6.8 MB
Progress (1): 4.8/6.8 MB
Progress (1): 4.9/6.8 MB
Progress (1): 4.9/6.8 MB
Progress (1): 4.9/6.8 MB
Progress (1): 4.9/6.8 MB
Progress (1): 4.9/6.8 MB
Progress (1): 4.9/6.8 MB
Progress (1): 5.0/6.8 MB
Progress (1): 5.0/6.8 MB
Progress (1): 5.0/6.8 MB
Progress (1): 5.0/6.8 MB
Progress (1): 5.0/6.8 MB
Progress (1): 5.0/6.8 MB
Progress (1): 5.1/6.8 MB
Progress (1): 5.1/6.8 MB
Progress (1): 5.1/6.8 MB
Progress (1): 5.1/6.8 MB
Progress (1): 5.1/6.8 MB
Progress (1): 5.1/6.8 MB
Progress (1): 5.1/6.8 MB
Progress (1): 5.2/6.8 MB
Progress (1): 5.2/6.8 MB
Progress (1): 5.2/6.8 MB
Progress (1): 5.2/6.8 MB
Progress (1): 5.2/6.8 MB
Progress (1): 5.2/6.8 MB
Progress (1): 5.3/6.8 MB
Progress (1): 5.3/6.8 MB
Progress (1): 5.3/6.8 MB
Progress (1): 5.3/6.8 MB
Progress (1): 5.3/6.8 MB
Progress (1): 5.3/6.8 MB
Progress (1): 5.4/6.8 MB
Progress (1): 5.4/6.8 MB
Progress (1): 5.4/6.8 MB
Progress (1): 5.4/6.8 MB
Progress (1): 5.4/6.8 MB
Progress (1): 5.4/6.8 MB
Progress (1): 5.5/6.8 MB
Progress (1): 5.5/6.8 MB
Progress (1): 5.5/6.8 MB
Progress (1): 5.5/6.8 MB
Progress (1): 5.5/6.8 MB
Progress (1): 5.5/6.8 MB
Progress (1): 5.6/6.8 MB
Progress (1): 5.6/6.8 MB
Progress (1): 5.6/6.8 MB
Progress (1): 5.6/6.8 MB
Progress (1): 5.6/6.8 MB
Progress (1): 5.6/6.8 MB
Progress (1): 5.7/6.8 MB
Progress (1): 5.7/6.8 MB
Progress (1): 5.7/6.8 MB
Progress (1): 5.7/6.8 MB
Progress (1): 5.7/6.8 MB
Progress (1): 5.7/6.8 MB
Progress (1): 5.7/6.8 MB
Progress (1): 5.8/6.8 MB
Progress (1): 5.8/6.8 MB
Progress (1): 5.8/6.8 MB
Progress (1): 5.8/6.8 MB
Progress (1): 5.8/6.8 MB
Progress (1): 5.8/6.8 MB
Progress (1): 5.9/6.8 MB
Progress (1): 5.9/6.8 MB
Progress (1): 5.9/6.8 MB
Progress (1): 5.9/6.8 MB
Progress (1): 5.9/6.8 MB
Progress (1): 5.9/6.8 MB
Progress (1): 6.0/6.8 MB
Progress (1): 6.0/6.8 MB
Progress (1): 6.0/6.8 MB
Progress (1): 6.0/6.8 MB
Progress (1): 6.0/6.8 MB
Progress (1): 6.0/6.8 MB
Progress (1): 6.1/6.8 MB
Progress (1): 6.1/6.8 MB
Progress (1): 6.1/6.8 MB
Progress (1): 6.1/6.8 MB
Progress (1): 6.1/6.8 MB
Progress (1): 6.1/6.8 MB
Progress (1): 6.1/6.8 MB
Progress (1): 6.2/6.8 MB
Progress (1): 6.2/6.8 MB
Progress (1): 6.2/6.8 MB
Progress (1): 6.2/6.8 MB
Progress (1): 6.2/6.8 MB
Progress (1): 6.2/6.8 MB
Progress (1): 6.3/6.8 MB
Progress (1): 6.3/6.8 MB
Progress (1): 6.3/6.8 MB
Progress (1): 6.3/6.8 MB
Progress (1): 6.3/6.8 MB
Progress (1): 6.3/6.8 MB
Progress (1): 6.4/6.8 MB
Progress (1): 6.4/6.8 MB
Progress (1): 6.4/6.8 MB
Progress (1): 6.4/6.8 MB
Progress (1): 6.4/6.8 MB
Progress (1): 6.4/6.8 MB
Progress (1): 6.4/6.8 MB
Progress (1): 6.5/6.8 MB
Progress (1): 6.5/6.8 MB
Progress (1): 6.5/6.8 MB
Progress (1): 6.5/6.8 MB
Progress (1): 6.5/6.8 MB
Progress (1): 6.5/6.8 MB
Progress (1): 6.6/6.8 MB
Progress (1): 6.6/6.8 MB
Progress (1): 6.6/6.8 MB
Progress (1): 6.6/6.8 MB
Progress (1): 6.6/6.8 MB
Progress (1): 6.6/6.8 MB
Progress (1): 6.7/6.8 MB
Progress (1): 6.7/6.8 MB
Progress (1): 6.7/6.8 MB
Progress (1): 6.7/6.8 MB
Progress (1): 6.7/6.8 MB
Progress (1): 6.7/6.8 MB
Progress (1): 6.8/6.8 MB
Progress (1): 6.8/6.8 MB
Progress (1): 6.8 MB    
                    
Downloaded from central: https://repo.maven.apache.org/maven2/com/github/luben/zstd-jni/1.5.5-11/zstd-jni-1.5.5-11.jar (6.8 MB at 11 MB/s)
[INFO] Building jar: C:\esocial-novo\target\esocial-novo-0.0.1-SNAPSHOT.jar
[INFO] 
[INFO] --- spring-boot:3.4.5:repackage (repackage) @ esocial-novo ---
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-buildpack-platform/3.4.5/spring-boot-buildpack-platform-3.4.5.pom
Progress (1): 3.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-buildpack-platform/3.4.5/spring-boot-buildpack-platform-3.4.5.pom (3.2 kB at 100 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/net/java/dev/jna/jna-platform/5.13.0/jna-platform-5.13.0.pom
Progress (1): 2.3 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/net/java/dev/jna/jna-platform/5.13.0/jna-platform-5.13.0.pom (2.3 kB at 73 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/net/java/dev/jna/jna/5.13.0/jna-5.13.0.pom
Progress (1): 2.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/net/java/dev/jna/jna/5.13.0/jna-5.13.0.pom (2.0 kB at 63 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-compress/1.25.0/commons-compress-1.25.0.pom
Progress (1): 16/22 kB
Progress (1): 22 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-compress/1.25.0/commons-compress-1.25.0.pom (22 kB at 603 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/client5/httpclient5/5.4.3/httpclient5-5.4.3.pom
Progress (1): 6.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/client5/httpclient5/5.4.3/httpclient5-5.4.3.pom (6.1 kB at 202 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/client5/httpclient5-parent/5.4.3/httpclient5-parent-5.4.3.pom
Progress (1): 16 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/client5/httpclient5-parent/5.4.3/httpclient5-parent-5.4.3.pom (16 kB at 407 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/httpcomponents-parent/13/httpcomponents-parent-13.pom
Progress (1): 16/30 kB
Progress (1): 30 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/httpcomponents-parent/13/httpcomponents-parent-13.pom (30 kB at 1.2 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/apache/27/apache-27.pom
Progress (1): 16/20 kB
Progress (1): 20 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/apache/27/apache-27.pom (20 kB at 848 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/junit/junit-bom/5.11.0/junit-bom-5.11.0.pom
Progress (1): 5.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/junit/junit-bom/5.11.0/junit-bom-5.11.0.pom (5.6 kB at 235 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/core5/httpcore5/5.3.4/httpcore5-5.3.4.pom
Progress (1): 3.9 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/core5/httpcore5/5.3.4/httpcore5-5.3.4.pom (3.9 kB at 164 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/core5/httpcore5-parent/5.3.4/httpcore5-parent-5.3.4.pom
Progress (1): 14 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/core5/httpcore5-parent/5.3.4/httpcore5-parent-5.3.4.pom (14 kB at 558 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/core5/httpcore5-h2/5.3.4/httpcore5-h2-5.3.4.pom
Progress (1): 3.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/core5/httpcore5-h2/5.3.4/httpcore5-h2-5.3.4.pom (3.6 kB at 113 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/tomlj/tomlj/1.0.0/tomlj-1.0.0.pom
Progress (1): 2.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/tomlj/tomlj/1.0.0/tomlj-1.0.0.pom (2.8 kB at 113 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/antlr/antlr4-runtime/4.7.2/antlr4-runtime-4.7.2.pom
Progress (1): 3.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/antlr/antlr4-runtime/4.7.2/antlr4-runtime-4.7.2.pom (3.6 kB at 113 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/antlr/antlr4-master/4.7.2/antlr4-master-4.7.2.pom
Progress (1): 4.4 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/antlr/antlr4-master/4.7.2/antlr4-master-4.7.2.pom (4.4 kB at 142 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.pom
Progress (1): 4.3 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.pom (4.3 kB at 134 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-loader-tools/3.4.5/spring-boot-loader-tools-3.4.5.pom
Progress (1): 2.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-loader-tools/3.4.5/spring-boot-loader-tools-3.4.5.pom (2.2 kB at 124 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-common-artifact-filters/3.3.2/maven-common-artifact-filters-3.3.2.pom
Progress (1): 5.3 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-common-artifact-filters/3.3.2/maven-common-artifact-filters-3.3.2.pom (5.3 kB at 329 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-components/37/maven-shared-components-37.pom
Progress (1): 4.9 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-shared-components/37/maven-shared-components-37.pom (4.9 kB at 153 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/37/maven-parent-37.pom
Progress (1): 16/46 kB
Progress (1): 33/46 kB
Progress (1): 46 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/maven-parent/37/maven-parent-37.pom (46 kB at 1.5 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/io/micrometer/micrometer-observation/1.14.5/micrometer-observation-1.14.5.pom
Progress (1): 3.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/io/micrometer/micrometer-observation/1.14.5/micrometer-observation-1.14.5.pom (3.8 kB at 120 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/io/micrometer/micrometer-commons/1.14.5/micrometer-commons-1.14.5.pom
Progress (1): 3.4 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/io/micrometer/micrometer-commons/1.14.5/micrometer-commons-1.14.5.pom (3.4 kB at 106 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-shade-plugin/3.5.0/maven-shade-plugin-3.5.0.pom
Progress (1): 12 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-shade-plugin/3.5.0/maven-shade-plugin-3.5.0.pom (12 kB at 401 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/slf4j/slf4j-api/1.7.32/slf4j-api-1.7.32.pom
Progress (1): 3.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/slf4j/slf4j-api/1.7.32/slf4j-api-1.7.32.pom (3.8 kB at 101 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/slf4j/slf4j-parent/1.7.32/slf4j-parent-1.7.32.pom
Progress (1): 14 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/slf4j/slf4j-parent/1.7.32/slf4j-parent-1.7.32.pom (14 kB at 445 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/ow2/asm/asm/9.5/asm-9.5.pom
Progress (1): 2.4 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/ow2/asm/asm/9.5/asm-9.5.pom (2.4 kB at 74 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/ow2/asm/asm-commons/9.5/asm-commons-9.5.pom
Progress (1): 2.8 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/ow2/asm/asm-commons/9.5/asm-commons-9.5.pom (2.8 kB at 87 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/ow2/asm/asm-tree/9.5/asm-tree-9.5.pom
Progress (1): 2.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/ow2/asm/asm-tree/9.5/asm-tree-9.5.pom (2.6 kB at 162 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/jdom/jdom2/*******/jdom2-*******.pom
Progress (1): 4.6 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/jdom/jdom2/*******/jdom2-*******.pom (4.6 kB at 144 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-dependency-tree/3.2.1/maven-dependency-tree-3.2.1.pom
Progress (1): 6.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-dependency-tree/3.2.1/maven-dependency-tree-3.2.1.pom (6.2 kB at 240 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/eclipse/aether/aether-util/1.0.0.v20140518/aether-util-1.0.0.v20140518.pom
Progress (1): 2.2 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/eclipse/aether/aether-util/1.0.0.v20140518/aether-util-1.0.0.v20140518.pom (2.2 kB at 69 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/eclipse/aether/aether/1.0.0.v20140518/aether-1.0.0.v20140518.pom
Progress (1): 16/30 kB
Progress (1): 30 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/eclipse/aether/aether/1.0.0.v20140518/aether-1.0.0.v20140518.pom (30 kB at 1.5 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/eclipse/aether/aether-api/1.0.0.v20140518/aether-api-1.0.0.v20140518.pom
Progress (1): 1.9 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/eclipse/aether/aether-api/1.0.0.v20140518/aether-api-1.0.0.v20140518.pom (1.9 kB at 95 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/commons-io/commons-io/2.13.0/commons-io-2.13.0.pom
Progress (1): 16/20 kB
Progress (1): 20 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/commons-io/commons-io/2.13.0/commons-io-2.13.0.pom (20 kB at 398 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-parent/58/commons-parent-58.pom
Progress (1): 16/83 kB
Progress (1): 33/83 kB
Progress (1): 49/83 kB
Progress (1): 66/83 kB
Progress (1): 82/83 kB
Progress (1): 83 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-parent/58/commons-parent-58.pom (83 kB at 2.3 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/vafer/jdependency/2.8.0/jdependency-2.8.0.pom
Progress (1): 14 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/vafer/jdependency/2.8.0/jdependency-2.8.0.pom (14 kB at 771 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.pom
Progress (1): 16/24 kB
Progress (1): 24 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.pom (24 kB at 767 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-parent/48/commons-parent-48.pom
Progress (1): 16/72 kB
Progress (1): 32/72 kB
Progress (1): 49/72 kB
Progress (1): 65/72 kB
Progress (1): 72 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-parent/48/commons-parent-48.pom (72 kB at 2.3 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-buildpack-platform/3.4.5/spring-boot-buildpack-platform-3.4.5.jar
Progress (1): 16/300 kB
Progress (1): 33/300 kB
Progress (1): 49/300 kB
Progress (1): 65/300 kB
Progress (1): 81/300 kB
Progress (1): 98/300 kB
Progress (1): 114/300 kB
Progress (1): 130/300 kB
Progress (1): 147/300 kB
Progress (1): 163/300 kB
Progress (1): 180/300 kB
Progress (1): 196/300 kB
Progress (1): 212/300 kB
Progress (1): 229/300 kB
Progress (1): 245/300 kB
Progress (1): 262/300 kB
Progress (1): 278/300 kB
Progress (1): 294/300 kB
Progress (1): 300 kB    
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-buildpack-platform/3.4.5/spring-boot-buildpack-platform-3.4.5.jar (300 kB at 9.7 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-compress/1.25.0/commons-compress-1.25.0.jar
Progress (1): 0/1.1 MB
Progress (1): 0/1.1 MB
Progress (1): 0/1.1 MB
Progress (1): 0.1/1.1 MB
Progress (1): 0.1/1.1 MB
Progress (1): 0.1/1.1 MB
Progress (1): 0.1/1.1 MB
Progress (1): 0.1/1.1 MB
Progress (1): 0.1/1.1 MB
Progress (1): 0.2/1.1 MB
Progress (1): 0.2/1.1 MB
Progress (1): 0.2/1.1 MB
Progress (1): 0.2/1.1 MB
Progress (1): 0.2/1.1 MB
Progress (1): 0.2/1.1 MB
Progress (1): 0.3/1.1 MB
Progress (1): 0.3/1.1 MB
Progress (1): 0.3/1.1 MB
Progress (1): 0.3/1.1 MB
Progress (1): 0.3/1.1 MB
Progress (1): 0.3/1.1 MB
Progress (1): 0.4/1.1 MB
Progress (1): 0.4/1.1 MB
Progress (1): 0.4/1.1 MB
Progress (1): 0.4/1.1 MB
Progress (1): 0.4/1.1 MB
Progress (1): 0.4/1.1 MB
Progress (1): 0.5/1.1 MB
Progress (1): 0.5/1.1 MB
Progress (1): 0.5/1.1 MB
Progress (1): 0.5/1.1 MB
Progress (1): 0.5/1.1 MB
Progress (1): 0.5/1.1 MB
Progress (1): 0.6/1.1 MB
Progress (1): 0.6/1.1 MB
Progress (1): 0.6/1.1 MB
Progress (1): 0.6/1.1 MB
Progress (1): 0.6/1.1 MB
Progress (1): 0.6/1.1 MB
Progress (1): 0.7/1.1 MB
Progress (1): 0.7/1.1 MB
Progress (1): 0.7/1.1 MB
Progress (1): 0.7/1.1 MB
Progress (1): 0.7/1.1 MB
Progress (1): 0.7/1.1 MB
Progress (1): 0.7/1.1 MB
Progress (1): 0.8/1.1 MB
Progress (1): 0.8/1.1 MB
Progress (1): 0.8/1.1 MB
Progress (1): 0.8/1.1 MB
Progress (1): 0.8/1.1 MB
Progress (1): 0.8/1.1 MB
Progress (1): 0.9/1.1 MB
Progress (1): 0.9/1.1 MB
Progress (1): 0.9/1.1 MB
Progress (1): 0.9/1.1 MB
Progress (1): 0.9/1.1 MB
Progress (1): 0.9/1.1 MB
Progress (1): 1.0/1.1 MB
Progress (1): 1.0/1.1 MB
Progress (1): 1.0/1.1 MB
Progress (1): 1.0/1.1 MB
Progress (1): 1.0/1.1 MB
Progress (1): 1.0/1.1 MB
Progress (1): 1.1/1.1 MB
Progress (1): 1.1/1.1 MB
Progress (1): 1.1 MB    
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-compress/1.25.0/commons-compress-1.25.0.jar (1.1 MB at 6.3 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/core5/httpcore5-h2/5.3.4/httpcore5-h2-5.3.4.jar
Progress (1): 16/242 kB
Progress (1): 32/242 kB
Progress (1): 49/242 kB
Progress (1): 65/242 kB
Progress (1): 81/242 kB
Progress (1): 98/242 kB
Progress (1): 114/242 kB
Progress (1): 130/242 kB
Progress (1): 147/242 kB
Progress (1): 163/242 kB
Progress (1): 180/242 kB
Progress (1): 196/242 kB
Progress (1): 212/242 kB
Progress (1): 229/242 kB
Progress (1): 242 kB    
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/core5/httpcore5-h2/5.3.4/httpcore5-h2-5.3.4.jar (242 kB at 1.2 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/tomlj/tomlj/1.0.0/tomlj-1.0.0.jar
Progress (1): 16/157 kB
Progress (1): 33/157 kB
Progress (1): 49/157 kB
Progress (1): 62/157 kB
Progress (1): 78/157 kB
Progress (1): 95/157 kB
Progress (1): 111/157 kB
Progress (1): 128/157 kB
Progress (1): 144/157 kB
Progress (1): 157 kB    
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/tomlj/tomlj/1.0.0/tomlj-1.0.0.jar (157 kB at 718 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/antlr/antlr4-runtime/4.7.2/antlr4-runtime-4.7.2.jar
Progress (1): 16/338 kB
Progress (1): 33/338 kB
Progress (1): 49/338 kB
Progress (1): 63/338 kB
Progress (1): 80/338 kB
Progress (1): 96/338 kB
Progress (1): 112/338 kB
Progress (1): 129/338 kB
Progress (1): 145/338 kB
Progress (1): 162/338 kB
Progress (1): 178/338 kB
Progress (1): 194/338 kB
Progress (1): 211/338 kB
Progress (1): 227/338 kB
Progress (1): 243/338 kB
Progress (1): 252/338 kB
Progress (1): 260/338 kB
Progress (1): 276/338 kB
Progress (1): 293/338 kB
Progress (1): 309/338 kB
Progress (1): 325/338 kB
Progress (1): 338 kB    
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/antlr/antlr4-runtime/4.7.2/antlr4-runtime-4.7.2.jar (338 kB at 1.4 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar
Progress (1): 16/20 kB
Progress (1): 20 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar (20 kB at 71 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-loader-tools/3.4.5/spring-boot-loader-tools-3.4.5.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/client5/httpclient5/5.4.3/httpclient5-5.4.3.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/core5/httpcore5/5.3.4/httpcore5-5.3.4.jar
Downloading from central: https://repo.maven.apache.org/maven2/net/java/dev/jna/jna-platform/5.13.0/jna-platform-5.13.0.jar
Progress (1): 15/465 kB
Progress (1): 31/465 kB
Progress (1): 48/465 kB
Progress (1): 64/465 kB
Progress (1): 80/465 kB
Progress (1): 97/465 kB
Progress (1): 113/465 kB
Progress (1): 130/465 kB
Progress (1): 146/465 kB
Progress (1): 162/465 kB
Progress (1): 179/465 kB
Progress (1): 195/465 kB
Progress (1): 211/465 kB
Progress (1): 228/465 kB
Progress (1): 244/465 kB
Progress (1): 261/465 kB
Progress (1): 277/465 kB
Progress (1): 293/465 kB
Progress (1): 310/465 kB
Progress (1): 326/465 kB
Progress (1): 343/465 kB
Progress (1): 359/465 kB
Progress (1): 375/465 kB
Progress (1): 392/465 kB
Progress (1): 408/465 kB
Progress (1): 424/465 kB
Progress (1): 441/465 kB
Progress (1): 457/465 kB
Progress (1): 465 kB    
Progress (2): 465 kB | 16/910 kB
Progress (2): 465 kB | 32/910 kB
Progress (2): 465 kB | 49/910 kB
Progress (2): 465 kB | 65/910 kB
Progress (2): 465 kB | 81/910 kB
Progress (2): 465 kB | 98/910 kB
Progress (2): 465 kB | 114/910 kB
Progress (2): 465 kB | 130/910 kB
Progress (2): 465 kB | 147/910 kB
Progress (2): 465 kB | 163/910 kB
Progress (2): 465 kB | 180/910 kB
Progress (2): 465 kB | 196/910 kB
Progress (2): 465 kB | 212/910 kB
Progress (2): 465 kB | 229/910 kB
Progress (2): 465 kB | 245/910 kB
Progress (2): 465 kB | 262/910 kB
Progress (2): 465 kB | 278/910 kB
Progress (2): 465 kB | 294/910 kB
Progress (2): 465 kB | 311/910 kB
Progress (2): 465 kB | 327/910 kB
Progress (2): 465 kB | 343/910 kB
Progress (2): 465 kB | 360/910 kB
Progress (2): 465 kB | 376/910 kB
                                 
Downloaded from central: https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-loader-tools/3.4.5/spring-boot-loader-tools-3.4.5.jar (465 kB at 1.5 MB/s)
Progress (1): 393/910 kB
                        
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-common-artifact-filters/3.3.2/maven-common-artifact-filters-3.3.2.jar
Progress (1): 409/910 kB
Progress (1): 425/910 kB
Progress (1): 442/910 kB
Progress (1): 458/910 kB
Progress (1): 475/910 kB
Progress (1): 491/910 kB
Progress (1): 507/910 kB
Progress (1): 524/910 kB
Progress (1): 540/910 kB
Progress (1): 556/910 kB
Progress (1): 573/910 kB
Progress (1): 589/910 kB
Progress (1): 606/910 kB
Progress (1): 622/910 kB
Progress (1): 638/910 kB
Progress (1): 655/910 kB
Progress (1): 671/910 kB
Progress (1): 688/910 kB
Progress (1): 704/910 kB
Progress (1): 720/910 kB
Progress (1): 737/910 kB
Progress (1): 753/910 kB
Progress (1): 769/910 kB
Progress (1): 786/910 kB
Progress (1): 802/910 kB
Progress (1): 819/910 kB
Progress (1): 835/910 kB
Progress (1): 851/910 kB
Progress (1): 868/910 kB
Progress (1): 884/910 kB
Progress (1): 901/910 kB
Progress (1): 910 kB    
Progress (2): 910 kB | 0/1.4 MB
Progress (2): 910 kB | 0/1.4 MB
Progress (3): 910 kB | 0/1.4 MB | 16/909 kB
Progress (3): 910 kB | 0/1.4 MB | 16/909 kB
Progress (3): 910 kB | 0/1.4 MB | 32/909 kB
Progress (3): 910 kB | 0.1/1.4 MB | 32/909 kB
Progress (3): 910 kB | 0.1/1.4 MB | 49/909 kB
Progress (3): 910 kB | 0.1/1.4 MB | 49/909 kB
Progress (3): 910 kB | 0.1/1.4 MB | 65/909 kB
Progress (3): 910 kB | 0.1/1.4 MB | 65/909 kB
Progress (3): 910 kB | 0.1/1.4 MB | 81/909 kB
Progress (3): 910 kB | 0.1/1.4 MB | 81/909 kB
Progress (3): 910 kB | 0.1/1.4 MB | 98/909 kB
Progress (3): 910 kB | 0.1/1.4 MB | 98/909 kB
Progress (3): 910 kB | 0.1/1.4 MB | 114/909 kB
Progress (3): 910 kB | 0.1/1.4 MB | 114/909 kB
Progress (3): 910 kB | 0.1/1.4 MB | 130/909 kB
Progress (3): 910 kB | 0.2/1.4 MB | 130/909 kB
Progress (3): 910 kB | 0.2/1.4 MB | 147/909 kB
Progress (3): 910 kB | 0.2/1.4 MB | 147/909 kB
Progress (3): 910 kB | 0.2/1.4 MB | 163/909 kB
Progress (3): 910 kB | 0.2/1.4 MB | 163/909 kB
Progress (3): 910 kB | 0.2/1.4 MB | 180/909 kB
Progress (3): 910 kB | 0.2/1.4 MB | 180/909 kB
Progress (3): 910 kB | 0.2/1.4 MB | 196/909 kB
Progress (3): 910 kB | 0.2/1.4 MB | 196/909 kB
Progress (3): 910 kB | 0.2/1.4 MB | 212/909 kB
Progress (3): 910 kB | 0.2/1.4 MB | 212/909 kB
Progress (3): 910 kB | 0.2/1.4 MB | 229/909 kB
Progress (3): 910 kB | 0.3/1.4 MB | 229/909 kB
Progress (3): 910 kB | 0.3/1.4 MB | 245/909 kB
Progress (3): 910 kB | 0.3/1.4 MB | 262/909 kB
Progress (3): 910 kB | 0.3/1.4 MB | 262/909 kB
Progress (3): 910 kB | 0.3/1.4 MB | 262/909 kB
Progress (3): 910 kB | 0.3/1.4 MB | 278/909 kB
Progress (3): 910 kB | 0.3/1.4 MB | 278/909 kB
Progress (3): 910 kB | 0.3/1.4 MB | 294/909 kB
Progress (3): 910 kB | 0.3/1.4 MB | 294/909 kB
Progress (3): 910 kB | 0.3/1.4 MB | 311/909 kB
Progress (3): 910 kB | 0.3/1.4 MB | 327/909 kB
Progress (3): 910 kB | 0.3/1.4 MB | 327/909 kB
Progress (3): 910 kB | 0.3/1.4 MB | 343/909 kB
Progress (3): 910 kB | 0.4/1.4 MB | 343/909 kB
Progress (3): 910 kB | 0.4/1.4 MB | 360/909 kB
Progress (3): 910 kB | 0.4/1.4 MB | 360/909 kB
Progress (3): 910 kB | 0.4/1.4 MB | 360/909 kB
Progress (4): 910 kB | 0.4/1.4 MB | 360/909 kB | 16/58 kB
Progress (4): 910 kB | 0.4/1.4 MB | 376/909 kB | 16/58 kB
Progress (4): 910 kB | 0.4/1.4 MB | 376/909 kB | 16/58 kB
Progress (4): 910 kB | 0.4/1.4 MB | 376/909 kB | 33/58 kB
Progress (4): 910 kB | 0.4/1.4 MB | 393/909 kB | 33/58 kB
Progress (4): 910 kB | 0.4/1.4 MB | 393/909 kB | 33/58 kB
Progress (4): 910 kB | 0.4/1.4 MB | 409/909 kB | 33/58 kB
Progress (4): 910 kB | 0.4/1.4 MB | 409/909 kB | 49/58 kB
Progress (4): 910 kB | 0.4/1.4 MB | 409/909 kB | 49/58 kB
Progress (4): 910 kB | 0.4/1.4 MB | 409/909 kB | 58 kB   
Progress (4): 910 kB | 0.4/1.4 MB | 425/909 kB | 58 kB
Progress (4): 910 kB | 0.5/1.4 MB | 425/909 kB | 58 kB
Progress (4): 910 kB | 0.5/1.4 MB | 442/909 kB | 58 kB
Progress (4): 910 kB | 0.5/1.4 MB | 442/909 kB | 58 kB
Progress (4): 910 kB | 0.5/1.4 MB | 458/909 kB | 58 kB
Progress (4): 910 kB | 0.5/1.4 MB | 458/909 kB | 58 kB
Progress (4): 910 kB | 0.5/1.4 MB | 475/909 kB | 58 kB
Progress (4): 910 kB | 0.5/1.4 MB | 475/909 kB | 58 kB
Progress (4): 910 kB | 0.5/1.4 MB | 491/909 kB | 58 kB
Progress (4): 910 kB | 0.5/1.4 MB | 491/909 kB | 58 kB
Progress (4): 910 kB | 0.5/1.4 MB | 507/909 kB | 58 kB
Progress (4): 910 kB | 0.5/1.4 MB | 507/909 kB | 58 kB
Progress (4): 910 kB | 0.5/1.4 MB | 524/909 kB | 58 kB
Progress (4): 910 kB | 0.6/1.4 MB | 524/909 kB | 58 kB
Progress (4): 910 kB | 0.6/1.4 MB | 540/909 kB | 58 kB
Progress (4): 910 kB | 0.6/1.4 MB | 540/909 kB | 58 kB
Progress (4): 910 kB | 0.6/1.4 MB | 556/909 kB | 58 kB
Progress (4): 910 kB | 0.6/1.4 MB | 556/909 kB | 58 kB
Progress (4): 910 kB | 0.6/1.4 MB | 573/909 kB | 58 kB
Progress (4): 910 kB | 0.6/1.4 MB | 573/909 kB | 58 kB
Progress (4): 910 kB | 0.6/1.4 MB | 589/909 kB | 58 kB
Progress (4): 910 kB | 0.6/1.4 MB | 589/909 kB | 58 kB
Progress (4): 910 kB | 0.6/1.4 MB | 589/909 kB | 58 kB
Progress (4): 910 kB | 0.6/1.4 MB | 606/909 kB | 58 kB
Progress (4): 910 kB | 0.6/1.4 MB | 622/909 kB | 58 kB
                                                      
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/client5/httpclient5/5.4.3/httpclient5-5.4.3.jar (910 kB at 2.6 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/io/micrometer/micrometer-observation/1.14.5/micrometer-observation-1.14.5.jar
Progress (3): 0.7/1.4 MB | 622/909 kB | 58 kB
Progress (3): 0.7/1.4 MB | 638/909 kB | 58 kB
Progress (3): 0.7/1.4 MB | 655/909 kB | 58 kB
Progress (3): 0.7/1.4 MB | 655/909 kB | 58 kB
Progress (3): 0.7/1.4 MB | 671/909 kB | 58 kB
Progress (3): 0.7/1.4 MB | 671/909 kB | 58 kB
Progress (3): 0.7/1.4 MB | 688/909 kB | 58 kB
Progress (3): 0.7/1.4 MB | 688/909 kB | 58 kB
Progress (3): 0.7/1.4 MB | 704/909 kB | 58 kB
Progress (3): 0.7/1.4 MB | 704/909 kB | 58 kB
Progress (3): 0.7/1.4 MB | 720/909 kB | 58 kB
Progress (3): 0.7/1.4 MB | 720/909 kB | 58 kB
Progress (3): 0.7/1.4 MB | 737/909 kB | 58 kB
Progress (3): 0.8/1.4 MB | 737/909 kB | 58 kB
Progress (3): 0.8/1.4 MB | 753/909 kB | 58 kB
Progress (3): 0.8/1.4 MB | 753/909 kB | 58 kB
Progress (3): 0.8/1.4 MB | 769/909 kB | 58 kB
Progress (3): 0.8/1.4 MB | 769/909 kB | 58 kB
Progress (3): 0.8/1.4 MB | 786/909 kB | 58 kB
Progress (3): 0.8/1.4 MB | 786/909 kB | 58 kB
Progress (3): 0.8/1.4 MB | 802/909 kB | 58 kB
Progress (3): 0.8/1.4 MB | 802/909 kB | 58 kB
Progress (3): 0.8/1.4 MB | 819/909 kB | 58 kB
Progress (3): 0.8/1.4 MB | 819/909 kB | 58 kB
Progress (3): 0.8/1.4 MB | 835/909 kB | 58 kB
Progress (3): 0.9/1.4 MB | 835/909 kB | 58 kB
Progress (3): 0.9/1.4 MB | 851/909 kB | 58 kB
Progress (3): 0.9/1.4 MB | 851/909 kB | 58 kB
Progress (3): 0.9/1.4 MB | 868/909 kB | 58 kB
Progress (3): 0.9/1.4 MB | 868/909 kB | 58 kB
Progress (3): 0.9/1.4 MB | 884/909 kB | 58 kB
Progress (3): 0.9/1.4 MB | 884/909 kB | 58 kB
Progress (3): 0.9/1.4 MB | 901/909 kB | 58 kB
Progress (3): 0.9/1.4 MB | 901/909 kB | 58 kB
Progress (3): 0.9/1.4 MB | 909 kB | 58 kB    
Progress (3): 0.9/1.4 MB | 909 kB | 58 kB
                                         
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-common-artifact-filters/3.3.2/maven-common-artifact-filters-3.3.2.jar (58 kB at 170 kB/s)
Progress (2): 0.9/1.4 MB | 909 kB
                                 
Downloading from central: https://repo.maven.apache.org/maven2/io/micrometer/micrometer-commons/1.14.5/micrometer-commons-1.14.5.jar
Progress (2): 1.0/1.4 MB | 909 kB
Progress (2): 1.0/1.4 MB | 909 kB
Progress (2): 1.0/1.4 MB | 909 kB
Progress (2): 1.0/1.4 MB | 909 kB
Progress (2): 1.0/1.4 MB | 909 kB
Progress (2): 1.0/1.4 MB | 909 kB
Progress (2): 1.1/1.4 MB | 909 kB
Progress (2): 1.1/1.4 MB | 909 kB
Progress (2): 1.1/1.4 MB | 909 kB
Progress (2): 1.1/1.4 MB | 909 kB
Progress (2): 1.1/1.4 MB | 909 kB
Progress (2): 1.1/1.4 MB | 909 kB
Progress (2): 1.2/1.4 MB | 909 kB
Progress (2): 1.2/1.4 MB | 909 kB
Progress (2): 1.2/1.4 MB | 909 kB
Progress (2): 1.2/1.4 MB | 909 kB
Progress (2): 1.2/1.4 MB | 909 kB
Progress (2): 1.2/1.4 MB | 909 kB
Progress (2): 1.3/1.4 MB | 909 kB
Progress (2): 1.3/1.4 MB | 909 kB
Progress (2): 1.3/1.4 MB | 909 kB
Progress (2): 1.3/1.4 MB | 909 kB
Progress (2): 1.3/1.4 MB | 909 kB
Progress (2): 1.3/1.4 MB | 909 kB
Progress (2): 1.4/1.4 MB | 909 kB
Progress (2): 1.4 MB | 909 kB    
                             
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/httpcomponents/core5/httpcore5/5.3.4/httpcore5-5.3.4.jar (909 kB at 2.6 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-shade-plugin/3.5.0/maven-shade-plugin-3.5.0.jar
Progress (2): 1.4 MB | 16/75 kB
Progress (2): 1.4 MB | 33/75 kB
Progress (2): 1.4 MB | 49/75 kB
Progress (2): 1.4 MB | 66/75 kB
Progress (2): 1.4 MB | 75 kB   
Progress (3): 1.4 MB | 75 kB | 16/48 kB
Progress (3): 1.4 MB | 75 kB | 32/48 kB
Progress (3): 1.4 MB | 75 kB | 48 kB   
                                    
Downloaded from central: https://repo.maven.apache.org/maven2/net/java/dev/jna/jna-platform/5.13.0/jna-platform-5.13.0.jar (1.4 MB at 3.8 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/ow2/asm/asm/9.5/asm-9.5.jar
Downloaded from central: https://repo.maven.apache.org/maven2/io/micrometer/micrometer-observation/1.14.5/micrometer-observation-1.14.5.jar (75 kB at 209 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/ow2/asm/asm-commons/9.5/asm-commons-9.5.jar
Downloaded from central: https://repo.maven.apache.org/maven2/io/micrometer/micrometer-commons/1.14.5/micrometer-commons-1.14.5.jar (48 kB at 133 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/ow2/asm/asm-tree/9.5/asm-tree-9.5.jar
Progress (1): 16/147 kB
Progress (1): 33/147 kB
Progress (1): 49/147 kB
Progress (1): 66/147 kB
Progress (1): 82/147 kB
Progress (1): 98/147 kB
Progress (1): 115/147 kB
Progress (1): 131/147 kB
Progress (1): 147 kB    
Progress (2): 147 kB | 16/72 kB
Progress (3): 147 kB | 16/72 kB | 16/122 kB
Progress (3): 147 kB | 33/72 kB | 16/122 kB
Progress (3): 147 kB | 33/72 kB | 33/122 kB
Progress (3): 147 kB | 49/72 kB | 33/122 kB
Progress (3): 147 kB | 49/72 kB | 49/122 kB
Progress (3): 147 kB | 66/72 kB | 49/122 kB
Progress (3): 147 kB | 66/72 kB | 66/122 kB
Progress (3): 147 kB | 72 kB | 66/122 kB   
Progress (3): 147 kB | 72 kB | 82/122 kB
Progress (3): 147 kB | 72 kB | 98/122 kB
Progress (3): 147 kB | 72 kB | 115/122 kB
Progress (3): 147 kB | 72 kB | 122 kB    
Progress (4): 147 kB | 72 kB | 122 kB | 16/52 kB
Progress (4): 147 kB | 72 kB | 122 kB | 33/52 kB
Progress (4): 147 kB | 72 kB | 122 kB | 49/52 kB
Progress (4): 147 kB | 72 kB | 122 kB | 52 kB   
                                             
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-shade-plugin/3.5.0/maven-shade-plugin-3.5.0.jar (147 kB at 391 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/jdom/jdom2/*******/jdom2-*******.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/ow2/asm/asm-commons/9.5/asm-commons-9.5.jar (72 kB at 193 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-dependency-tree/3.2.1/maven-dependency-tree-3.2.1.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/ow2/asm/asm/9.5/asm-9.5.jar (122 kB at 325 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/eclipse/aether/aether-util/1.0.0.v20140518/aether-util-1.0.0.v20140518.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/ow2/asm/asm-tree/9.5/asm-tree-9.5.jar (52 kB at 139 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/eclipse/aether/aether-api/1.0.0.v20140518/aether-api-1.0.0.v20140518.jar
Progress (1): 16/328 kB
Progress (1): 33/328 kB
Progress (1): 49/328 kB
Progress (1): 66/328 kB
Progress (1): 82/328 kB
Progress (1): 98/328 kB
Progress (2): 98/328 kB | 16/43 kB
Progress (2): 115/328 kB | 16/43 kB
Progress (3): 115/328 kB | 16/43 kB | 16/146 kB
Progress (3): 115/328 kB | 33/43 kB | 16/146 kB
Progress (3): 131/328 kB | 33/43 kB | 16/146 kB
Progress (3): 131/328 kB | 43 kB | 16/146 kB   
Progress (3): 147/328 kB | 43 kB | 16/146 kB
Progress (3): 147/328 kB | 43 kB | 33/146 kB
Progress (3): 164/328 kB | 43 kB | 33/146 kB
Progress (3): 164/328 kB | 43 kB | 49/146 kB
Progress (3): 180/328 kB | 43 kB | 49/146 kB
Progress (3): 180/328 kB | 43 kB | 62/146 kB
Progress (3): 197/328 kB | 43 kB | 62/146 kB
Progress (3): 197/328 kB | 43 kB | 78/146 kB
Progress (3): 213/328 kB | 43 kB | 78/146 kB
Progress (3): 213/328 kB | 43 kB | 95/146 kB
Progress (3): 229/328 kB | 43 kB | 95/146 kB
Progress (4): 229/328 kB | 43 kB | 95/146 kB | 16/136 kB
Progress (4): 246/328 kB | 43 kB | 95/146 kB | 16/136 kB
Progress (4): 246/328 kB | 43 kB | 111/146 kB | 16/136 kB
Progress (4): 262/328 kB | 43 kB | 111/146 kB | 16/136 kB
Progress (4): 262/328 kB | 43 kB | 111/146 kB | 33/136 kB
Progress (4): 262/328 kB | 43 kB | 127/146 kB | 33/136 kB
Progress (4): 279/328 kB | 43 kB | 127/146 kB | 33/136 kB
Progress (4): 279/328 kB | 43 kB | 127/146 kB | 49/136 kB
Progress (4): 295/328 kB | 43 kB | 127/146 kB | 49/136 kB
Progress (4): 295/328 kB | 43 kB | 144/146 kB | 49/136 kB
Progress (4): 295/328 kB | 43 kB | 144/146 kB | 65/136 kB
Progress (4): 295/328 kB | 43 kB | 146 kB | 65/136 kB    
Progress (4): 311/328 kB | 43 kB | 146 kB | 65/136 kB
Progress (4): 311/328 kB | 43 kB | 146 kB | 81/136 kB
Progress (4): 328/328 kB | 43 kB | 146 kB | 81/136 kB
Progress (4): 328 kB | 43 kB | 146 kB | 81/136 kB    
Progress (4): 328 kB | 43 kB | 146 kB | 98/136 kB
Progress (4): 328 kB | 43 kB | 146 kB | 114/136 kB
Progress (4): 328 kB | 43 kB | 146 kB | 130/136 kB
Progress (4): 328 kB | 43 kB | 146 kB | 136 kB    
                                              
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/shared/maven-dependency-tree/3.2.1/maven-dependency-tree-3.2.1.jar (43 kB at 105 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/commons-io/commons-io/2.13.0/commons-io-2.13.0.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/eclipse/aether/aether-util/1.0.0.v20140518/aether-util-1.0.0.v20140518.jar (146 kB at 358 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/vafer/jdependency/2.8.0/jdependency-2.8.0.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/eclipse/aether/aether-api/1.0.0.v20140518/aether-api-1.0.0.v20140518.jar (136 kB at 335 kB/s)
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar
Downloaded from central: https://repo.maven.apache.org/maven2/org/jdom/jdom2/*******/jdom2-*******.jar (328 kB at 805 kB/s)
Progress (1): 16/484 kB
Progress (1): 33/484 kB
Progress (1): 49/484 kB
Progress (1): 65/484 kB
Progress (1): 82/484 kB
Progress (1): 98/484 kB
Progress (1): 114/484 kB
Progress (2): 114/484 kB | 16/233 kB
Progress (2): 131/484 kB | 16/233 kB
Progress (2): 147/484 kB | 16/233 kB
Progress (2): 147/484 kB | 32/233 kB
Progress (2): 163/484 kB | 32/233 kB
Progress (2): 163/484 kB | 49/233 kB
Progress (2): 180/484 kB | 49/233 kB
Progress (2): 180/484 kB | 65/233 kB
Progress (2): 196/484 kB | 65/233 kB
Progress (2): 196/484 kB | 81/233 kB
Progress (2): 213/484 kB | 81/233 kB
Progress (2): 229/484 kB | 81/233 kB
Progress (2): 229/484 kB | 98/233 kB
Progress (2): 245/484 kB | 98/233 kB
Progress (2): 245/484 kB | 114/233 kB
Progress (2): 262/484 kB | 114/233 kB
Progress (2): 262/484 kB | 130/233 kB
Progress (2): 278/484 kB | 130/233 kB
Progress (2): 278/484 kB | 147/233 kB
Progress (2): 295/484 kB | 147/233 kB
Progress (2): 295/484 kB | 163/233 kB
Progress (2): 311/484 kB | 163/233 kB
Progress (2): 311/484 kB | 180/233 kB
Progress (2): 327/484 kB | 180/233 kB
Progress (2): 327/484 kB | 196/233 kB
Progress (2): 331/484 kB | 196/233 kB
Progress (2): 348/484 kB | 196/233 kB
Progress (2): 348/484 kB | 212/233 kB
Progress (2): 364/484 kB | 212/233 kB
Progress (2): 364/484 kB | 229/233 kB
Progress (2): 364/484 kB | 233 kB    
Progress (2): 381/484 kB | 233 kB
Progress (3): 381/484 kB | 233 kB | 16/752 kB
Progress (3): 397/484 kB | 233 kB | 16/752 kB
Progress (3): 413/484 kB | 233 kB | 16/752 kB
Progress (3): 413/484 kB | 233 kB | 33/752 kB
Progress (3): 430/484 kB | 233 kB | 33/752 kB
Progress (3): 430/484 kB | 233 kB | 49/752 kB
Progress (3): 446/484 kB | 233 kB | 49/752 kB
Progress (3): 462/484 kB | 233 kB | 49/752 kB
Progress (3): 479/484 kB | 233 kB | 49/752 kB
Progress (3): 479/484 kB | 233 kB | 64/752 kB
Progress (3): 484 kB | 233 kB | 64/752 kB    
Progress (3): 484 kB | 233 kB | 80/752 kB
Progress (3): 484 kB | 233 kB | 97/752 kB
Progress (3): 484 kB | 233 kB | 113/752 kB
Progress (3): 484 kB | 233 kB | 129/752 kB
Progress (3): 484 kB | 233 kB | 146/752 kB
Progress (3): 484 kB | 233 kB | 162/752 kB
Progress (3): 484 kB | 233 kB | 178/752 kB
Progress (3): 484 kB | 233 kB | 195/752 kB
Progress (3): 484 kB | 233 kB | 211/752 kB
Progress (3): 484 kB | 233 kB | 228/752 kB
Progress (3): 484 kB | 233 kB | 244/752 kB
Progress (3): 484 kB | 233 kB | 260/752 kB
Progress (3): 484 kB | 233 kB | 277/752 kB
Progress (3): 484 kB | 233 kB | 293/752 kB
Progress (3): 484 kB | 233 kB | 310/752 kB
Progress (3): 484 kB | 233 kB | 326/752 kB
Progress (3): 484 kB | 233 kB | 342/752 kB
Progress (3): 484 kB | 233 kB | 359/752 kB
Progress (3): 484 kB | 233 kB | 375/752 kB
Progress (3): 484 kB | 233 kB | 391/752 kB
Progress (3): 484 kB | 233 kB | 408/752 kB
Progress (3): 484 kB | 233 kB | 424/752 kB
Progress (3): 484 kB | 233 kB | 441/752 kB
                                          
Downloaded from central: https://repo.maven.apache.org/maven2/org/vafer/jdependency/2.8.0/jdependency-2.8.0.jar (233 kB at 553 kB/s)
Progress (2): 484 kB | 457/752 kB
Progress (2): 484 kB | 473/752 kB
Progress (2): 484 kB | 490/752 kB
Progress (2): 484 kB | 506/752 kB
Progress (2): 484 kB | 522/752 kB
Progress (2): 484 kB | 539/752 kB
Progress (2): 484 kB | 555/752 kB
Progress (2): 484 kB | 572/752 kB
Progress (2): 484 kB | 588/752 kB
Progress (2): 484 kB | 604/752 kB
Progress (2): 484 kB | 621/752 kB
Progress (2): 484 kB | 637/752 kB
Progress (2): 484 kB | 654/752 kB
Progress (2): 484 kB | 670/752 kB
Progress (2): 484 kB | 686/752 kB
Progress (2): 484 kB | 703/752 kB
Progress (2): 484 kB | 719/752 kB
Progress (2): 484 kB | 735/752 kB
Progress (2): 484 kB | 752/752 kB
Progress (2): 484 kB | 752 kB    
                             
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar (752 kB at 1.7 MB/s)
Downloading from central: https://repo.maven.apache.org/maven2/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar
Downloaded from central: https://repo.maven.apache.org/maven2/commons-io/commons-io/2.13.0/commons-io-2.13.0.jar (484 kB at 1.1 MB/s)
Progress (1): 0/1.9 MB
Progress (1): 0/1.9 MB
Progress (1): 0/1.9 MB
Progress (1): 0.1/1.9 MB
Progress (1): 0.1/1.9 MB
Progress (1): 0.1/1.9 MB
Progress (1): 0.1/1.9 MB
Progress (1): 0.1/1.9 MB
Progress (1): 0.1/1.9 MB
Progress (1): 0.2/1.9 MB
Progress (1): 0.2/1.9 MB
Progress (1): 0.2/1.9 MB
Progress (1): 0.2/1.9 MB
Progress (1): 0.2/1.9 MB
Progress (1): 0.2/1.9 MB
Progress (1): 0.3/1.9 MB
Progress (1): 0.3/1.9 MB
Progress (1): 0.3/1.9 MB
Progress (1): 0.3/1.9 MB
Progress (1): 0.3/1.9 MB
Progress (1): 0.3/1.9 MB
Progress (1): 0.4/1.9 MB
Progress (1): 0.4/1.9 MB
Progress (1): 0.4/1.9 MB
Progress (1): 0.4/1.9 MB
Progress (1): 0.4/1.9 MB
Progress (1): 0.4/1.9 MB
Progress (1): 0.5/1.9 MB
Progress (1): 0.5/1.9 MB
Progress (1): 0.5/1.9 MB
Progress (1): 0.5/1.9 MB
Progress (1): 0.5/1.9 MB
Progress (1): 0.5/1.9 MB
Progress (1): 0.6/1.9 MB
Progress (1): 0.6/1.9 MB
Progress (1): 0.6/1.9 MB
Progress (1): 0.6/1.9 MB
Progress (1): 0.6/1.9 MB
Progress (1): 0.6/1.9 MB
Progress (1): 0.7/1.9 MB
Progress (1): 0.7/1.9 MB
Progress (1): 0.7/1.9 MB
Progress (1): 0.7/1.9 MB
Progress (1): 0.7/1.9 MB
Progress (1): 0.7/1.9 MB
Progress (1): 0.7/1.9 MB
Progress (1): 0.8/1.9 MB
Progress (1): 0.8/1.9 MB
Progress (1): 0.8/1.9 MB
Progress (1): 0.8/1.9 MB
Progress (1): 0.8/1.9 MB
Progress (1): 0.8/1.9 MB
Progress (1): 0.9/1.9 MB
Progress (1): 0.9/1.9 MB
Progress (1): 0.9/1.9 MB
Progress (1): 0.9/1.9 MB
Progress (1): 0.9/1.9 MB
Progress (1): 0.9/1.9 MB
Progress (1): 1.0/1.9 MB
Progress (1): 1.0/1.9 MB
Progress (1): 1.0/1.9 MB
Progress (1): 1.0/1.9 MB
Progress (1): 1.0/1.9 MB
Progress (1): 1.0/1.9 MB
Progress (1): 1.1/1.9 MB
Progress (1): 1.1/1.9 MB
Progress (1): 1.1/1.9 MB
Progress (1): 1.1/1.9 MB
Progress (1): 1.1/1.9 MB
Progress (1): 1.1/1.9 MB
Progress (1): 1.2/1.9 MB
Progress (1): 1.2/1.9 MB
Progress (1): 1.2/1.9 MB
Progress (1): 1.2/1.9 MB
Progress (1): 1.2/1.9 MB
Progress (1): 1.2/1.9 MB
Progress (1): 1.3/1.9 MB
Progress (1): 1.3/1.9 MB
Progress (1): 1.3/1.9 MB
Progress (1): 1.3/1.9 MB
Progress (1): 1.3/1.9 MB
Progress (1): 1.3/1.9 MB
Progress (1): 1.4/1.9 MB
Progress (1): 1.4/1.9 MB
Progress (1): 1.4/1.9 MB
Progress (1): 1.4/1.9 MB
Progress (1): 1.4/1.9 MB
Progress (1): 1.4/1.9 MB
Progress (1): 1.4/1.9 MB
Progress (1): 1.5/1.9 MB
Progress (1): 1.5/1.9 MB
Progress (1): 1.5/1.9 MB
Progress (1): 1.5/1.9 MB
Progress (1): 1.5/1.9 MB
Progress (1): 1.5/1.9 MB
Progress (1): 1.6/1.9 MB
Progress (1): 1.6/1.9 MB
Progress (1): 1.6/1.9 MB
Progress (1): 1.6/1.9 MB
Progress (1): 1.6/1.9 MB
Progress (1): 1.6/1.9 MB
Progress (1): 1.7/1.9 MB
Progress (1): 1.7/1.9 MB
Progress (1): 1.7/1.9 MB
Progress (1): 1.7/1.9 MB
Progress (1): 1.7/1.9 MB
Progress (1): 1.7/1.9 MB
Progress (1): 1.8/1.9 MB
Progress (1): 1.8/1.9 MB
Progress (1): 1.8/1.9 MB
Progress (1): 1.8/1.9 MB
Progress (1): 1.8/1.9 MB
Progress (1): 1.8/1.9 MB
Progress (1): 1.9/1.9 MB
Progress (1): 1.9/1.9 MB
Progress (1): 1.9 MB    
                    
Downloaded from central: https://repo.maven.apache.org/maven2/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar (1.9 MB at 3.6 MB/s)
[INFO] Replacing main artifact C:\esocial-novo\target\esocial-novo-0.0.1-SNAPSHOT.jar with repackaged archive, adding nested dependencies in BOOT-INF/.
[INFO] The original artifact has been renamed to C:\esocial-novo\target\esocial-novo-0.0.1-SNAPSHOT.jar.original
[INFO] ------------------------------------------------------------------------
[INFO] BUILD SUCCESS
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  01:07 min
[INFO] Finished at: 2025-06-16T13:45:31-03:00
[INFO] ------------------------------------------------------------------------

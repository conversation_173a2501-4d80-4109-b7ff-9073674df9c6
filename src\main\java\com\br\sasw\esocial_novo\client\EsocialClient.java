package com.br.sasw.esocial_novo.client;

import br.gov.esocial.servicos.empregador.lote.eventos.envio.v1_1_0.EnviarLoteEventos;
import br.gov.esocial.servicos.empregador.lote.eventos.envio.v1_1_0.EnviarLoteEventosResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;
import org.springframework.ws.soap.client.core.SoapActionCallback;
import org.w3c.dom.Document;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;

@Service
public class EsocialClient extends WebServiceGatewaySupport {

    @Autowired
    WebServiceTemplate webServiceTemplate;

    public EnviarLoteEventosResponse enviarLoteEventos(String xmlLoteEventos) {
        EnviarLoteEventos request = new EnviarLoteEventos();
        
        // Cria o elemento any com o XML do lote de eventos
        org.w3c.dom.Element xmlElement = criarElementoXml(xmlLoteEventos);
        request.setLoteEventos(new EnviarLoteEventos.LoteEventos());
        request.getLoteEventos().setAny(xmlElement);
        
        String soapAction = "http://www.esocial.gov.br/servicos/empregador/lote/eventos/envio/v1_1_0/ServicoEnviarLoteEventos/EnviarLoteEventos";
        
        return (EnviarLoteEventosResponse) webServiceTemplate
                .marshalSendAndReceive(
                        "https://webservices.envio.esocial.gov.br/servicos/empregador/enviarloteeventos/WsEnviarLoteEventos.svc",
                        request,
                        new SoapActionCallback(soapAction)
                );
    }
    
    private org.w3c.dom.Element criarElementoXml(String xmlString) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlString)));
            return document.getDocumentElement();
        } catch (Exception e) {
            throw new RuntimeException("Erro ao criar elemento XML", e);
        }
    }
}
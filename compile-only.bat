@echo off
setlocal enabledelayedexpansion

echo ========================================
echo  eSocial Novo - Compilacao Apenas
echo ========================================

REM Configuracao do Java local
REM Modifique o caminho abaixo para apontar para sua instalacao local do Java 17
set "LOCAL_JAVA_HOME=C:\java\jdk-17"

REM Verifica se o diretorio do Java existe
if not exist "%LOCAL_JAVA_HOME%" (
    echo ERRO: Diretorio do Java nao encontrado: %LOCAL_JAVA_HOME%
    echo.
    echo Por favor, edite este script e configure o LOCAL_JAVA_HOME
    echo para apontar para sua instalacao local do Java 17.
    echo.
    pause
    exit /b 1
)

REM Configura as variaveis de ambiente para usar o Java local
set "JAVA_HOME=%LOCAL_JAVA_HOME%"
set "PATH=%LOCAL_JAVA_HOME%\bin;%PATH%"

echo Usando Java: %JAVA_HOME%
echo.

REM Verifica a versao do Java
echo Verificando versao do Java...
"%JAVA_HOME%\bin\java.exe" -version
echo.

REM Limpa diretorio target se existir
if exist "target" (
    echo Limpando diretorio target...
    rmdir /s /q "target" 2>nul
    timeout /t 2 /nobreak >nul
)

REM Compila a aplicacao
echo ========================================
echo  Compilando a aplicacao...
echo ========================================

REM Primeira tentativa com mvnw
echo Tentativa 1: Usando Maven Wrapper...
call mvnw.cmd clean package -DskipTests -Dmaven.repo.local=.m2\repository

if %ERRORLEVEL% neq 0 (
    echo.
    echo Maven Wrapper falhou. Tentando Maven do sistema...

    REM Verifica se Maven esta instalado no sistema
    mvn -version >nul 2>&1
    if %ERRORLEVEL% equ 0 (
        echo Tentativa 2: Usando Maven do sistema...
        mvn clean package -DskipTests -Dmaven.repo.local=.m2\repository

        if %ERRORLEVEL% neq 0 (
            echo.
            echo ERRO: Falha na compilacao!
            echo Execute como Administrador ou use run-app-portable.bat
            pause
            exit /b 1
        )
    ) else (
        echo.
        echo ERRO: Maven nao encontrado!
        echo Use run-app-portable.bat ou instale Maven
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo  Compilacao concluida com sucesso!
echo ========================================

REM Mostra informacoes do JAR gerado
echo.
echo JARs gerados:
dir target\*.jar /b 2>nul

echo.
echo Para executar a aplicacao, use: run-app.bat
echo.
pause

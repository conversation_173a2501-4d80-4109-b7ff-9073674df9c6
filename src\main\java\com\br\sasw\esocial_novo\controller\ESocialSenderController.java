package com.br.sasw.esocial_novo.controller;

import br.gov.esocial.servicos.empregador.lote.eventos.envio.v1_1_0.EnviarLoteEventosResponse;
import com.br.sasw.esocial_novo.client.EsocialClient;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Collections;

@RestController
@RequiredArgsConstructor
public class ESocialSenderController {

    private final EsocialClient esocialClient;

    @PostMapping(path = "/envio-esocial")
    private ResponseEntity<String> send(){
        EnviarLoteEventosResponse response = null;
        ClassLoader classloader = Thread.currentThread().getContextClassLoader();
        try(InputStream is = classloader.getResourceAsStream("s2200-assinado.xml")) {
            response = esocialClient.enviarLoteEventos(buildEvent(new String(is.readAllBytes(), StandardCharsets.UTF_8)));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return ResponseEntity.ok().body(response.getEnviarLoteEventosResult().getAny().toString());
    }


    private String buildEvent(String xml){
        String evento =
               """
                <eSocial xmlns="http://www.esocial.gov.br/schema/lote/eventos/envio/v1_1_1">
                    <envioLoteEventos grupo="2">
                        <ideEmpregador>
                            <tpInsc>1</tpInsc>
                            <nrInsc>00914803</nrInsc>
                        </ideEmpregador>
                        <ideTransmissor>
                            <tpInsc>1</tpInsc>
                            <nrInsc>00914803000151</nrInsc>
                        </ideTransmissor>
                        <eventos>
                            <evento Id="ID1009148030000002025051410352900004">${evento}</evento>
                        </eventos>
                </envioLoteEventos>
               </eSocial>
               """;
        evento = evento.replace("${evento}", xml);
        return evento;
    }
}

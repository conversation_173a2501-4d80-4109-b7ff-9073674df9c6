@echo off
setlocal enabledelayedexpansion

echo ========================================
echo  eSocial Novo - Versao Robusta
echo ========================================

REM Configuracao do Java local
set "LOCAL_JAVA_HOME=C:\Program Files\Java\jdk-17.0.15+6"
set "MAVEN_HOME=%~dp0tools\maven"

REM Configura as variaveis de ambiente
set "JAVA_HOME=%LOCAL_JAVA_HOME%"
set "PATH=%LOCAL_JAVA_HOME%\bin;%MAVEN_HOME%\bin;%PATH%"
set "M2_HOME=%MAVEN_HOME%"

echo Usando Java: %JAVA_HOME%
echo Usando Maven: %MAVEN_HOME%
echo.

REM Verifica arquivos necessarios
if not exist "pom.xml" (
    echo ERRO: pom.xml nao encontrado!
    pause
    exit /b 1
)

if not exist "src\main\resources\wsdl\WsEnviarLoteEventos.xsd" (
    echo ERRO: Arquivo XSD nao encontrado!
    echo Esperado: src\main\resources\wsdl\WsEnviarLoteEventos.xsd
    pause
    exit /b 1
)

REM Limpa completamente o projeto
echo ========================================
echo  Limpando projeto...
echo ========================================
if exist "target" (
    rmdir /s /q "target" 2>nul
    timeout /t 2 /nobreak >nul
)

if exist ".m2\repository" (
    echo Limpando cache Maven local...
    rmdir /s /q ".m2\repository" 2>nul
)

REM Cria diretorio .m2 local
if not exist ".m2" mkdir ".m2"

echo.

REM Etapa 1: Validacao
echo ========================================
echo  Etapa 1: Validando projeto...
echo ========================================
"%MAVEN_HOME%\bin\mvn.cmd" validate -Dmaven.repo.local=.m2\repository
if %ERRORLEVEL% neq 0 (
    echo ERRO na validacao!
    pause
    exit /b 1
)
echo [OK] Validacao concluida
echo.

REM Etapa 2: Limpeza
echo ========================================
echo  Etapa 2: Limpeza...
echo ========================================
"%MAVEN_HOME%\bin\mvn.cmd" clean -Dmaven.repo.local=.m2\repository
if %ERRORLEVEL% neq 0 (
    echo ERRO na limpeza!
    pause
    exit /b 1
)
echo [OK] Limpeza concluida
echo.

REM Etapa 3: Geracao de codigo
echo ========================================
echo  Etapa 3: Gerando codigo JAXB...
echo ========================================
"%MAVEN_HOME%\bin\mvn.cmd" generate-sources -Dmaven.repo.local=.m2\repository
if %ERRORLEVEL% neq 0 (
    echo ERRO na geracao de codigo!
    echo.
    echo Tentando com configuracao alternativa...
    "%MAVEN_HOME%\bin\mvn.cmd" jaxb2:xjc -Dmaven.repo.local=.m2\repository
    if %ERRORLEVEL% neq 0 (
        echo ERRO: Falha na geracao de codigo JAXB!
        pause
        exit /b 1
    )
)
echo [OK] Codigo JAXB gerado
echo.

REM Verifica se codigo foi gerado
if exist "target\generated-sources\jaxb" (
    echo Codigo gerado em: target\generated-sources\jaxb
    dir "target\generated-sources\jaxb" /s /b | find ".java" >nul
    if %ERRORLEVEL% equ 0 (
        echo [OK] Arquivos Java encontrados
    ) else (
        echo [AVISO] Nenhum arquivo Java gerado
    )
) else (
    echo [AVISO] Diretorio de codigo gerado nao encontrado
)
echo.

REM Etapa 4: Compilacao
echo ========================================
echo  Etapa 4: Compilando...
echo ========================================
"%MAVEN_HOME%\bin\mvn.cmd" compile -Dmaven.repo.local=.m2\repository
if %ERRORLEVEL% neq 0 (
    echo ERRO na compilacao!
    echo.
    echo Tentando baixar dependencias primeiro...
    "%MAVEN_HOME%\bin\mvn.cmd" dependency:resolve -Dmaven.repo.local=.m2\repository
    echo.
    echo Tentando compilar novamente...
    "%MAVEN_HOME%\bin\mvn.cmd" compile -Dmaven.repo.local=.m2\repository
    if %ERRORLEVEL% neq 0 (
        echo ERRO: Falha na compilacao!
        pause
        exit /b 1
    )
)
echo [OK] Compilacao concluida
echo.

REM Etapa 5: Empacotamento
echo ========================================
echo  Etapa 5: Empacotando...
echo ========================================
"%MAVEN_HOME%\bin\mvn.cmd" package -DskipTests -Dmaven.repo.local=.m2\repository
if %ERRORLEVEL% neq 0 (
    echo ERRO no empacotamento!
    pause
    exit /b 1
)
echo [OK] Empacotamento concluido
echo.

REM Encontra o JAR gerado
set "JAR_FILE="
for %%f in (target\*.jar) do (
    if not "%%~nf"=="%%~nf.original" (
        set "JAR_FILE=%%f"
    )
)

if "%JAR_FILE%"=="" (
    echo ERRO: JAR nao encontrado!
    echo.
    echo JARs no diretorio target:
    dir "target\*.jar" /b 2>nul
    pause
    exit /b 1
)

echo ========================================
echo  Compilacao bem-sucedida!
echo ========================================
echo JAR gerado: %JAR_FILE%
echo.

REM Executa a aplicacao
echo ========================================
echo  Iniciando aplicacao...
echo ========================================
echo.
echo Para parar a aplicacao, pressione Ctrl+C
echo.

"%JAVA_HOME%\bin\java.exe" -jar "%JAR_FILE%"

echo.
echo Aplicacao finalizada
pause

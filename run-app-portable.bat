@echo off
setlocal enabledelayedexpansion

echo ========================================
echo  eSocial Novo - Versao Portatil
echo ========================================

REM Configuracao do Java local
set "LOCAL_JAVA_HOME=C:\java\jdk-17"

REM Configuracao do Maven portatil
set "MAVEN_HOME=%~dp0tools\maven"
set "MAVEN_VERSION=3.9.9"

REM Verifica se o diretorio do Java existe
if not exist "%LOCAL_JAVA_HOME%" (
    echo ERRO: Diretorio do Java nao encontrado: %LOCAL_JAVA_HOME%
    echo.
    echo Por favor, edite este script e configure o LOCAL_JAVA_HOME
    echo.
    pause
    exit /b 1
)

REM Configura as variaveis de ambiente
set "JAVA_HOME=%LOCAL_JAVA_HOME%"
set "PATH=%LOCAL_JAVA_HOME%\bin;%PATH%"

echo Usando Java: %JAVA_HOME%
"%JAVA_HOME%\bin\java.exe" -version
echo.

REM Verifica se Maven portatil existe
if not exist "%MAVEN_HOME%\bin\mvn.cmd" (
    echo Maven portatil nao encontrado. Baixando...
    echo.
    
    REM Cria diretorio tools se nao existir
    if not exist "tools" mkdir "tools"
    
    REM URL do Maven
    set "MAVEN_URL=https://archive.apache.org/dist/maven/maven-3/%MAVEN_VERSION%/binaries/apache-maven-%MAVEN_VERSION%-bin.zip"
    set "MAVEN_ZIP=tools\apache-maven-%MAVEN_VERSION%-bin.zip"
    
    echo Baixando Maven %MAVEN_VERSION%...
    echo Isso pode demorar alguns minutos...
    
    REM Usa PowerShell para baixar
    powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri '%MAVEN_URL%' -OutFile '%MAVEN_ZIP%'}"
    
    if not exist "%MAVEN_ZIP%" (
        echo ERRO: Falha ao baixar Maven!
        echo Tente baixar manualmente de: %MAVEN_URL%
        pause
        exit /b 1
    )
    
    echo Extraindo Maven...
    powershell -Command "Expand-Archive -Path '%MAVEN_ZIP%' -DestinationPath 'tools\' -Force"
    
    REM Renomeia diretorio
    if exist "tools\apache-maven-%MAVEN_VERSION%" (
        move "tools\apache-maven-%MAVEN_VERSION%" "tools\maven" >nul
    )
    
    REM Remove arquivo zip
    del "%MAVEN_ZIP%" >nul 2>&1
    
    if not exist "%MAVEN_HOME%\bin\mvn.cmd" (
        echo ERRO: Falha ao configurar Maven portatil!
        pause
        exit /b 1
    )
    
    echo Maven portatil configurado com sucesso!
    echo.
)

REM Configura Maven no PATH
set "PATH=%MAVEN_HOME%\bin;%PATH%"
set "M2_HOME=%MAVEN_HOME%"

echo Usando Maven: %MAVEN_HOME%
"%MAVEN_HOME%\bin\mvn.cmd" -version
echo.

REM Limpa diretorio target
if exist "target" (
    echo Limpando compilacao anterior...
    rmdir /s /q "target" 2>nul
)

REM Compila a aplicacao
echo ========================================
echo  Compilando a aplicacao...
echo ========================================

echo Executando: "%MAVEN_HOME%\bin\mvn.cmd" clean package -DskipTests
echo.

"%MAVEN_HOME%\bin\mvn.cmd" clean package -DskipTests

set "COMPILE_RESULT=%ERRORLEVEL%"
echo.
echo Codigo de retorno da compilacao: %COMPILE_RESULT%

if %COMPILE_RESULT% neq 0 (
    echo.
    echo ========================================
    echo  ERRO: Falha na compilacao!
    echo ========================================
    echo.
    echo Possiveis causas:
    echo 1. Problemas de conectividade com repositorios Maven
    echo 2. Dependencias nao encontradas
    echo 3. Erros no codigo fonte
    echo 4. Problemas com geracao de codigo JAXB
    echo.
    echo Tente executar manualmente:
    echo "%MAVEN_HOME%\bin\mvn.cmd" clean compile
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo  Compilacao concluida!
echo ========================================

REM Encontra o JAR gerado
set "JAR_FILE="
for %%f in (target\*.jar) do (
    if not "%%~nf"=="%%~nf.original" (
        set "JAR_FILE=%%f"
    )
)

if "%JAR_FILE%"=="" (
    echo ERRO: JAR nao encontrado!
    pause
    exit /b 1
)

echo JAR encontrado: %JAR_FILE%
echo.

REM Executa a aplicacao
echo ========================================
echo  Iniciando a aplicacao...
echo ========================================
echo.
echo Para parar a aplicacao, pressione Ctrl+C
echo.

"%JAVA_HOME%\bin\java.exe" -jar "%JAR_FILE%"

echo.
echo Aplicacao finalizada
pause

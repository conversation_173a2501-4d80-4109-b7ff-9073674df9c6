# Scripts para Execução da Aplicação eSocial Novo

Este conjunto de scripts permite compilar e executar a aplicação usando uma instalação local do Java, evitando conflitos com outras aplicações do sistema.

## Pré-requisitos

- Java 17 instalado localmente (não precisa estar no PATH do sistema)
- Maven (o projeto já inclui o Maven Wrapper - mvnw.cmd)

## Scripts Disponíveis

### 1. `config-java.bat` - Configurador do Java Local
**Use este script primeiro para identificar e testar sua instalação do Java.**

- Mostra locais comuns onde o Java pode estar instalado
- Permite testar se uma instalação do Java está funcionando
- Fornece instruções de como configurar os outros scripts

**Como usar:**
```cmd
config-java.bat
```

### 2. `run-app.bat` - Script Principal
**Script completo que compila e executa a aplicação.**

- Compila a aplicação usando Maven
- Executa o JAR gerado
- Usa Java local (não interfere com outras instalações)

**Como usar:**
1. Edite o script e configure o caminho do Java:
   ```batch
   set "LOCAL_JAVA_HOME=C:\seu\caminho\para\java\jdk-17"
   ```
2. Execute o script:
   ```cmd
   run-app.bat
   ```

### 3. `compile-only.bat` - Apenas Compilação
**Script para compilar a aplicação sem executá-la.**

Útil durante o desenvolvimento quando você só quer verificar se o código compila.

**Como usar:**
1. Configure o caminho do Java (mesmo que no run-app.bat)
2. Execute:
   ```cmd
   compile-only.bat
   ```

## Configuração Inicial

### Passo 1: Identifique sua instalação do Java 17
Execute o script de configuração:
```cmd
config-java.bat
```

### Passo 2: Configure os scripts
Edite os arquivos `run-app.bat` e `compile-only.bat` e modifique a linha:
```batch
set "LOCAL_JAVA_HOME=C:\java\jdk-17"
```

Substitua pelo caminho real da sua instalação do Java 17.

**Exemplos de caminhos comuns:**
- `C:\Program Files\Java\jdk-17.0.9`
- `C:\Program Files\Eclipse Adoptium\jdk-********-hotspot`
- `C:\java\jdk-17`
- `C:\tools\java\jdk-17`

### Passo 3: Teste a configuração
Execute o script principal:
```cmd
run-app.bat
```

## Características dos Scripts

### Verificações de Segurança
- Verificam se o Java existe no caminho especificado
- Validam se o java.exe está presente
- Mostram a versão do Java sendo usada
- Verificam se a compilação foi bem-sucedida

### Isolamento do Java
- Configuram `JAVA_HOME` temporariamente
- Modificam o `PATH` apenas durante a execução
- Não afetam outras aplicações do sistema

### Tratamento de Erros
- Mensagens claras em caso de erro
- Pausam para permitir leitura das mensagens
- Códigos de saída apropriados

## Solução de Problemas

### Erro: "Diretório do Java não encontrado"
- Verifique se o caminho está correto
- Use o script `config-java.bat` para encontrar instalações
- Certifique-se de que é Java 17 ou superior

### Erro: "java.exe não encontrado"
- O caminho pode estar apontando para um diretório incorreto
- Certifique-se de que aponta para a raiz da instalação do JDK

### Erro: "Falha na compilação"
- Verifique se todas as dependências estão disponíveis
- Execute `mvnw.cmd clean` manualmente para limpar o projeto
- Verifique se há problemas de conectividade com repositórios Maven

### A aplicação não inicia
- Verifique se a compilação foi bem-sucedida
- Confirme se o JAR foi gerado no diretório `target/`
- Verifique os logs de erro na saída do console

## Informações Técnicas

- **Versão do Java requerida:** 17+
- **Tipo de aplicação:** Spring Boot
- **Classe principal:** `com.br.sasw.esocial_novo.EsocialNovoApplication`
- **Porta padrão:** 8080 (Spring Boot padrão)
- **Perfil Maven:** Usa o Spring Boot Maven Plugin para gerar JAR executável

## Notas Importantes

1. Os scripts não modificam configurações globais do sistema
2. Cada execução usa um ambiente isolado
3. É seguro ter múltiplas versões do Java no sistema
4. Os scripts podem ser personalizados conforme necessário
5. Para parar a aplicação, use `Ctrl+C` no console
